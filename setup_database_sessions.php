<?php
/**
 * Setup Database Sessions
 */

echo "<h1>🗄️ Setting Up Database Sessions</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;} .info{background:#e7f3ff;padding:15px;margin:10px 0;border-left:4px solid #2196F3;}</style>";

try {
    // Define FCPATH if not already defined
    if (!defined('FCPATH')) {
        define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
    }
    
    // Change to correct directory
    if (getcwd() . DIRECTORY_SEPARATOR !== FCPATH) {
        chdir(FCPATH);
    }
    
    // Load paths
    require FCPATH . 'app/Config/Paths.php';
    $paths = new Config\Paths();
    
    // Load autoloader
    require_once $paths->systemDirectory . '/../autoload.php';
    
    // Load database configuration
    require_once $paths->appDirectory . '/Config/Database.php';
    $dbConfig = new Config\Database();
    
    echo "<div class='info'>";
    echo "<h2>📊 Database Configuration</h2>";
    echo "Database: " . $dbConfig->default['database'] . "<br>";
    echo "Username: " . $dbConfig->default['username'] . "<br>";
    echo "Hostname: " . $dbConfig->default['hostname'] . "<br>";
    echo "</div>";
    
    // Connect to database
    $db = new mysqli(
        $dbConfig->default['hostname'],
        $dbConfig->default['username'],
        $dbConfig->default['password'],
        $dbConfig->default['database']
    );
    
    if ($db->connect_error) {
        throw new Exception("Database connection failed: " . $db->connect_error);
    }
    
    echo "<div class='success'>";
    echo "<h2>✅ Database Connected Successfully</h2>";
    echo "</div>";
    
    // Check if sessions table exists
    $result = $db->query("SHOW TABLES LIKE 'ci_sessions'");
    
    if ($result->num_rows > 0) {
        echo "<div class='info'>";
        echo "<h2>📋 Sessions Table Already Exists</h2>";
        
        // Show table structure
        $structure = $db->query("DESCRIBE ci_sessions");
        echo "<table border='1' style='border-collapse:collapse;margin:10px 0;'>";
        echo "<tr><th>Field</th><th>Type</th><th>Null</th><th>Key</th></tr>";
        while ($row = $structure->fetch_assoc()) {
            echo "<tr>";
            echo "<td>" . $row['Field'] . "</td>";
            echo "<td>" . $row['Type'] . "</td>";
            echo "<td>" . $row['Null'] . "</td>";
            echo "<td>" . $row['Key'] . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        echo "</div>";
        
        // Count existing sessions
        $count = $db->query("SELECT COUNT(*) as count FROM ci_sessions");
        $countRow = $count->fetch_assoc();
        echo "<div class='info'>";
        echo "<h2>📊 Current Sessions: " . $countRow['count'] . "</h2>";
        echo "</div>";
        
    } else {
        echo "<div class='info'>";
        echo "<h2>🔨 Creating Sessions Table</h2>";
        echo "</div>";
        
        // Create sessions table
        $sql = "CREATE TABLE ci_sessions (
            id varchar(128) NOT NULL,
            ip_address varchar(45) NOT NULL,
            timestamp timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
            data blob NOT NULL,
            KEY ci_sessions_timestamp (timestamp)
        )";
        
        if ($db->query($sql)) {
            echo "<div class='success'>";
            echo "<h2>✅ Sessions Table Created Successfully!</h2>";
            echo "<p>Table: ci_sessions</p>";
            echo "<p>Columns: id, ip_address, timestamp, data</p>";
            echo "</div>";
        } else {
            throw new Exception("Error creating table: " . $db->error);
        }
    }
    
    // Test session functionality
    echo "<div class='info'>";
    echo "<h2>🧪 Testing Session Functionality</h2>";
    echo "</div>";
    
    // Try to insert a test session
    $testId = 'test_' . time();
    $testIp = '127.0.0.1';
    $testData = 'test_data';
    
    $stmt = $db->prepare("INSERT INTO ci_sessions (id, ip_address, data) VALUES (?, ?, ?)");
    $stmt->bind_param("sss", $testId, $testIp, $testData);
    
    if ($stmt->execute()) {
        echo "<div class='success'>";
        echo "<h2>✅ Test Session Created</h2>";
        echo "Session ID: $testId<br>";
        echo "</div>";
        
        // Clean up test session
        $db->query("DELETE FROM ci_sessions WHERE id = '$testId'");
        echo "<div class='success'>";
        echo "<h2>✅ Test Session Cleaned Up</h2>";
        echo "</div>";
    } else {
        throw new Exception("Error testing session: " . $stmt->error);
    }
    
    $db->close();
    
    echo "<div class='success'>";
    echo "<h2>🎉 Database Sessions Setup Complete!</h2>";
    echo "<p><strong>Your site should now work without any session errors!</strong></p>";
    echo "<p>Sessions will be stored in the database instead of files.</p>";
    echo "<p><a href='../login'>🔐 Test Login Page</a></p>";
    echo "<p><a href='../'>🏠 Visit Main Site</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ Error: " . $e->getMessage() . "</h2>";
    echo "</div>";
}

?>

<script>
// Auto-redirect to test the site
setTimeout(function() {
    if (confirm('Database sessions setup complete! Test the login page now?')) {
        window.location.href = '../login';
    }
}, 5000);
</script>
