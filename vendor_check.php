<?php
echo "<h1>Vendor Directory Check</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .info{background:#e7f3ff;padding:15px;margin:10px 0;border-left:4px solid #2196F3;}</style>";

echo "<div class='info'>";
echo "<h2>Vendor Directory Contents</h2>";

if (file_exists('vendor')) {
    echo "✅ vendor directory exists<br><br>";
    
    // Check autoload.php
    if (file_exists('vendor/autoload.php')) {
        $size = filesize('vendor/autoload.php');
        echo "✅ vendor/autoload.php exists (Size: $size bytes)<br>";
    } else {
        echo "❌ vendor/autoload.php MISSING<br>";
    }
    
    // List vendor contents
    echo "<br><strong>Vendor directory contents:</strong><br>";
    $vendor_contents = scandir('vendor');
    foreach ($vendor_contents as $item) {
        if ($item != '.' && $item != '..') {
            $type = is_dir('vendor/' . $item) ? '[DIR]' : '[FILE]';
            echo "$type $item<br>";
        }
    }
    
    // Check CodeIgniter specifically
    echo "<br><strong>CodeIgniter Framework Check:</strong><br>";
    $ci_path = 'vendor/codeigniter4/framework/system/Boot.php';
    if (file_exists($ci_path)) {
        $boot_size = filesize($ci_path);
        echo "✅ CodeIgniter Boot.php exists (Size: $boot_size bytes)<br>";
        
        // Try to include autoload and see if it works
        echo "<br><strong>Autoload Test:</strong><br>";
        try {
            require_once 'vendor/autoload.php';
            echo "✅ Autoload included successfully<br>";
            
            // Check if CodeIgniter classes are available
            if (class_exists('CodeIgniter\\Boot')) {
                echo "✅ CodeIgniter\\Boot class is available<br>";
            } else {
                echo "❌ CodeIgniter\\Boot class not found<br>";
            }
        } catch (Exception $e) {
            echo "❌ Autoload error: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ CodeIgniter Boot.php missing at: $ci_path<br>";
    }
    
} else {
    echo "❌ vendor directory does not exist<br>";
}

echo "</div>";
?>
