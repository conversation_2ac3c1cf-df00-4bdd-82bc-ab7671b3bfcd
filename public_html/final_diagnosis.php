<?php
/**
 * Final Diagnosis for CodeIgniter 4 Site Issues
 */

echo "<h1>🔍 Final Site Diagnosis</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .info{background:#e7f3ff;padding:15px;margin:10px 0;border-left:4px solid #2196F3;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;}</style>";

// Test 1: Environment Check
echo "<div class='info'>";
echo "<h2>1. Environment Status</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current Directory: " . getcwd() . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "</div>";

// Test 2: Critical Files Check
echo "<div class='info'>";
echo "<h2>2. Critical Files Check</h2>";

$critical_files = [
    'index.php' => 'Main entry point',
    'app/Config/Paths.php' => 'Path configuration',
    'vendor/autoload.php' => 'Composer autoloader',
    'vendor/codeigniter4/framework/system/Boot.php' => 'CodeIgniter Boot class',
    '.env' => 'Environment configuration'
];

foreach ($critical_files as $file => $description) {
    $exists = file_exists($file);
    $status = $exists ? "✅" : "❌";
    echo "$status <strong>$file</strong> - $description<br>";
}
echo "</div>";

// Test 3: Check what Paths.php says
echo "<div class='info'>";
echo "<h2>3. Path Configuration Analysis</h2>";
if (file_exists('app/Config/Paths.php')) {
    try {
        require_once 'app/Config/Paths.php';
        $paths = new Config\Paths();
        
        echo "✅ Paths.php loaded successfully<br>";
        echo "<strong>System Directory:</strong> " . $paths->systemDirectory . "<br>";
        echo "<strong>App Directory:</strong> " . $paths->appDirectory . "<br>";
        echo "<strong>Writable Directory:</strong> " . $paths->writableDirectory . "<br>";
        
        // Check if Boot.php exists in the configured system directory
        $boot_path = $paths->systemDirectory . '/Boot.php';
        $boot_exists = file_exists($boot_path);
        $boot_status = $boot_exists ? "✅" : "❌";
        echo "$boot_status <strong>Boot.php at:</strong> $boot_path<br>";
        
    } catch (Exception $e) {
        echo "❌ Error loading Paths.php: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ app/Config/Paths.php not found<br>";
}
echo "</div>";

// Test 4: Try to simulate what index.php does
echo "<div class='info'>";
echo "<h2>4. Index.php Simulation Test</h2>";

try {
    // Check if index.php exists and what it contains
    if (file_exists('index.php')) {
        echo "✅ index.php exists<br>";
        
        // Read first few lines to see the structure
        $index_content = file_get_contents('index.php');
        echo "<strong>Index.php size:</strong> " . strlen($index_content) . " bytes<br>";
        
        // Check for key components
        if (strpos($index_content, 'FCPATH') !== false) {
            echo "✅ FCPATH definition found<br>";
        } else {
            echo "❌ FCPATH definition missing<br>";
        }
        
        if (strpos($index_content, 'Paths.php') !== false) {
            echo "✅ Paths.php inclusion found<br>";
        } else {
            echo "❌ Paths.php inclusion missing<br>";
        }
        
        if (strpos($index_content, 'Boot.php') !== false) {
            echo "✅ Boot.php reference found<br>";
        } else {
            echo "❌ Boot.php reference missing<br>";
        }
        
    } else {
        echo "❌ index.php does not exist!<br>";
        echo "<strong>This is likely the main problem!</strong><br>";
    }
    
} catch (Exception $e) {
    echo "❌ Error checking index.php: " . $e->getMessage() . "<br>";
}
echo "</div>";

// Test 5: Try to actually boot CodeIgniter
echo "<div class='info'>";
echo "<h2>5. CodeIgniter Boot Test</h2>";

try {
    // Set up environment like index.php would
    if (!defined('FCPATH')) {
        define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
    }
    
    if (file_exists('app/Config/Paths.php')) {
        require_once 'app/Config/Paths.php';
        $paths = new Config\Paths();
        
        // Try to include the autoloader
        if (file_exists($paths->systemDirectory . '/../autoload.php')) {
            require_once $paths->systemDirectory . '/../autoload.php';
            echo "✅ Autoloader included<br>";
        } else if (file_exists('vendor/autoload.php')) {
            require_once 'vendor/autoload.php';
            echo "✅ Vendor autoloader included<br>";
        }
        
        // Try to include Boot.php
        if (file_exists($paths->systemDirectory . '/Boot.php')) {
            require_once $paths->systemDirectory . '/Boot.php';
            echo "✅ Boot.php included<br>";
            
            // Try to boot
            if (class_exists('CodeIgniter\\Boot')) {
                echo "✅ CodeIgniter\\Boot class available<br>";
                
                try {
                    echo "Attempting to boot CodeIgniter...<br>";
                    $app = CodeIgniter\Boot::bootWeb($paths);
                    echo "✅ <strong>CodeIgniter booted successfully!</strong><br>";
                    echo "App class: " . get_class($app) . "<br>";
                    
                    // This means CodeIgniter can boot - the issue might be elsewhere
                    echo "<div class='success'>";
                    echo "<h3>🎉 CodeIgniter Can Boot Successfully!</h3>";
                    echo "<p>This means the core framework is working. The issue might be:</p>";
                    echo "<ul>";
                    echo "<li>Missing or incorrect index.php</li>";
                    echo "<li>Web server configuration (.htaccess)</li>";
                    echo "<li>Route configuration</li>";
                    echo "<li>Controller issues</li>";
                    echo "</ul>";
                    echo "</div>";
                    
                } catch (Exception $e) {
                    echo "❌ Boot failed: " . $e->getMessage() . "<br>";
                    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
                }
                
            } else {
                echo "❌ CodeIgniter\\Boot class not found<br>";
            }
            
        } else {
            echo "❌ Boot.php not found at: " . $paths->systemDirectory . "/Boot.php<br>";
        }
        
    } else {
        echo "❌ Cannot load Paths.php<br>";
    }
    
} catch (Exception $e) {
    echo "❌ Critical error: " . $e->getMessage() . "<br>";
}

echo "</div>";

// Test 6: Check .htaccess
echo "<div class='info'>";
echo "<h2>6. Web Server Configuration</h2>";
if (file_exists('.htaccess')) {
    echo "✅ .htaccess exists<br>";
    $htaccess_content = file_get_contents('.htaccess');
    echo "Size: " . strlen($htaccess_content) . " bytes<br>";
    
    if (strpos($htaccess_content, 'RewriteEngine') !== false) {
        echo "✅ RewriteEngine found<br>";
    } else {
        echo "❌ RewriteEngine not found<br>";
    }
    
} else {
    echo "❌ .htaccess missing<br>";
    echo "<strong>This could be why the site isn't loading!</strong><br>";
}
echo "</div>";

echo "<div class='success'>";
echo "<h2>🎯 Quick Test Links</h2>";
echo "<p><a href='../'>🏠 Try Main Site</a></p>";
echo "<p><a href='../index.php'>📄 Try index.php directly</a></p>";
echo "</div>";

?>
