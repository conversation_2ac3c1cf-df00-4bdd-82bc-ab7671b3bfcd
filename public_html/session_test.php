<?php
/**
 * Session Configuration Test
 */

echo "<h1>🔧 Session Configuration Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;}</style>";

try {
    // Prevent any output before headers
    ob_start();
    
    // Define FCPATH if not already defined
    if (!defined('FCPATH')) {
        define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
    }
    
    // Change to correct directory
    if (getcwd() . DIRECTORY_SEPARATOR !== FCPATH) {
        chdir(FCPATH);
    }
    
    // Load paths
    require FCPATH . 'app/Config/Paths.php';
    $paths = new Config\Paths();
    
    echo "<div class='success'>";
    echo "<h2>✅ Paths Configuration</h2>";
    echo "System Directory: " . $paths->systemDirectory . "<br>";
    echo "App Directory: " . $paths->appDirectory . "<br>";
    echo "Writable Directory: " . $paths->writableDirectory . "<br>";
    echo "</div>";
    
    // Load autoloader
    require_once $paths->systemDirectory . '/../autoload.php';
    
    echo "<div class='success'>";
    echo "<h2>✅ Autoloader Loaded</h2>";
    echo "</div>";
    
    // Load session configuration
    require_once $paths->appDirectory . '/Config/Session.php';
    $sessionConfig = new Config\Session();
    
    echo "<div class='success'>";
    echo "<h2>✅ Session Configuration</h2>";
    echo "Driver: " . $sessionConfig->driver . "<br>";
    echo "Save Path: " . $sessionConfig->savePath . "<br>";
    echo "Expiration: " . $sessionConfig->expiration . " seconds<br>";
    echo "Time to Update: " . $sessionConfig->timeToUpdate . "<br>";
    echo "Regenerate Destroy: " . ($sessionConfig->regenerateDestroy ? 'true' : 'false') . "<br>";
    echo "</div>";
    
    // Check if session directory exists and is writable
    $sessionPath = $sessionConfig->savePath;
    if (!is_dir($sessionPath)) {
        if (mkdir($sessionPath, 0755, true)) {
            echo "<div class='success'>";
            echo "<h2>✅ Session Directory Created</h2>";
            echo "Path: $sessionPath<br>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<h2>❌ Failed to Create Session Directory</h2>";
            echo "Path: $sessionPath<br>";
            echo "</div>";
        }
    } else {
        echo "<div class='success'>";
        echo "<h2>✅ Session Directory Exists</h2>";
        echo "Path: $sessionPath<br>";
        echo "Writable: " . (is_writable($sessionPath) ? 'Yes' : 'No') . "<br>";
        echo "</div>";
    }
    
    // Try to boot CodeIgniter
    require_once $paths->systemDirectory . '/Boot.php';
    
    echo "<div class='success'>";
    echo "<h2>✅ Boot.php Loaded</h2>";
    echo "</div>";
    
    // Try to create the app
    $app = CodeIgniter\Boot::bootWeb($paths);
    
    echo "<div class='success'>";
    echo "<h2>🎉 SUCCESS! CodeIgniter Booted Successfully!</h2>";
    echo "App class: " . get_class($app) . "<br>";
    echo "<p><strong>Your site should now be working!</strong></p>";
    echo "<p><a href='../'>🏠 Visit Main Site</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ Error: " . $e->getMessage() . "</h2>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "</div>";
}

?>
