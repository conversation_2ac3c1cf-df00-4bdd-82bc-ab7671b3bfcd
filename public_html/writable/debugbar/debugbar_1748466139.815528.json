{"url": "https://halqa.mazharulirfan.com/reports", "method": "GET", "isAJAX": false, "startTime": **********.698117, "totalTime": 94.2, "totalMemory": "1.424", "segmentDuration": 15, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.698656, "duration": 0.0033788681030273438}, {"name": "Required Before Filters", "component": "Timer", "start": **********.702038, "duration": 0.0009138584136962891}, {"name": "Routing", "component": "Timer", "start": **********.702958, "duration": 0.00044989585876464844}, {"name": "Before Filters", "component": "Timer", "start": **********.703491, "duration": 0.0061321258544921875}, {"name": "Controller", "component": "Timer", "start": **********.709626, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.709627, "duration": 0.0003871917724609375}, {"name": "After Filters", "component": "Timer", "start": **********.792101, "duration": 4.8160552978515625e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.792185, "duration": 0.0002009868621826172}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(100 total Queries, 81 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> GET_LOCK(&#039;9ed23a7dc6446a079b4f496139134143&#039;, 300) <strong>AS</strong> ci_session_lock", "trace": [{"file": "SYSTEMPATH/Session/Handlers/Database/MySQLiHandler.php:31", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Session/Handlers/DatabaseHandler.php:117", "function": "        CodeIgniter\\Session\\Handlers\\Database\\MySQLiHandler->lockSession()", "index": "  2    "}, {"function": "        CodeIgniter\\Session\\Handlers\\DatabaseHandler->read()", "file": "[internal function]", "index": "  3    "}, {"file": "SYSTEMPATH/Session/Session.php:912", "function": "        session_start()", "index": "  4    "}, {"file": "SYSTEMPATH/Session/Session.php:243", "function": "        CodeIgniter\\Session\\Session->startSession()", "index": "  5    "}, {"file": "SYSTEMPATH/Config/Services.php:710", "function": "        CodeIgniter\\Session\\Session->start()", "index": "  6    "}, {"file": "SYSTEMPATH/Config/BaseService.php:312", "function": "        CodeIgniter\\Config\\Services::session()", "index": "  7    "}, {"file": "SYSTEMPATH/Config/BaseService.php:251", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": "  8    "}, {"file": "SYSTEMPATH/Config/Services.php:668", "function": "        CodeIgniter\\Config\\BaseService::getSharedInstance()", "index": "  9    "}, {"file": "SYSTEMPATH/Config/BaseService.php:321", "function": "        CodeIgniter\\Config\\Services::session()", "index": " 10    "}, {"file": "SYSTEMPATH/Config/BaseService.php:202", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": " 11    "}, {"file": "SYSTEMPATH/Common.php:1000", "function": "        CodeIgniter\\Config\\BaseService::get()", "index": " 12    "}, {"file": "SYSTEMPATH/Common.php:973", "function": "        service()", "index": " 13    "}, {"file": "APPPATH/Filters/AuthFilter.php:29", "function": "        session()", "index": " 14    "}, {"file": "SYSTEMPATH/Filters/Filters.php:241", "function": "        App\\Filters\\AuthFilter->before()", "index": " 15    "}, {"file": "SYSTEMPATH/Filters/Filters.php:221", "function": "        CodeIgniter\\Filters\\Filters->runBefore()", "index": " 16    "}, {"file": "SYSTEMPATH/CodeIgniter.php:479", "function": "        CodeIgniter\\Filters\\Filters->run()", "index": " 17    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 18    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 19    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 20    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 21    "}], "trace-file": "[internal function]", "qid": "cce9bda5f2cb31570a740a6fac3980e9"}, {"hover": "", "class": "", "duration": "0.7 ms", "sql": "<strong>SELECT</strong> `data`\n<strong>FROM</strong> `ci_sessions`\n<strong>WHERE</strong> `id` = &#039;ci_session:a34f8764d85655fc73194e864f0b0a40&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Session/Handlers/DatabaseHandler.php:135", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"function": "        CodeIgniter\\Session\\Handlers\\DatabaseHandler->read()", "file": "[internal function]", "index": "  3    "}, {"file": "SYSTEMPATH/Session/Session.php:912", "function": "        session_start()", "index": "  4    "}, {"file": "SYSTEMPATH/Session/Session.php:243", "function": "        CodeIgniter\\Session\\Session->startSession()", "index": "  5    "}, {"file": "SYSTEMPATH/Config/Services.php:710", "function": "        CodeIgniter\\Session\\Session->start()", "index": "  6    "}, {"file": "SYSTEMPATH/Config/BaseService.php:312", "function": "        CodeIgniter\\Config\\Services::session()", "index": "  7    "}, {"file": "SYSTEMPATH/Config/BaseService.php:251", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": "  8    "}, {"file": "SYSTEMPATH/Config/Services.php:668", "function": "        CodeIgniter\\Config\\BaseService::getSharedInstance()", "index": "  9    "}, {"file": "SYSTEMPATH/Config/BaseService.php:321", "function": "        CodeIgniter\\Config\\Services::session()", "index": " 10    "}, {"file": "SYSTEMPATH/Config/BaseService.php:202", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": " 11    "}, {"file": "SYSTEMPATH/Common.php:1000", "function": "        CodeIgniter\\Config\\BaseService::get()", "index": " 12    "}, {"file": "SYSTEMPATH/Common.php:973", "function": "        service()", "index": " 13    "}, {"file": "APPPATH/Filters/AuthFilter.php:29", "function": "        session()", "index": " 14    "}, {"file": "SYSTEMPATH/Filters/Filters.php:241", "function": "        App\\Filters\\AuthFilter->before()", "index": " 15    "}, {"file": "SYSTEMPATH/Filters/Filters.php:221", "function": "        CodeIgniter\\Filters\\Filters->runBefore()", "index": " 16    "}, {"file": "SYSTEMPATH/CodeIgniter.php:479", "function": "        CodeIgniter\\Filters\\Filters->run()", "index": " 17    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 18    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 19    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 20    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 21    "}], "trace-file": "[internal function]", "qid": "274c08ccc76669674a488c19cd87c995"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:36", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:36", "qid": "a0f4ceecd3f0e474656a92c3670acdb5"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:37", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:37", "qid": "4b7c0e3c6da224ce05353996f953ef9f"}, {"hover": "", "class": "", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `members`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:41", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:41", "qid": "a051a7b3090ce8f9d6fab1f35b455502"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "ea2b6b53206e6b7d5f088cb454a3061f"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "b98e23d7781337d5a4140049de704343"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "59d20afbc5db03619fbc65a15df9207f"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "ce3a4ce18e5d5635b04f7114aa616258"}, {"hover": "", "class": "", "duration": "1.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "3bcffe8f7731e9ee781fd798dfac34dd"}, {"hover": "", "class": "", "duration": "0.43 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "d4a9065e7919f90857fd26fa0baa6574"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "5d66a50373701854fce232aa8013336e"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "e7d1219804a7f8b5e48e6c394019717a"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "332f6f96c4b63255bff1f0b84ad6cc73"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "2388410c8fb4481a408bf0e6faf80991"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "1c50094b2144d030d4427a52703e8bf4"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "1d6f5fab830655df786e80e146710b30"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "0e80ec9a24156ba52d273598deb807d9"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "db32d2a91bf3309a41790b16616edbbe"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "519bf1b95d825a3dacd0f5bacffd23d1"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "faaa7375143700ef78e69092a7f53c29"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "8ec53d21c8857ef8990e5cd6bafc70ce"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "9e1fefda473beaf22f922569c833f6be"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "22641134a4aabea0b6f5d0f11e5a9bb5"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "08af2878a739f3affadf8bef1d8c092c"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "416dc595b2453d9a6fcb9d601e65f5ea"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "5e28832da152c7c95c4aba6c6caf23d6"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "ce4d6f69dd55badb10a285a258040946"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "64f01829e6ca1f209f608961d08cd216"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "6e3675a2338d9241f0717789460aa67a"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "cc71a37bed175f5da4fe9fe2fab3a74c"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "b340ff0d09affd259e187509a2e0a73f"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "3ce566cff55807dcfb29f1bf78759bec"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "6896b44b26c67ad46fed9adf63fe59e2"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "d839896b444316f0bc5d34b7d7a774f0"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "375aa266f22d2017396a7d9ae118daf8"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "ef6a2ec1997cfeade9bbe4517f1fdaf8"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "405313e75f61fb6d529b1090ac0de762"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "6ef248716ac7439a26b2211d07b305e1"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "c033da6b72a4ea570e0e9080207f5f39"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> <strong>SUM</strong>(`amount`) <strong>AS</strong> `amount`\n<strong>FROM</strong> `payments`\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:47", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:47", "qid": "fa48db2dabbfc6d3d5ee021cfaaba63e"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> <strong>SUM</strong>(`balance`) <strong>AS</strong> `balance`\n<strong>FROM</strong> `collection_summary`\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:50", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:50", "qid": "1fbf68aad96da9b42046cee0463b42c3"}, {"hover": "", "class": "", "duration": "1.9 ms", "sql": "<strong>SELECT</strong> `p`.*, `m`.`name` as `member_name`, `c`.`amount` as `commitment_amount`, `c`.`frequency` as `commitment_frequency`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `p`.`member_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `c`.`commitment_id` = `p`.`commitment_id`\n<strong>ORDER</strong> <strong>BY</strong> `p`.`payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/PaymentModel.php:113", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:53", "function": "        App\\Models\\PaymentModel->getPaymentsWithMemberDetails()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/PaymentModel.php:113", "qid": "27883ef834cd34fd7117a7dba610681b"}, {"hover": "", "class": "", "duration": "0.68 ms", "sql": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`, `m`.`phone`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `cs`.`balance` &gt; 0\n<strong>AND</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `cs`.`balance` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:334", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:57", "function": "        App\\Models\\CollectionSummaryModel->getMembersWithOutstandingBalances()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:334", "qid": "7f2c9491ce285f8a13adf0f7a6fd1aea"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "e0354c7063c90a9ddaa675c950e4aef3"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "b127c1fbc0fcb86e55669e39d5429057"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "663fc36d6285a6bc79562fba388fffbf"}, {"hover": "", "class": "", "duration": "0.18 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "85c7a47cdf3e88011b9ef32f3735c23d"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "8b9e9ebdc37f1078fc75d9d5682a4a9f"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "482ee51a9bebe6d7818f0f7b59e44de5"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "9673d80c10bb697dd2342736cda8e41c"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "38f5c77e285e56ba830e14c8ff49cdde"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "6972462a82f3554132d7ed9c61e7dd6f"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "e0088425caaa1bce2c3d753ba5dec9fe"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "eccc35d5a4e838648e2aa0d0770747a0"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "432ec20dc072aa2fc3c5e4027569ab28"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "5dfb909184cc4226d74ba12177962657"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "49ee653c82c30aa1ffad23dc26f659ac"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "847b1af36ef0f0b569d0e7a3da878e72"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "9c633882fe2b2c854d0eb8b1974f835f"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "555a5812bc08a3a93314d20ea8bc252c"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "67e1fe4642810835d57d748fafcc406a"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "2085183b37cb70b9799ef3d46ab318bf"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "9ed01f493979dc1cc53483780855b564"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "04582a75ac2042f62f429cee752cbfaf"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "dd2c895eb1e864281dcbb799c0a4d377"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "fdc41df79613896c03693b8f40aaefed"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "2552036eb3559d36175d561de8316c14"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "526a50d868a09b918bf4ae1262ecaa6f"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "be627eb76fcd8bf2123bf5226a837128"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "8231c7aa8c06d216e29e4362dadcefb2"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "99c7055dc10c3f3bec0a94b6dc9647c6"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "260bce90553d1ad9dd836548a2173fe5"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "4531ad2db13f00f5a2cd409e74bee1a0"}, {"hover": "", "class": "", "duration": "0.79 ms", "sql": "<strong>SELECT</strong> `m`.*\n<strong>FROM</strong> `members` `m`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>AND</strong> `m`.`member_id` <strong>NOT</strong> <strong>IN</strong> (<strong>SELECT</strong> `c`.`member_id` <strong>FROM</strong> `commitments` `c` <strong>WHERE</strong> `c`.`start_date` &lt;= &#039;2025-05-28&#039; <strong>AND</strong>   ( `c`.`end_date` <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> `c`.`end_date` &gt;= &#039;2025-05-28&#039;  ))", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:993", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:75", "function": "        App\\Controllers\\Reports->getMembersWithoutActiveCommitments()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:993", "qid": "f9eeccfa8e225faf4ae1b4acdc85d7d9"}, {"hover": "", "class": "", "duration": "0.71 ms", "sql": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>ORDER</strong> <strong>BY</strong> `m`.`name` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:61", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:79", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetails()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:61", "qid": "1a29d24988e9b501db5873884557868f"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> `payment_method`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `payments`\n<strong>GROUP</strong> <strong>BY</strong> `payment_method`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:124", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:124", "qid": "f8b72c868029f2a72980d29664385e4e"}, {"hover": "", "class": "", "duration": "0.57 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:537", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "b1166ec0acac92a633b1cf92c6af1c5d"}, {"hover": "", "class": "", "duration": "0.63 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:538", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "59899cc14d5f44623c1f92bb54a4b514"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "502c6b84a9224407fbc059cf73e92a01"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.57 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "55aed170363eba48cccf4b07da4915fc"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.56 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "c4b2a5f0c856599e928b1469081c7873"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.54 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "bfc510d3d1221870e8919a8a16be5eda"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "6.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "336b637635733ea3f04b2e11cff996e0"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "02cf1a1cc1a1178fe45c63c5c51cbe43"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "af76ea437ac7a8db92989240d2159a45"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.52 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "d8b4531f4bbc6aacdad5112f73f87be3"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.52 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "c2b37da79e6e2a684a4d769ad55dcf7c"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.49 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "25d078d44b120439aab79edb52bd0a29"}, {"hover": "", "class": "", "duration": "0.49 ms", "sql": "<strong>SELECT</strong> `p`.`payment_date`, `c`.`start_date`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `p`.`commitment_id` = `c`.`commitment_id`\n<strong>WHERE</strong> `p`.`commitment_id` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `c`.`start_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `p`.`payment_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:588", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:556", "function": "        App\\Controllers\\Reports->calculateAverageMonthsToPayment()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:588", "qid": "e572f29b68066f72642386a80c1e2c44"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:559", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:559", "qid": "a000712319d5838e287fcdd3a3325f00"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `collection_summary`\n<strong>WHERE</strong> `balance` &gt; 0", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:560", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:560", "qid": "6343de1fc430c70b52a6945f0f3cd5c1"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.56 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "22573942ba719e6dd175dbf9e0ebf714"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.54 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "47c3232da56c93a5da1ecf2e78f3730f"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.53 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "cf5546fc7bfddd32c1aecabe2095bc83"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.58 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "ecbac1471bbfdb6135a8fa8239086603"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.56 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "0b52e9b9b7d71aaa48e8d30d84dc9b05"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.53 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "4b5f8ea791137f7a9938b4c615efb4cb"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.66 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "22954a673bf2dfe548b83c549264f474"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.56 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "af81fcf330a60649c030fe160b04d9a9"}]}, "badgeValue": 100, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.704804, "duration": "0.003059"}, {"name": "Query", "component": "Database", "start": **********.707975, "duration": "0.000260", "query": "<strong>SELECT</strong> GET_LOCK(&#039;9ed23a7dc6446a079b4f496139134143&#039;, 300) <strong>AS</strong> ci_session_lock"}, {"name": "Query", "component": "Database", "start": **********.708777, "duration": "0.000696", "query": "<strong>SELECT</strong> `data`\n<strong>FROM</strong> `ci_sessions`\n<strong>WHERE</strong> `id` = &#039;ci_session:a34f8764d85655fc73194e864f0b0a40&#039;"}, {"name": "Query", "component": "Database", "start": **********.710084, "duration": "0.000279", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`"}, {"name": "Query", "component": "Database", "start": **********.710471, "duration": "0.000263", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.710806, "duration": "0.000548", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `members`"}, {"name": "Query", "component": "Database", "start": **********.711481, "duration": "0.000330", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.711892, "duration": "0.000250", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;"}, {"name": "Query", "component": "Database", "start": **********.712224, "duration": "0.000230", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;"}, {"name": "Query", "component": "Database", "start": **********.712512, "duration": "0.000234", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.712806, "duration": "0.001332", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;"}, {"name": "Query", "component": "Database", "start": **********.714192, "duration": "0.000426", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.714737, "duration": "0.000328", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;"}, {"name": "Query", "component": "Database", "start": **********.715142, "duration": "0.000288", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.715516, "duration": "0.000261", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.715841, "duration": "0.000274", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;"}, {"name": "Query", "component": "Database", "start": **********.716176, "duration": "0.000242", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.716487, "duration": "0.000208", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.716769, "duration": "0.000208", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.717047, "duration": "0.000340", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.717478, "duration": "0.000348", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.71794, "duration": "0.000315", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.718352, "duration": "0.000294", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.718703, "duration": "0.000300", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.719082, "duration": "0.000285", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;"}, {"name": "Query", "component": "Database", "start": **********.719427, "duration": "0.000255", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.719737, "duration": "0.000266", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.720126, "duration": "0.000287", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.720518, "duration": "0.000276", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.720861, "duration": "0.000284", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.721222, "duration": "0.000271", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.721577, "duration": "0.000278", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.721924, "duration": "0.000284", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.722282, "duration": "0.000264", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.722611, "duration": "0.000268", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.722969, "duration": "0.000292", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.723339, "duration": "0.000256", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.723664, "duration": "0.000228", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.723963, "duration": "0.000240", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.724291, "duration": "0.000328", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.724702, "duration": "0.000276", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.725085, "duration": "0.000260", "query": "<strong>SELECT</strong> <strong>SUM</strong>(`amount`) <strong>AS</strong> `amount`\n<strong>FROM</strong> `payments`\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.725417, "duration": "0.000249", "query": "<strong>SELECT</strong> <strong>SUM</strong>(`balance`) <strong>AS</strong> `balance`\n<strong>FROM</strong> `collection_summary`\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.725897, "duration": "0.001897", "query": "<strong>SELECT</strong> `p`.*, `m`.`name` as `member_name`, `c`.`amount` as `commitment_amount`, `c`.`frequency` as `commitment_frequency`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `p`.`member_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `c`.`commitment_id` = `p`.`commitment_id`\n<strong>ORDER</strong> <strong>BY</strong> `p`.`payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.728174, "duration": "0.000678", "query": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`, `m`.`phone`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `cs`.`balance` &gt; 0\n<strong>AND</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `cs`.`balance` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.728965, "duration": "0.000246", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.729313, "duration": "0.000196", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.72959, "duration": "0.000192", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.729848, "duration": "0.000181", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.730128, "duration": "0.000186", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.730374, "duration": "0.000175", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.73061, "duration": "0.000169", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.730838, "duration": "0.000215", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.731121, "duration": "0.000199", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.731382, "duration": "0.000195", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.73165, "duration": "0.000219", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.731942, "duration": "0.000222", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.73224, "duration": "0.000212", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.732526, "duration": "0.000218", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.732814, "duration": "0.000219", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.733136, "duration": "0.000217", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.733459, "duration": "0.000214", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;"}, {"name": "Query", "component": "Database", "start": **********.733742, "duration": "0.000192", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.733993, "duration": "0.000197", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.734247, "duration": "0.000172", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.734477, "duration": "0.000347", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.734898, "duration": "0.000247", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;"}, {"name": "Query", "component": "Database", "start": **********.73525, "duration": "0.000225", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.735543, "duration": "0.000203", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.735824, "duration": "0.000210", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.736132, "duration": "0.000211", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.736411, "duration": "0.000196", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.736669, "duration": "0.000189", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.736932, "duration": "0.000212", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.737239, "duration": "0.000185", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.737694, "duration": "0.000792", "query": "<strong>SELECT</strong> `m`.*\n<strong>FROM</strong> `members` `m`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>AND</strong> `m`.`member_id` <strong>NOT</strong> <strong>IN</strong> (<strong>SELECT</strong> `c`.`member_id` <strong>FROM</strong> `commitments` `c` <strong>WHERE</strong> `c`.`start_date` &lt;= &#039;2025-05-28&#039; <strong>AND</strong>   ( `c`.`end_date` <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> `c`.`end_date` &gt;= &#039;2025-05-28&#039;  ))"}, {"name": "Query", "component": "Database", "start": **********.738656, "duration": "0.000713", "query": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>ORDER</strong> <strong>BY</strong> `m`.`name` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.739501, "duration": "0.000336", "query": "<strong>SELECT</strong> `payment_method`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `payments`\n<strong>GROUP</strong> <strong>BY</strong> `payment_method`"}, {"name": "Query", "component": "Database", "start": **********.739904, "duration": "0.000569", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.740782, "duration": "0.000632", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.741618, "duration": "0.000603", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.742673, "duration": "0.000567", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.74342, "duration": "0.000555", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.744332, "duration": "0.000537", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.745031, "duration": "0.006242", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.751535, "duration": "0.000597", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.752247, "duration": "0.000555", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.753043, "duration": "0.000517", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.753679, "duration": "0.000516", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.754438, "duration": "0.000494", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.755189, "duration": "0.000491", "query": "<strong>SELECT</strong> `p`.`payment_date`, `c`.`start_date`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `p`.`commitment_id` = `c`.`commitment_id`\n<strong>WHERE</strong> `p`.`commitment_id` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `c`.`start_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `p`.`payment_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.755915, "duration": "0.000499", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.756487, "duration": "0.000295", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `collection_summary`\n<strong>WHERE</strong> `balance` &gt; 0"}, {"name": "Query", "component": "Database", "start": **********.756873, "duration": "0.000558", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.757665, "duration": "0.000541", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.758335, "duration": "0.000531", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.759112, "duration": "0.000582", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.759809, "duration": "0.000555", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.760638, "duration": "0.000533", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.761281, "duration": "0.000658", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.762193, "duration": "0.000557", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: layout/main.php", "component": "Views", "start": **********.791555, "duration": 0.0003941059112548828}, {"name": "View: reports/dashboard.php", "component": "Views", "start": **********.790861, "duration": 0.001177072525024414}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 163 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/Database/MySQLiHandler.php", "name": "MySQLiHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/DatabaseHandler.php", "name": "DatabaseHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Performance.php", "name": "Performance.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Controllers/Reports.php", "name": "Reports.php"}, {"path": "APPPATH/Filters/AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH/Filters/PerformanceFilter.php", "name": "PerformanceFilter.php"}, {"path": "APPPATH/Helpers/currency_helper.php", "name": "currency_helper.php"}, {"path": "APPPATH/Models/CollectionSummaryModel.php", "name": "CollectionSummaryModel.php"}, {"path": "APPPATH/Models/CommitmentModel.php", "name": "CommitmentModel.php"}, {"path": "APPPATH/Models/MemberModel.php", "name": "MemberModel.php"}, {"path": "APPPATH/Models/PaymentModel.php", "name": "PaymentModel.php"}, {"path": "APPPATH/Views/layout/main.php", "name": "main.php"}, {"path": "APPPATH/Views/reports/dashboard.php", "name": "dashboard.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "FCPATH/vendor/autoload.php", "name": "autoload.php"}, {"path": "FCPATH/vendor/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "FCPATH/vendor/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "FCPATH/vendor/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "FCPATH/vendor/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "FCPATH/vendor/composer/installed.php", "name": "installed.php"}, {"path": "FCPATH/vendor/composer/platform_check.php", "name": "platform_check.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "FCPATH/vendor/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}]}, "badgeValue": 163, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Reports", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::index"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/change-password", "handler": "\\App\\Controllers\\Auth::changePassword"}, {"method": "GET", "route": "members", "handler": "\\App\\Controllers\\Members::index"}, {"method": "GET", "route": "members/new", "handler": "\\App\\Controllers\\Members::new"}, {"method": "GET", "route": "members/show/([0-9]+)", "handler": "\\App\\Controllers\\Members::show/$1"}, {"method": "GET", "route": "members/edit/([0-9]+)", "handler": "\\App\\Controllers\\Members::edit/$1"}, {"method": "GET", "route": "members/delete/([0-9]+)", "handler": "\\App\\Controllers\\Members::delete/$1"}, {"method": "GET", "route": "commitments", "handler": "\\App\\Controllers\\Commitments::index"}, {"method": "GET", "route": "commitments/new", "handler": "\\App\\Controllers\\Commitments::new"}, {"method": "GET", "route": "commitments/create/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::new/$1"}, {"method": "GET", "route": "commitments/show/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::show/$1"}, {"method": "GET", "route": "commitments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::edit/$1"}, {"method": "GET", "route": "commitments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::delete/$1"}, {"method": "GET", "route": "commitments/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::memberCommitments/$1"}, {"method": "GET", "route": "payments", "handler": "\\App\\Controllers\\Payments::index"}, {"method": "GET", "route": "payments/new", "handler": "\\App\\Controllers\\Payments::new"}, {"method": "GET", "route": "payments/show/([0-9]+)", "handler": "\\App\\Controllers\\Payments::show/$1"}, {"method": "GET", "route": "payments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Payments::edit/$1"}, {"method": "GET", "route": "payments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Payments::delete/$1"}, {"method": "GET", "route": "payments/member-payments/([0-9]+)", "handler": "\\App\\Controllers\\Payments::memberPayments/$1"}, {"method": "GET", "route": "payments/generate-receipt/([0-9]+)", "handler": "\\App\\Controllers\\Payments::generateReceipt/$1"}, {"method": "GET", "route": "reports", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/collection-summary", "handler": "\\App\\Controllers\\Reports::collectionSummary"}, {"method": "GET", "route": "reports/outstanding-balances", "handler": "\\App\\Controllers\\Reports::outstandingBalances"}, {"method": "GET", "route": "reports/payment-history", "handler": "\\App\\Controllers\\Reports::paymentHistory"}, {"method": "GET", "route": "reports/commitment-report", "handler": "\\App\\Controllers\\Reports::commitmentReport"}, {"method": "GET", "route": "reports/statistics", "handler": "\\App\\Controllers\\Reports::statistics"}, {"method": "GET", "route": "reports/recalculate-all-summaries", "handler": "\\App\\Controllers\\Reports::recalculateAllSummaries"}, {"method": "GET", "route": "reports/members-without-commitments", "handler": "\\App\\Controllers\\Reports::membersWithoutCommitments"}, {"method": "GET", "route": "api/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Api::getMemberCommitments/$1"}, {"method": "GET", "route": "api/payment-details/([0-9]+)/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1/$2"}, {"method": "GET", "route": "api/payment-details/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "auth/update-password", "handler": "\\App\\Controllers\\Auth::updatePassword"}, {"method": "POST", "route": "members/create", "handler": "\\App\\Controllers\\Members::create"}, {"method": "POST", "route": "members/update/([0-9]+)", "handler": "\\App\\Controllers\\Members::update/$1"}, {"method": "POST", "route": "commitments/create", "handler": "\\App\\Controllers\\Commitments::create"}, {"method": "POST", "route": "commitments/update/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::update/$1"}, {"method": "POST", "route": "payments/create", "handler": "\\App\\Controllers\\Payments::create"}, {"method": "POST", "route": "payments/update/([0-9]+)", "handler": "\\App\\Controllers\\Payments::update/$1"}]}, "badgeValue": 35, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "1.10", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "1.42", "count": 144}}}, "badgeValue": 145, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.700927, "duration": 0.0011019706726074219}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.708242, "duration": 6.413459777832031e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.709476, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.710365, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.710736, "duration": 2.09808349609375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.711356, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.711812, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.712144, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.712456, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.712747, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.714139, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.71462, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.715066, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.715431, "duration": 2.002716064453125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.715778, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.716117, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.716419, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.716696, "duration": 2.09808349609375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.716978, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.717389, "duration": 1.6927719116210938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.717829, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.718257, "duration": 2.6941299438476562e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.718647, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.719004, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.719368, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.719683, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.720004, "duration": 2.193450927734375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.720416, "duration": 1.3828277587890625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.720795, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.721146, "duration": 1.3828277587890625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.721494, "duration": 2.09808349609375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.721857, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.722209, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.722547, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.722881, "duration": 2.384185791015625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.723263, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.723597, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.723894, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.724205, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.724621, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.72498, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.725347, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.725668, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.727797, "duration": 3.0994415283203125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.728854, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729213, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729511, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729784, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.73003, "duration": 4.100799560546875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.730315, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.730551, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.73078, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731054, "duration": 1.6927719116210938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731321, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731578, "duration": 2.288818359375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731871, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.732166, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.732455, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.732745, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.733035, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.733355, "duration": 3.1948089599609375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.733675, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.733935, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.734191, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.734421, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.734825, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.735147, "duration": 2.7894973754882812e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.735477, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.735748, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.736036, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.736344, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.736608, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.736859, "duration": 2.384185791015625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.737147, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.737426, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.738489, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.739372, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.739839, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.740475, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.741417, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.742224, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.743242, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.743977, "duration": 3.504753112792969e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.744871, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.751275, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.752134, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.752804, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.753561, "duration": 2.5987625122070312e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.754196, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.754933, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.755681, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.756416, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.756783, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.757432, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.758208, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.758867, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.759695, "duration": 1.6927719116210938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.760366, "duration": 1.3113021850585938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.761173, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.761941, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.762751, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.763419, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.764363, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.765035, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.765931, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.766669, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.767449, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.768075, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.768826, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.769441, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.770217, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.770836, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.7716, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.772257, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.772941, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.773494, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.77425, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.774874, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.775609, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.776204, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.777049, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.77782, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.778569, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.77921, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.779844, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.780597, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.781343, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.782102, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.782926, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.783724, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.784513, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.785461, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.786353, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.786737, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.787121, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.7876, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.787916, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.788254, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.788596, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.788892, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.789196, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.78948, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.789767, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.790048, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.790343, "duration": 1.9073486328125e-06}]}], "vars": {"varData": {"View Data": {"title": "Dashboard", "totalMembers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 35</dt></dl></div>", "activeMembers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 35</dt></dl></div>", "totalCommitments": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>double</var> 126800</dt></dl></div>", "totalPayments": "20190.00", "totalOutstanding": "106610.00", "recentPayments": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>payment_id</th><th>member_id</th><th>commitment_id</th><th>amount</th><th>payment_date</th><th>receipt_number</th><th>receipt_book_number</th><th>payment_method</th><th>notes</th><th>payment_periods</th><th>created_at</th><th>updated_at</th><th>member_name</th><th>commitment_amount</th><th>commitment_frequency</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">2</td><td title=\"string (1)\">1</td><td title=\"string (1)\">7</td><td title=\"string (6)\">300.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050002</td><td title=\"string (1)\">1</td><td title=\"string (13)\">bank_transfer</td><td title=\"string (55)\">Payment for: October 2024, November 2024, December 2024</td><td title=\"string (31)\">[\"2024-10\",\"2024-11\",\"2024-12\"]</td><td title=\"string (19)\">2025-05-24 13:25:21</td><td title=\"string (19)\">2025-05-24 13:25:21</td><td title=\"string (14)\">Abdu Raheem KK</td><td title=\"string (6)\">100.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>1</th><td title=\"string (2)\">58</td><td title=\"string (2)\">15</td><td title=\"string (2)\">29</td><td title=\"string (7)\">4730.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050005</td><td title=\"string (1)\">4</td><td title=\"string (5)\">check</td><td title=\"string (155)\">Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, JuUTF-8</td><td title=\"string (111)\">[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"202UTF-8</td><td title=\"string (19)\">2025-05-24 16:59:14</td><td title=\"string (19)\">2025-05-24 16:59:14</td><td title=\"string (12)\">Adnan Al-Ali</td><td title=\"string (6)\">430.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>2</th><td title=\"string (1)\">1</td><td title=\"string (1)\">3</td><td title=\"string (1)\">4</td><td title=\"string (6)\">100.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050001</td><td title=\"string (1)\">1</td><td title=\"string (4)\">cash</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 12:35:13</td><td title=\"string (19)\">2025-05-24 12:35:13</td><td title=\"string (5)\">Murad</td><td title=\"string (6)\">100.00</td><td title=\"string (8)\">one-time</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"string (1)\">4</td><td title=\"string (1)\">6</td><td title=\"string (6)\">600.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050004</td><td title=\"string (1)\">3</td><td title=\"string (5)\">other</td><td title=\"string (52)\">Payment for: January 2025, February 2025, March 2025</td><td title=\"string (31)\">[\"2025-01\",\"2025-02\",\"2025-03\"]</td><td title=\"string (19)\">2025-05-24 14:13:50</td><td title=\"string (19)\">2025-05-24 14:13:50</td><td title=\"string (5)\">Riyas</td><td title=\"string (6)\">200.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>4</th><td title=\"string (2)\">18</td><td title=\"string (2)\">14</td><td title=\"string (2)\">26</td><td title=\"string (5)\">70.00</td><td title=\"string (10)\">2025-05-19</td><td title=\"string (4)\">8961</td><td title=\"string (4)\">BK02</td><td title=\"string (4)\">cash</td><td title=\"null\"><var>null</var></td><td title=\"string (7)\">2025-03</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (15)\">Ibrahim Al-Saud</td><td title=\"string (5)\">70.00</td><td title=\"string (7)\">monthly</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[0]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"300.00\"<div class=\"access-path\">$value[0]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[0]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050002\"<div class=\"access-path\">$value[0]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (13) \"bank_transfer\"<div class=\"access-path\">$value[0]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (55) \"Payment for: October 2024, November 2024, December 2024\"<div class=\"access-path\">$value[0]['notes']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (31) \"[\"2024-10\",\"2024-11\",\"2024-12\"]\"<div class=\"access-path\">$value[0]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2024-10\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2024-11\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2024-12\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[2]</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 13:25:21\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 13:25:21\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Abdu Raheem KK\"<div class=\"access-path\">$value[0]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[0]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[0]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (2) \"58\"<div class=\"access-path\">$value[1]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[1]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (7) \"4730.00\"<div class=\"access-path\">$value[1]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[1]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050005\"<div class=\"access-path\">$value[1]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (5) \"check\"<div class=\"access-path\">$value[1]['payment_method']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>notes</dfn> =&gt; <var>string</var> (155) \"Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, Jul...<div class=\"access-path\">$value[1]['notes']</div></dt><dd><pre>Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, July 2025, August 2025, September 2025, October 2025, November 2025, December 2025\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (111) \"[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"2025...<div class=\"access-path\">$value[1]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (11)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2025-02\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2025-04\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[2]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>3</dfn> =&gt; <var>string</var> (7) \"2025-05\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[3]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>4</dfn> =&gt; <var>string</var> (7) \"2025-06\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[4]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>5</dfn> =&gt; <var>string</var> (7) \"2025-07\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[5]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>6</dfn> =&gt; <var>string</var> (7) \"2025-08\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[6]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>7</dfn> =&gt; <var>string</var> (7) \"2025-09\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[7]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>8</dfn> =&gt; <var>string</var> (7) \"2025-10\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[8]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>9</dfn> =&gt; <var>string</var> (7) \"2025-11\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[9]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>10</dfn> =&gt; <var>string</var> (7) \"2025-12\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[10]</div></dt></dl></li><li><pre>[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"2025-09\",\"2025-10\",\"2025-11\",\"2025-12\"]\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:59:14\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:59:14\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (12) \"Adnan Al-Ali\"<div class=\"access-path\">$value[1]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"430.00\"<div class=\"access-path\">$value[1]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[1]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[2]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[2]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050001\"<div class=\"access-path\">$value[2]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (4) \"cash\"<div class=\"access-path\">$value[2]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_periods</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['payment_periods']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 12:35:13\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 12:35:13\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Murad\"<div class=\"access-path\">$value[2]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[2]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (8) \"one-time\"<div class=\"access-path\">$value[2]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[3]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"600.00\"<div class=\"access-path\">$value[3]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[3]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050004\"<div class=\"access-path\">$value[3]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (5) \"other\"<div class=\"access-path\">$value[3]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (52) \"Payment for: January 2025, February 2025, March 2025\"<div class=\"access-path\">$value[3]['notes']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (31) \"[\"2025-01\",\"2025-02\",\"2025-03\"]\"<div class=\"access-path\">$value[3]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2025-01\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2025-02\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[2]</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 14:13:50\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 14:13:50\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Riyas\"<div class=\"access-path\">$value[3]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"200.00\"<div class=\"access-path\">$value[3]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[3]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[4]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[4]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[4]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-19\"<div class=\"access-path\">$value[4]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (4) \"8961\"<div class=\"access-path\">$value[4]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (4) \"BK02\"<div class=\"access-path\">$value[4]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (4) \"cash\"<div class=\"access-path\">$value[4]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_periods</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">$value[4]['payment_periods']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Ibrahim Al-Saud\"<div class=\"access-path\">$value[4]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[4]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[4]['commitment_frequency']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "membersWithOutstandingBalances": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>summary_id</th><th>member_id</th><th>total_committed</th><th>total_paid</th><th>balance</th><th>last_payment_date</th><th>last_payment_amount</th><th>created_at</th><th>updated_at</th><th>member_name</th><th>status</th><th>phone</th><th>whatsapp_number</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">28</td><td title=\"string (2)\">28</td><td title=\"string (7)\">9540.00</td><td title=\"string (6)\">340.00</td><td title=\"string (7)\">9200.00</td><td title=\"string (10)\">2024-11-10</td><td title=\"string (6)\">170.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (13)\">Saud Al-Ahmad</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0592485468</td><td title=\"string (10)\">0554246003</td></tr><tr><th>1</th><td title=\"string (2)\">18</td><td title=\"string (2)\">18</td><td title=\"string (7)\">9150.00</td><td title=\"string (6)\">380.00</td><td title=\"string (7)\">8770.00</td><td title=\"string (10)\">2025-05-07</td><td title=\"string (6)\">190.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (15)\">Bilal Al-Waleed</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0515351788</td><td title=\"string (10)\">0522375982</td></tr><tr><th>2</th><td title=\"string (2)\">21</td><td title=\"string (2)\">21</td><td title=\"string (8)\">10020.00</td><td title=\"string (7)\">1480.00</td><td title=\"string (7)\">8540.00</td><td title=\"string (10)\">2025-03-10</td><td title=\"string (6)\">490.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (14)\">Bilal Al-Jamal</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0528141858</td><td title=\"string (10)\">0523298126</td></tr><tr><th>3</th><td title=\"string (2)\">30</td><td title=\"string (2)\">30</td><td title=\"string (7)\">6690.00</td><td title=\"string (4)\">0.00</td><td title=\"string (7)\">6690.00</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (17)\">Ibrahim Al-Hassan</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0549461895</td><td title=\"string (10)\">0582341569</td></tr><tr><th>4</th><td title=\"string (2)\">29</td><td title=\"string (2)\">29</td><td title=\"string (7)\">6100.00</td><td title=\"string (4)\">0.00</td><td title=\"string (7)\">6100.00</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (15)\">Salman Al-Bilal</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0541587438</td><td title=\"string (10)\">0523101499</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[0]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"9540.00\"<div class=\"access-path\">$value[0]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (6) \"340.00\"<div class=\"access-path\">$value[0]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"9200.00\"<div class=\"access-path\">$value[0]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-11-10\"<div class=\"access-path\">$value[0]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"170.00\"<div class=\"access-path\">$value[0]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (13) \"Saud Al-Ahmad\"<div class=\"access-path\">$value[0]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0592485468\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>1988-10-10T11:17:48+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0554246003\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>1987-07-25T21:13:23+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"9150.00\"<div class=\"access-path\">$value[1]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (6) \"380.00\"<div class=\"access-path\">$value[1]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8770.00\"<div class=\"access-path\">$value[1]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-07\"<div class=\"access-path\">$value[1]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"190.00\"<div class=\"access-path\">$value[1]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Bilal Al-Waleed\"<div class=\"access-path\">$value[1]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0515351788\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>1986-05-01T17:16:28+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0522375982\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>1986-07-22T00:26:22+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (8) \"10020.00\"<div class=\"access-path\">$value[2]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (7) \"1480.00\"<div class=\"access-path\">$value[2]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8540.00\"<div class=\"access-path\">$value[2]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-10\"<div class=\"access-path\">$value[2]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"490.00\"<div class=\"access-path\">$value[2]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Bilal Al-Jamal\"<div class=\"access-path\">$value[2]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0528141858\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1986-09-26T18:04:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523298126\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1986-08-01T16:35:26+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"6690.00\"<div class=\"access-path\">$value[3]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[3]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6690.00\"<div class=\"access-path\">$value[3]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (17) \"Ibrahim Al-Hassan\"<div class=\"access-path\">$value[3]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0549461895\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1987-05-31T12:18:15+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0582341569\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1988-06-15T01:32:49+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[4]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"6100.00\"<div class=\"access-path\">$value[4]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[4]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6100.00\"<div class=\"access-path\">$value[4]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Salman Al-Bilal\"<div class=\"access-path\">$value[4]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0541587438\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1987-03-01T08:57:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523101499\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1986-07-30T09:58:19+00:00\n</pre></dd></dl></dd></dl></li></ul></dd></dl></div>", "membersWithoutActiveCommitments": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>member_id</th><th>name</th><th>email</th><th>post_office</th><th>old_reference</th><th>phone</th><th>whatsapp_number</th><th>address</th><th>join_date</th><th>status</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (14)\">Abdu Raheem KK</td><td title=\"string (18)\"><EMAIL></td><td title=\"string (10)\">Karalmanna</td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777125</td><td title=\"string (10)\">9947777125</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2018-01-01</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 22:16:26</td><td title=\"string (19)\">2025-05-24 10:30:28</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (7)\">Subeena</td><td title=\"string (15)\"><EMAIL></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777124</td><td title=\"string (10)\">9605447793</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2025-05-18</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 23:25:30</td><td title=\"string (19)\">2025-05-24 10:30:55</td></tr><tr><th>2</th><td title=\"string (1)\">5</td><td title=\"string (14)\">Omar Al-Nasser</td><td title=\"string (26)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">HIST-C-001</td><td title=\"string (10)\">0537494636</td><td title=\"string (10)\">0572364484</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-11-09</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:20</td><td title=\"string (19)\">2025-05-24 21:58:20</td></tr><tr><th>3</th><td title=\"string (1)\">7</td><td title=\"string (9)\">Zaid Khan</td><td title=\"string (21)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">OLD-A1-002</td><td title=\"string (10)\">0537155671</td><td title=\"string (10)\">0537155671</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-04-02</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td></tr><tr><th>4</th><td title=\"string (2)\">10</td><td title=\"string (15)\">Fahad Al-Faisal</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (15)\">Industrial Area</td><td title=\"string (12)\">PREV-002-005</td><td title=\"string (10)\">0540420476</td><td title=\"string (10)\">0579349018</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-03-14</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Abdu Raheem KK\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (18) \"<EMAIL>\"<div class=\"access-path\">$value[0]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (10) \"Karalmanna\"<div class=\"access-path\">$value[0]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[0]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2018-01-01\"<div class=\"access-path\">$value[0]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 22:16:26\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:28\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (7) \"Subeena\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (15) \"<EMAIL>\"<div class=\"access-path\">$value[1]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777124\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>2285-03-26T07:25:24+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9605447793\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>2274-05-21T03:56:33+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[1]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-05-18\"<div class=\"access-path\">$value[1]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 23:25:30\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:55\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Omar Al-Nasser\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value[2]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[2]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"HIST-C-001\"<div class=\"access-path\">$value[2]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537494636\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1987-01-13T00:03:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0572364484\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1988-02-20T14:08:04+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-11-09\"<div class=\"access-path\">$value[2]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (9) \"Zaid Khan\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (21) \"<EMAIL>\"<div class=\"access-path\">$value[3]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[3]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-A1-002\"<div class=\"access-path\">$value[3]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-04-02\"<div class=\"access-path\">$value[3]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Fahad Al-Faisal\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[4]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (15) \"Industrial Area\"<div class=\"access-path\">$value[4]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (12) \"PREV-002-005\"<div class=\"access-path\">$value[4]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0540420476\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1987-02-15T20:47:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0579349018\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1988-05-11T10:16:58+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-03-14\"<div class=\"access-path\">$value[4]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "startDate": "2024-05-28", "endDate": "2025-05-28", "months": "[&quot;May 2024&quot;,&quot;Jun 2024&quot;,&quot;Jul 2024&quot;,&quot;Aug 2024&quot;,&quot;Sep 2024&quot;,&quot;Oct 2024&quot;,&quot;Nov 2024&quot;,&quot;Dec 2024&quot;,&quot;Jan 2025&quot;,&quot;Feb 2025&quot;,&quot;Mar 2025&quot;,&quot;Apr 2025&quot;,&quot;May 2025&quot;]", "committedAmounts": "[360,2060,3670,3460,5820,6470,7320,8360,9430,8460,8730,9280,10020]", "paidAmounts": "[0,950,450,1230,920,530,790,980,2930,1370,2270,750,1460]", "memberCounts": "[2,7,11,12,17,17,19,23,22,22,20,24,24]", "datePaidAmounts": "[50,0,2000,450,530,1320,900,970,1160,2260,2200,1710,6640]", "fulfillmentRates": "[0,46.1,12.3,35.5,15.8,8.2,10.8,11.7,31.1,16.2,26,8.1,14.6]", "movingAverages": "[50,25,683.********33334,816.6666666666666,993.********33334,766.6666666666666,916.6666666666666,1063.********33333,1010,1463.********33333,1873.********33333,2056.*************,3516.*************]", "memberComplianceData": "{&quot;labels&quot;:[&quot;<PERSON>y Paid (100%)&quot;,&quot;Mostly Paid (75-99%)&quot;,&quot;Partially Paid (25-74%)&quot;,&quot;Minimally Paid (1-24%)&quot;,&quot;No Payments (0%)&quot;],&quot;data&quot;:[4,0,4,15,11],&quot;backgroundColor&quot;:[&quot;#28a745&quot;,&quot;#5cb85c&quot;,&quot;#ffc107&quot;,&quot;#fd7e14&quot;,&quot;#dc3545&quot;]}", "paymentMethodData": "{&quot;labels&quot;:[&quot;Cash&quot;,&quot;Check&quot;,&quot;Bank_transfer&quot;,&quot;Other&quot;],&quot;data&quot;:[55,1,1,1],&quot;backgroundColor&quot;:[&quot;#28a745&quot;,&quot;#6f42c1&quot;,&quot;#6c757d&quot;,&quot;#6c757d&quot;]}", "collectionEfficiencyData": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>currentMonthRate</dfn> =&gt; <var>double</var> 14.6<div class=\"access-path\">$value['currentMonthRate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>ytdRate</dfn> =&gt; <var>double</var> 19.1<div class=\"access-path\">$value['ytdRate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>avgMonths</dfn> =&gt; <var>double</var> 2.8<div class=\"access-path\">$value['avgMonths']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentWithBalance</dfn> =&gt; <var>double</var> 85.7<div class=\"access-path\">$value['percentWithBalance']</div></dt></dl></dd></dl></div>"}}, "session": {"_ci_previous_url": "https://halqa.mazharulirfan.com/reports", "admin_id": "2", "username": "boss", "isLoggedIn": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Accept-Encoding": "br", "Accept-Language": "en-US,en;q=0.9", "Cookie": "_ga=GA1.2.1969489433.1748457976; _gid=GA1.2.22748706.1748457976; _ga_9Q6H0QETRF=GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0; ci_session=a34f8764d85655fc73194e864f0b0a40; csrf_cookie_name=20a371f6557c22ef157eb725bd0aaf89", "Host": "halqa.mazharulirfan.com", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "X-Forwarded-For": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Forwarded-Proto": "https", "X-Real-Ip": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Real-Port": "59594", "X-Forwarded-Port": "443", "X-Port": "443", "X-Lscache": "1", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Site": "none", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;macOS&quot;", "Priority": "u=0, i"}, "cookies": {"_ga": "GA1.2.1969489433.1748457976", "_gid": "GA1.2.22748706.1748457976", "_ga_9Q6H0QETRF": "GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0", "ci_session": "a34f8764d85655fc73194e864f0b0a40", "csrf_cookie_name": "20a371f6557c22ef157eb725bd0aaf89"}, "request": "HTTPS/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.27", "phpSAPI": "litespeed", "environment": "development", "baseURL": "https://halqa.mazharulirfan.com/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}