{"url": "https://halqa.mazharulirfan.com/reports", "method": "GET", "isAJAX": false, "startTime": **********.253959, "totalTime": 85.3, "totalMemory": "1.422", "segmentDuration": 15, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.254251, "duration": 0.0013229846954345703}, {"name": "Required Before Filters", "component": "Timer", "start": **********.255574, "duration": 0.0005300045013427734}, {"name": "Routing", "component": "Timer", "start": **********.256109, "duration": 0.0003380775451660156}, {"name": "Before Filters", "component": "Timer", "start": **********.256487, "duration": 0.0008561611175537109}, {"name": "Controller", "component": "Timer", "start": **********.257345, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.257346, "duration": 0.0005471706390380859}, {"name": "After Filters", "component": "Timer", "start": **********.339098, "duration": 4.9114227294921875e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.33918, "duration": 0.00013208389282226562}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(100 total Queries, 79 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:36", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:36", "qid": "afca576f06db58b63ba031421b21df6e"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:37", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:37", "qid": "c6ba52e1af39cb5cee6e7e8f4780e279"}, {"hover": "", "class": "", "duration": "0.52 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `members`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:41", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:41", "qid": "1c6387b1ae60ea55280ef4d772a32e96"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "0e3630bf5056588639d99950d6b85831"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "02bf08a1343c4316da1f8c0a62a60f17"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "96236bc07a7e7480a11ef88b4a60028c"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "d088e34dc7597d55b604f9a99cabed76"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "3aedae5d3c0581a337c843ad623e1b81"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "984244bbc7909ef77003653fce4ca28f"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "3dcd2cb902918de80305999e516715b9"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "97c3c90cc664302e197a4828f77b49b5"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "f1411ecdcb1e03a2f93a44ca38899bcd"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "aa8dca7a15eff485336627df620921b0"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "6b63bb5b4352192bc9c2e3de5d1b9e36"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "ce1fd5a909e8c18847ca9dc4c8c180e8"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "7f4d4e9f748669c1c0bb85ab9d5fa000"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "32a308518184e0d6f982f43aadddabd0"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "fd8fbaaf57851280de7e81818fdf0555"}, {"hover": "", "class": "", "duration": "3.43 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "849a3e613376deba1f6303f6e4be87d2"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "788112c90e12a1c7da0be5b2dd4be176"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "cc87a65c5ae823b01307f27c092f3c86"}, {"hover": "", "class": "", "duration": "0.45 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "e6e7b91c67a4a660fe6aa058d051120e"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "bd86f0be26bb48b484168b1ec27fbdec"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "909d1920a4ef29042e36d1250893b92b"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "0b6cf30fcb572cbf1624b7f3d30909f1"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "2dbcb56fb8e6e24354a7aed8cfa3af0e"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "5e97cca9ba78a22fbe8d5308e1e86ee0"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "7c90b02762ce3a8517ea1cc1bd95dedf"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "e8bd7a0cd05c0c01bae69230054491fd"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "633ab906f323c921fea52ba55473047d"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "6f4699052055e9fcc8de52cad751d51b"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "1aaca27691e716a82afce2bcb687e513"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "7d15279dd87073c5eb8f27de2000845f"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "46f68fdf6a16d6055590f7e40bf93290"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "bf09a9d2a95d8a71432f71f33be79041"}, {"hover": "", "class": "", "duration": "0.71 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "c44394b4c7efcc0a75a369e6e6dbc302"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "f55acdccce4e05d9e0985740eb6ff471"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "bbfaa1dd9db46ea37e306f5d67f2bc94"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> <strong>SUM</strong>(`amount`) <strong>AS</strong> `amount`\n<strong>FROM</strong> `payments`\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:47", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:47", "qid": "659e0923ba0e498a908ba7391f0bc06a"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> <strong>SUM</strong>(`balance`) <strong>AS</strong> `balance`\n<strong>FROM</strong> `collection_summary`\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:50", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:50", "qid": "1d1502523d49516d67906a5d9fdbbabe"}, {"hover": "", "class": "", "duration": "1.93 ms", "sql": "<strong>SELECT</strong> `p`.*, `m`.`name` as `member_name`, `c`.`amount` as `commitment_amount`, `c`.`frequency` as `commitment_frequency`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `p`.`member_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `c`.`commitment_id` = `p`.`commitment_id`\n<strong>ORDER</strong> <strong>BY</strong> `p`.`payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/PaymentModel.php:113", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:53", "function": "        App\\Models\\PaymentModel->getPaymentsWithMemberDetails()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/PaymentModel.php:113", "qid": "9ea9178c7e4c467ec2a3cbd04aad230d"}, {"hover": "", "class": "", "duration": "0.76 ms", "sql": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`, `m`.`phone`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `cs`.`balance` &gt; 0\n<strong>AND</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `cs`.`balance` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:334", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:57", "function": "        App\\Models\\CollectionSummaryModel->getMembersWithOutstandingBalances()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:334", "qid": "115f2a471a396fcefe8ce53aa4bafc82"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "0f40cda54f4f4135f077aea0a7e99cd1"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "07ed838327ec7a4a269044d33369275c"}, {"hover": "", "class": "", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "59ec18e28de01596ae8ce782dd69e4ba"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "20aa71f51be004afea5891b1a06228a6"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "7fdce5ab8ae6e2397baf2fdd9e9cfe15"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "a9461452ca6d47eea6c020127ec2b8c8"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "c6c50c5b491129ba4e47d29e4ed9fe43"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "b3bc0457950002d80ef93914fb1b4512"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "59a533950709c9e7e7a2d48895f42dfb"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "3307d63e980c6624d0a7e2c88c2771f1"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "e23bd7ec0a23c55d63aa4a501ea2451a"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "e296ec9f3b509fc70f5829afe27491f2"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "79022d77b11e01d25cc6cba8f147c7cc"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "8e4d71fc30347312ba28b5b0acd0ab21"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "41be1c41d864f1aa515da85bbb4d7fd8"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "6ca0e47601840a947e7acee507329438"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "81b8c56aad5d68bede1a81f6fad0c0d0"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "ce919596b5a6d35d3b23cfad44f977dd"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "2076dca17707398b0c605fd0343169b1"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "d5631dc0e337fe260989872a7790ec97"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "91b3fa83c6196d4f8b63fb025aa8df30"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "0cb95b5583e9aea7d9deff31a466267d"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "17fdac8f239b97428b646a515643c7e9"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "3cce816083871ad189ef5d4ddd9b93d3"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "0b1c08fb30b585b995d7f63a035d8230"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "9f5737da1451758322943a5dda5753a4"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "9ab98b01eb848c300728d1b9230a89d8"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "b4e64b663eee7becde18ecc2a7944897"}, {"hover": "", "class": "", "duration": "0.16 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "8602f17cc2e275b92747302a1b9eff5b"}, {"hover": "", "class": "", "duration": "0.18 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "90a3e013bba31d844cc324cd4d385624"}, {"hover": "", "class": "", "duration": "0.71 ms", "sql": "<strong>SELECT</strong> `m`.*\n<strong>FROM</strong> `members` `m`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>AND</strong> `m`.`member_id` <strong>NOT</strong> <strong>IN</strong> (<strong>SELECT</strong> `c`.`member_id` <strong>FROM</strong> `commitments` `c` <strong>WHERE</strong> `c`.`start_date` &lt;= &#039;2025-05-28&#039; <strong>AND</strong>   ( `c`.`end_date` <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> `c`.`end_date` &gt;= &#039;2025-05-28&#039;  ))", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:993", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:75", "function": "        App\\Controllers\\Reports->getMembersWithoutActiveCommitments()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:993", "qid": "48bd3ebbebd555a2f2a1488d5cc9b6c6"}, {"hover": "", "class": "", "duration": "0.78 ms", "sql": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>ORDER</strong> <strong>BY</strong> `m`.`name` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:61", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:79", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetails()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:61", "qid": "cd66d614b525a8b341cee9fc1d8d9748"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> `payment_method`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `payments`\n<strong>GROUP</strong> <strong>BY</strong> `payment_method`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:124", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:124", "qid": "1a6a5a0da70ba6a4406b7e62e0f1681e"}, {"hover": "", "class": "", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:537", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "b6ffadab22bcd9543b2015660b45cbdb"}, {"hover": "", "class": "", "duration": "0.53 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:538", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "59073f04cf33f58212af8be950cd1b64"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.56 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "caa11d9dd5995e44d1277555a0cba99a"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.54 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "88c975518970e4be22f38cbd795560aa"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.65 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "96b707eb128f390d4807b18556e3a5f6"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.52 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "ccb5d51fbd9fe865c6a2052094ae7b49"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "9ad8188c5b1502d1a1883787d45bf68a"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.49 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "703a590e57040773d4402adb6ddfeced"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.45 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "285b7f1758870bc0f78d328eade1bb09"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "c2415fe380902d4dad9b64822ba433b5"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "a93a375b7aa9c95e4c3da4674ae63552"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.43 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "bef8e1da66a546ca772a874b3d7ee8a4"}, {"hover": "", "class": "", "duration": "0.42 ms", "sql": "<strong>SELECT</strong> `p`.`payment_date`, `c`.`start_date`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `p`.`commitment_id` = `c`.`commitment_id`\n<strong>WHERE</strong> `p`.`commitment_id` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `c`.`start_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `p`.`payment_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:588", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:556", "function": "        App\\Controllers\\Reports->calculateAverageMonthsToPayment()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:588", "qid": "77b8be9ec2a816dfca947f782ce5f259"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:559", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:559", "qid": "bf6d6a87a5fadf63769575af7b4bedd9"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `collection_summary`\n<strong>WHERE</strong> `balance` &gt; 0", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:560", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:560", "qid": "d40d1b93dbb06ac24bac99d2e9f044b6"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "609ca9b80cebf8b00b9682782cc3cec2"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.58 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "eb472272a57e46ce34b17c88c79a19d8"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "aa4fe33c998d30dcc02465245daa21ad"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.57 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "e566eddb9b23531f7a5a7667bff57b47"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "f40c21353fa3af8eb1293509f3d42516"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "a3e25de6963dcea0d34bf8bea44483f2"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "dbc8b8e4ece15e5e8e988d279cae4478"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.67 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "791257e07c21989909b37c5b4d7188dd"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.74 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "268d66377a1f2af8526174ec497c89d1"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.77 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "1a90740c62f5350c0d804b5bb0a0081d"}]}, "badgeValue": 100, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.258075, "duration": "0.002791"}, {"name": "Query", "component": "Database", "start": **********.260934, "duration": "0.000282", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`"}, {"name": "Query", "component": "Database", "start": **********.261491, "duration": "0.000249", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.261784, "duration": "0.000519", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `members`"}, {"name": "Query", "component": "Database", "start": **********.262408, "duration": "0.000314", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.262785, "duration": "0.000233", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;"}, {"name": "Query", "component": "Database", "start": **********.263091, "duration": "0.000217", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;"}, {"name": "Query", "component": "Database", "start": **********.263354, "duration": "0.000342", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.263744, "duration": "0.000295", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;"}, {"name": "Query", "component": "Database", "start": **********.264091, "duration": "0.000222", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.264359, "duration": "0.000204", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;"}, {"name": "Query", "component": "Database", "start": **********.264631, "duration": "0.000219", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.264905, "duration": "0.000208", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.265158, "duration": "0.000203", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;"}, {"name": "Query", "component": "Database", "start": **********.265398, "duration": "0.000201", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.265653, "duration": "0.000190", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.26588, "duration": "0.000186", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.266105, "duration": "0.000218", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.266394, "duration": "0.000230", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.266687, "duration": "0.003432", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.270207, "duration": "0.000304", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.270609, "duration": "0.000262", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.270966, "duration": "0.000450", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;"}, {"name": "Query", "component": "Database", "start": **********.271505, "duration": "0.000288", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.271869, "duration": "0.000302", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.272245, "duration": "0.000337", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.272662, "duration": "0.000265", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.272982, "duration": "0.000320", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.273385, "duration": "0.000244", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.273711, "duration": "0.000290", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.274106, "duration": "0.000322", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.27451, "duration": "0.000263", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.274879, "duration": "0.000341", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.275285, "duration": "0.000264", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.275625, "duration": "0.000288", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.27598, "duration": "0.000302", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.276364, "duration": "0.000713", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.277137, "duration": "0.000258", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.277461, "duration": "0.000224", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.277769, "duration": "0.000187", "query": "<strong>SELECT</strong> <strong>SUM</strong>(`amount`) <strong>AS</strong> `amount`\n<strong>FROM</strong> `payments`\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.278011, "duration": "0.000240", "query": "<strong>SELECT</strong> <strong>SUM</strong>(`balance`) <strong>AS</strong> `balance`\n<strong>FROM</strong> `collection_summary`\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.278445, "duration": "0.001929", "query": "<strong>SELECT</strong> `p`.*, `m`.`name` as `member_name`, `c`.`amount` as `commitment_amount`, `c`.`frequency` as `commitment_frequency`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `p`.`member_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `c`.`commitment_id` = `p`.`commitment_id`\n<strong>ORDER</strong> <strong>BY</strong> `p`.`payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.280689, "duration": "0.000757", "query": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`, `m`.`phone`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `cs`.`balance` &gt; 0\n<strong>AND</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `cs`.`balance` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.281553, "duration": "0.000206", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.281839, "duration": "0.000264", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.282203, "duration": "0.000602", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.282911, "duration": "0.000372", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.28335, "duration": "0.000332", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.283737, "duration": "0.000266", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.284069, "duration": "0.000190", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.284326, "duration": "0.000226", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.284603, "duration": "0.000287", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.284966, "duration": "0.000236", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.28529, "duration": "0.000224", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.285588, "duration": "0.000241", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.285907, "duration": "0.000245", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.286236, "duration": "0.000170", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.286478, "duration": "0.000202", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.286752, "duration": "0.000211", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.287036, "duration": "0.000254", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;"}, {"name": "Query", "component": "Database", "start": **********.287363, "duration": "0.000205", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.287643, "duration": "0.000348", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.288143, "duration": "0.000248", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.288484, "duration": "0.000190", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.288743, "duration": "0.000196", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;"}, {"name": "Query", "component": "Database", "start": **********.289007, "duration": "0.000262", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.28934, "duration": "0.000216", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.289635, "duration": "0.000199", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.289909, "duration": "0.000199", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.2902, "duration": "0.000190", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.290444, "duration": "0.000189", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.290684, "duration": "0.000164", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.290899, "duration": "0.000178", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.29127, "duration": "0.000710", "query": "<strong>SELECT</strong> `m`.*\n<strong>FROM</strong> `members` `m`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>AND</strong> `m`.`member_id` <strong>NOT</strong> <strong>IN</strong> (<strong>SELECT</strong> `c`.`member_id` <strong>FROM</strong> `commitments` `c` <strong>WHERE</strong> `c`.`start_date` &lt;= &#039;2025-05-28&#039; <strong>AND</strong>   ( `c`.`end_date` <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> `c`.`end_date` &gt;= &#039;2025-05-28&#039;  ))"}, {"name": "Query", "component": "Database", "start": **********.292111, "duration": "0.000783", "query": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>ORDER</strong> <strong>BY</strong> `m`.`name` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.293027, "duration": "0.000334", "query": "<strong>SELECT</strong> `payment_method`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `payments`\n<strong>GROUP</strong> <strong>BY</strong> `payment_method`"}, {"name": "Query", "component": "Database", "start": **********.293412, "duration": "0.000552", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.294251, "duration": "0.000526", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.294908, "duration": "0.000555", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.295709, "duration": "0.000544", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.296362, "duration": "0.000646", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.297292, "duration": "0.000522", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.29795, "duration": "0.000485", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.298611, "duration": "0.000485", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.299194, "duration": "0.000454", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.299885, "duration": "0.000501", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.300492, "duration": "0.000397", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.301167, "duration": "0.000430", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.301854, "duration": "0.000415", "query": "<strong>SELECT</strong> `p`.`payment_date`, `c`.`start_date`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `p`.`commitment_id` = `c`.`commitment_id`\n<strong>WHERE</strong> `p`.`commitment_id` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `c`.`start_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `p`.`payment_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.302508, "duration": "0.000270", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.302849, "duration": "0.000281", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `collection_summary`\n<strong>WHERE</strong> `balance` &gt; 0"}, {"name": "Query", "component": "Database", "start": **********.303214, "duration": "0.000554", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.30399, "duration": "0.000583", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.3047, "duration": "0.000603", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.305621, "duration": "0.000568", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.306313, "duration": "0.000601", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.307239, "duration": "0.000553", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.307923, "duration": "0.000597", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.308693, "duration": "0.000668", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.309511, "duration": "0.000744", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.310759, "duration": "0.000773", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: layout/main.php", "component": "Views", "start": **********.338661, "duration": 0.00030994415283203125}, {"name": "View: reports/dashboard.php", "component": "Views", "start": **********.337994, "duration": 0.0010068416595458984}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 162 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Performance.php", "name": "Performance.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Controllers/Reports.php", "name": "Reports.php"}, {"path": "APPPATH/Filters/AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH/Filters/PerformanceFilter.php", "name": "PerformanceFilter.php"}, {"path": "APPPATH/Helpers/currency_helper.php", "name": "currency_helper.php"}, {"path": "APPPATH/Models/CollectionSummaryModel.php", "name": "CollectionSummaryModel.php"}, {"path": "APPPATH/Models/CommitmentModel.php", "name": "CommitmentModel.php"}, {"path": "APPPATH/Models/MemberModel.php", "name": "MemberModel.php"}, {"path": "APPPATH/Models/PaymentModel.php", "name": "PaymentModel.php"}, {"path": "APPPATH/Views/layout/main.php", "name": "main.php"}, {"path": "APPPATH/Views/reports/dashboard.php", "name": "dashboard.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "FCPATH/vendor/autoload.php", "name": "autoload.php"}, {"path": "FCPATH/vendor/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "FCPATH/vendor/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "FCPATH/vendor/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "FCPATH/vendor/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "FCPATH/vendor/composer/installed.php", "name": "installed.php"}, {"path": "FCPATH/vendor/composer/platform_check.php", "name": "platform_check.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "FCPATH/vendor/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}]}, "badgeValue": 162, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Reports", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::index"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/change-password", "handler": "\\App\\Controllers\\Auth::changePassword"}, {"method": "GET", "route": "members", "handler": "\\App\\Controllers\\Members::index"}, {"method": "GET", "route": "members/new", "handler": "\\App\\Controllers\\Members::new"}, {"method": "GET", "route": "members/show/([0-9]+)", "handler": "\\App\\Controllers\\Members::show/$1"}, {"method": "GET", "route": "members/edit/([0-9]+)", "handler": "\\App\\Controllers\\Members::edit/$1"}, {"method": "GET", "route": "members/delete/([0-9]+)", "handler": "\\App\\Controllers\\Members::delete/$1"}, {"method": "GET", "route": "commitments", "handler": "\\App\\Controllers\\Commitments::index"}, {"method": "GET", "route": "commitments/new", "handler": "\\App\\Controllers\\Commitments::new"}, {"method": "GET", "route": "commitments/create/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::new/$1"}, {"method": "GET", "route": "commitments/show/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::show/$1"}, {"method": "GET", "route": "commitments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::edit/$1"}, {"method": "GET", "route": "commitments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::delete/$1"}, {"method": "GET", "route": "commitments/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::memberCommitments/$1"}, {"method": "GET", "route": "payments", "handler": "\\App\\Controllers\\Payments::index"}, {"method": "GET", "route": "payments/new", "handler": "\\App\\Controllers\\Payments::new"}, {"method": "GET", "route": "payments/show/([0-9]+)", "handler": "\\App\\Controllers\\Payments::show/$1"}, {"method": "GET", "route": "payments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Payments::edit/$1"}, {"method": "GET", "route": "payments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Payments::delete/$1"}, {"method": "GET", "route": "payments/member-payments/([0-9]+)", "handler": "\\App\\Controllers\\Payments::memberPayments/$1"}, {"method": "GET", "route": "payments/generate-receipt/([0-9]+)", "handler": "\\App\\Controllers\\Payments::generateReceipt/$1"}, {"method": "GET", "route": "reports", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/collection-summary", "handler": "\\App\\Controllers\\Reports::collectionSummary"}, {"method": "GET", "route": "reports/outstanding-balances", "handler": "\\App\\Controllers\\Reports::outstandingBalances"}, {"method": "GET", "route": "reports/payment-history", "handler": "\\App\\Controllers\\Reports::paymentHistory"}, {"method": "GET", "route": "reports/commitment-report", "handler": "\\App\\Controllers\\Reports::commitmentReport"}, {"method": "GET", "route": "reports/statistics", "handler": "\\App\\Controllers\\Reports::statistics"}, {"method": "GET", "route": "reports/recalculate-all-summaries", "handler": "\\App\\Controllers\\Reports::recalculateAllSummaries"}, {"method": "GET", "route": "reports/members-without-commitments", "handler": "\\App\\Controllers\\Reports::membersWithoutCommitments"}, {"method": "GET", "route": "api/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Api::getMemberCommitments/$1"}, {"method": "GET", "route": "api/payment-details/([0-9]+)/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1/$2"}, {"method": "GET", "route": "api/payment-details/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "auth/update-password", "handler": "\\App\\Controllers\\Auth::updatePassword"}, {"method": "POST", "route": "members/create", "handler": "\\App\\Controllers\\Members::create"}, {"method": "POST", "route": "members/update/([0-9]+)", "handler": "\\App\\Controllers\\Members::update/$1"}, {"method": "POST", "route": "commitments/create", "handler": "\\App\\Controllers\\Commitments::create"}, {"method": "POST", "route": "commitments/update/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::update/$1"}, {"method": "POST", "route": "payments/create", "handler": "\\App\\Controllers\\Payments::create"}, {"method": "POST", "route": "payments/update/([0-9]+)", "handler": "\\App\\Controllers\\Payments::update/$1"}]}, "badgeValue": 35, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "0.33", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "1.39", "count": 142}}}, "badgeValue": 143, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.25524, "duration": 0.00033092498779296875}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.261219, "duration": 4.38690185546875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.261741, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.262304, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.262723, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.263019, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.263309, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.263697, "duration": 6.198883056640625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.26404, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.264314, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.264564, "duration": 2.5987625122070312e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.264852, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.265114, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.265362, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.265599, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.265844, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.266067, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.266325, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.266625, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.270121, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.270513, "duration": 2.002716064453125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.270873, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.271418, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.271796, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.272173, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.272584, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.272929, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.273303, "duration": 2.9087066650390625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.273631, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.274003, "duration": 1.2874603271484375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.27443, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.274774, "duration": 3.0994415283203125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.275222, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.275551, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.275915, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.276284, "duration": 2.09808349609375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.277079, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.277396, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.277687, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.277958, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.278253, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.280376, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.281448, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.28176, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.282106, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.282807, "duration": 2.384185791015625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.283285, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.283683, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.284005, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.284261, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.284553, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.284892, "duration": 2.5987625122070312e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.285204, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.285516, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.285831, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.286153, "duration": 1.8835067749023438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.286408, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.286682, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.286965, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.287291, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.28757, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.287993, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.288392, "duration": 3.0994415283203125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.288676, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.28894, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.289271, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.289558, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.289836, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.29011, "duration": 2.6941299438476562e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.290391, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.290634, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.290849, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.291078, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.291981, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.292896, "duration": 3.0994415283203125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.293362, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.293966, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.294779, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.295465, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.296255, "duration": 1.8835067749023438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.29701, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.297815, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.298437, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.299098, "duration": 1.9073486328125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.29965, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.300387, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.300891, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.301599, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.302271, "duration": 2.1219253540039062e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.30278, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.303131, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.30377, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.304574, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.305305, "duration": 2.5987625122070312e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.306191, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.306916, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.307794, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.308522, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.309363, "duration": 2.9087066650390625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.310267, "duration": 5.793571472167969e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.311543, "duration": 5.1021575927734375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.312412, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.313223, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.313815, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.314559, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.315958, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.316915, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.317536, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.318232, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.318733, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.319424, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.319964, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.320708, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.321291, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.321946, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.322543, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.323368, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.323973, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.324736, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.3256, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.326329, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.327156, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.327971, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.328708, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.329399, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.330423, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.331034, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.331669, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.332468, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.333222, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.334002, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.334342, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.334638, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.334919, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.335236, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.33554, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.335857, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.336185, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.336473, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.336747, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.337024, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.337302, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.337564, "duration": 1.9073486328125e-06}]}], "vars": {"varData": {"View Data": {"title": "Dashboard", "totalMembers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 35</dt></dl></div>", "activeMembers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 35</dt></dl></div>", "totalCommitments": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>double</var> 126800</dt></dl></div>", "totalPayments": "20190.00", "totalOutstanding": "106610.00", "recentPayments": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>payment_id</th><th>member_id</th><th>commitment_id</th><th>amount</th><th>payment_date</th><th>receipt_number</th><th>receipt_book_number</th><th>payment_method</th><th>notes</th><th>payment_periods</th><th>created_at</th><th>updated_at</th><th>member_name</th><th>commitment_amount</th><th>commitment_frequency</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">2</td><td title=\"string (1)\">1</td><td title=\"string (1)\">7</td><td title=\"string (6)\">300.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050002</td><td title=\"string (1)\">1</td><td title=\"string (13)\">bank_transfer</td><td title=\"string (55)\">Payment for: October 2024, November 2024, December 2024</td><td title=\"string (31)\">[\"2024-10\",\"2024-11\",\"2024-12\"]</td><td title=\"string (19)\">2025-05-24 13:25:21</td><td title=\"string (19)\">2025-05-24 13:25:21</td><td title=\"string (14)\">Abdu Raheem KK</td><td title=\"string (6)\">100.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>1</th><td title=\"string (2)\">58</td><td title=\"string (2)\">15</td><td title=\"string (2)\">29</td><td title=\"string (7)\">4730.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050005</td><td title=\"string (1)\">4</td><td title=\"string (5)\">check</td><td title=\"string (155)\">Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, JuUTF-8</td><td title=\"string (111)\">[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"202UTF-8</td><td title=\"string (19)\">2025-05-24 16:59:14</td><td title=\"string (19)\">2025-05-24 16:59:14</td><td title=\"string (12)\">Adnan Al-Ali</td><td title=\"string (6)\">430.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>2</th><td title=\"string (1)\">1</td><td title=\"string (1)\">3</td><td title=\"string (1)\">4</td><td title=\"string (6)\">100.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050001</td><td title=\"string (1)\">1</td><td title=\"string (4)\">cash</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 12:35:13</td><td title=\"string (19)\">2025-05-24 12:35:13</td><td title=\"string (5)\">Murad</td><td title=\"string (6)\">100.00</td><td title=\"string (8)\">one-time</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"string (1)\">4</td><td title=\"string (1)\">6</td><td title=\"string (6)\">600.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050004</td><td title=\"string (1)\">3</td><td title=\"string (5)\">other</td><td title=\"string (52)\">Payment for: January 2025, February 2025, March 2025</td><td title=\"string (31)\">[\"2025-01\",\"2025-02\",\"2025-03\"]</td><td title=\"string (19)\">2025-05-24 14:13:50</td><td title=\"string (19)\">2025-05-24 14:13:50</td><td title=\"string (5)\">Riyas</td><td title=\"string (6)\">200.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>4</th><td title=\"string (2)\">18</td><td title=\"string (2)\">14</td><td title=\"string (2)\">26</td><td title=\"string (5)\">70.00</td><td title=\"string (10)\">2025-05-19</td><td title=\"string (4)\">8961</td><td title=\"string (4)\">BK02</td><td title=\"string (4)\">cash</td><td title=\"null\"><var>null</var></td><td title=\"string (7)\">2025-03</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (15)\">Ibrahim Al-Saud</td><td title=\"string (5)\">70.00</td><td title=\"string (7)\">monthly</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[0]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"300.00\"<div class=\"access-path\">$value[0]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[0]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050002\"<div class=\"access-path\">$value[0]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (13) \"bank_transfer\"<div class=\"access-path\">$value[0]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (55) \"Payment for: October 2024, November 2024, December 2024\"<div class=\"access-path\">$value[0]['notes']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (31) \"[\"2024-10\",\"2024-11\",\"2024-12\"]\"<div class=\"access-path\">$value[0]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2024-10\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2024-11\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2024-12\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[2]</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 13:25:21\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 13:25:21\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Abdu Raheem KK\"<div class=\"access-path\">$value[0]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[0]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[0]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (2) \"58\"<div class=\"access-path\">$value[1]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[1]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (7) \"4730.00\"<div class=\"access-path\">$value[1]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[1]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050005\"<div class=\"access-path\">$value[1]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (5) \"check\"<div class=\"access-path\">$value[1]['payment_method']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>notes</dfn> =&gt; <var>string</var> (155) \"Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, Jul...<div class=\"access-path\">$value[1]['notes']</div></dt><dd><pre>Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, July 2025, August 2025, September 2025, October 2025, November 2025, December 2025\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (111) \"[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"2025...<div class=\"access-path\">$value[1]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (11)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2025-02\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2025-04\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[2]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>3</dfn> =&gt; <var>string</var> (7) \"2025-05\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[3]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>4</dfn> =&gt; <var>string</var> (7) \"2025-06\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[4]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>5</dfn> =&gt; <var>string</var> (7) \"2025-07\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[5]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>6</dfn> =&gt; <var>string</var> (7) \"2025-08\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[6]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>7</dfn> =&gt; <var>string</var> (7) \"2025-09\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[7]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>8</dfn> =&gt; <var>string</var> (7) \"2025-10\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[8]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>9</dfn> =&gt; <var>string</var> (7) \"2025-11\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[9]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>10</dfn> =&gt; <var>string</var> (7) \"2025-12\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[10]</div></dt></dl></li><li><pre>[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"2025-09\",\"2025-10\",\"2025-11\",\"2025-12\"]\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:59:14\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:59:14\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (12) \"Adnan Al-Ali\"<div class=\"access-path\">$value[1]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"430.00\"<div class=\"access-path\">$value[1]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[1]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[2]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[2]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050001\"<div class=\"access-path\">$value[2]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (4) \"cash\"<div class=\"access-path\">$value[2]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_periods</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['payment_periods']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 12:35:13\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 12:35:13\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Murad\"<div class=\"access-path\">$value[2]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[2]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (8) \"one-time\"<div class=\"access-path\">$value[2]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[3]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"600.00\"<div class=\"access-path\">$value[3]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[3]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050004\"<div class=\"access-path\">$value[3]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (5) \"other\"<div class=\"access-path\">$value[3]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (52) \"Payment for: January 2025, February 2025, March 2025\"<div class=\"access-path\">$value[3]['notes']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (31) \"[\"2025-01\",\"2025-02\",\"2025-03\"]\"<div class=\"access-path\">$value[3]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2025-01\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2025-02\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[2]</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 14:13:50\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 14:13:50\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Riyas\"<div class=\"access-path\">$value[3]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"200.00\"<div class=\"access-path\">$value[3]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[3]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[4]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[4]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[4]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-19\"<div class=\"access-path\">$value[4]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (4) \"8961\"<div class=\"access-path\">$value[4]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (4) \"BK02\"<div class=\"access-path\">$value[4]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (4) \"cash\"<div class=\"access-path\">$value[4]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_periods</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">$value[4]['payment_periods']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Ibrahim Al-Saud\"<div class=\"access-path\">$value[4]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[4]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[4]['commitment_frequency']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "membersWithOutstandingBalances": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>summary_id</th><th>member_id</th><th>total_committed</th><th>total_paid</th><th>balance</th><th>last_payment_date</th><th>last_payment_amount</th><th>created_at</th><th>updated_at</th><th>member_name</th><th>status</th><th>phone</th><th>whatsapp_number</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">28</td><td title=\"string (2)\">28</td><td title=\"string (7)\">9540.00</td><td title=\"string (6)\">340.00</td><td title=\"string (7)\">9200.00</td><td title=\"string (10)\">2024-11-10</td><td title=\"string (6)\">170.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (13)\">Saud Al-Ahmad</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0592485468</td><td title=\"string (10)\">0554246003</td></tr><tr><th>1</th><td title=\"string (2)\">18</td><td title=\"string (2)\">18</td><td title=\"string (7)\">9150.00</td><td title=\"string (6)\">380.00</td><td title=\"string (7)\">8770.00</td><td title=\"string (10)\">2025-05-07</td><td title=\"string (6)\">190.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (15)\">Bilal Al-Waleed</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0515351788</td><td title=\"string (10)\">0522375982</td></tr><tr><th>2</th><td title=\"string (2)\">21</td><td title=\"string (2)\">21</td><td title=\"string (8)\">10020.00</td><td title=\"string (7)\">1480.00</td><td title=\"string (7)\">8540.00</td><td title=\"string (10)\">2025-03-10</td><td title=\"string (6)\">490.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (14)\">Bilal Al-Jamal</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0528141858</td><td title=\"string (10)\">0523298126</td></tr><tr><th>3</th><td title=\"string (2)\">30</td><td title=\"string (2)\">30</td><td title=\"string (7)\">6690.00</td><td title=\"string (4)\">0.00</td><td title=\"string (7)\">6690.00</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (17)\">Ibrahim Al-Hassan</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0549461895</td><td title=\"string (10)\">0582341569</td></tr><tr><th>4</th><td title=\"string (2)\">29</td><td title=\"string (2)\">29</td><td title=\"string (7)\">6100.00</td><td title=\"string (4)\">0.00</td><td title=\"string (7)\">6100.00</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (15)\">Salman Al-Bilal</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0541587438</td><td title=\"string (10)\">0523101499</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[0]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"9540.00\"<div class=\"access-path\">$value[0]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (6) \"340.00\"<div class=\"access-path\">$value[0]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"9200.00\"<div class=\"access-path\">$value[0]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-11-10\"<div class=\"access-path\">$value[0]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"170.00\"<div class=\"access-path\">$value[0]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (13) \"Saud Al-Ahmad\"<div class=\"access-path\">$value[0]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0592485468\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>1988-10-10T11:17:48+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0554246003\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>1987-07-25T21:13:23+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"9150.00\"<div class=\"access-path\">$value[1]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (6) \"380.00\"<div class=\"access-path\">$value[1]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8770.00\"<div class=\"access-path\">$value[1]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-07\"<div class=\"access-path\">$value[1]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"190.00\"<div class=\"access-path\">$value[1]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Bilal Al-Waleed\"<div class=\"access-path\">$value[1]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0515351788\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>1986-05-01T17:16:28+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0522375982\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>1986-07-22T00:26:22+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (8) \"10020.00\"<div class=\"access-path\">$value[2]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (7) \"1480.00\"<div class=\"access-path\">$value[2]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8540.00\"<div class=\"access-path\">$value[2]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-10\"<div class=\"access-path\">$value[2]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"490.00\"<div class=\"access-path\">$value[2]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Bilal Al-Jamal\"<div class=\"access-path\">$value[2]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0528141858\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1986-09-26T18:04:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523298126\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1986-08-01T16:35:26+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"6690.00\"<div class=\"access-path\">$value[3]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[3]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6690.00\"<div class=\"access-path\">$value[3]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (17) \"Ibrahim Al-Hassan\"<div class=\"access-path\">$value[3]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0549461895\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1987-05-31T12:18:15+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0582341569\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1988-06-15T01:32:49+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[4]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"6100.00\"<div class=\"access-path\">$value[4]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[4]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6100.00\"<div class=\"access-path\">$value[4]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Salman Al-Bilal\"<div class=\"access-path\">$value[4]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0541587438\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1987-03-01T08:57:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523101499\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1986-07-30T09:58:19+00:00\n</pre></dd></dl></dd></dl></li></ul></dd></dl></div>", "membersWithoutActiveCommitments": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>member_id</th><th>name</th><th>email</th><th>post_office</th><th>old_reference</th><th>phone</th><th>whatsapp_number</th><th>address</th><th>join_date</th><th>status</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (14)\">Abdu Raheem KK</td><td title=\"string (18)\"><EMAIL></td><td title=\"string (10)\">Karalmanna</td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777125</td><td title=\"string (10)\">9947777125</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2018-01-01</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 22:16:26</td><td title=\"string (19)\">2025-05-24 10:30:28</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (7)\">Subeena</td><td title=\"string (15)\"><EMAIL></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777124</td><td title=\"string (10)\">9605447793</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2025-05-18</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 23:25:30</td><td title=\"string (19)\">2025-05-24 10:30:55</td></tr><tr><th>2</th><td title=\"string (1)\">5</td><td title=\"string (14)\">Omar Al-Nasser</td><td title=\"string (26)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">HIST-C-001</td><td title=\"string (10)\">0537494636</td><td title=\"string (10)\">0572364484</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-11-09</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:20</td><td title=\"string (19)\">2025-05-24 21:58:20</td></tr><tr><th>3</th><td title=\"string (1)\">7</td><td title=\"string (9)\">Zaid Khan</td><td title=\"string (21)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">OLD-A1-002</td><td title=\"string (10)\">0537155671</td><td title=\"string (10)\">0537155671</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-04-02</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td></tr><tr><th>4</th><td title=\"string (2)\">10</td><td title=\"string (15)\">Fahad Al-Faisal</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (15)\">Industrial Area</td><td title=\"string (12)\">PREV-002-005</td><td title=\"string (10)\">0540420476</td><td title=\"string (10)\">0579349018</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-03-14</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Abdu Raheem KK\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (18) \"<EMAIL>\"<div class=\"access-path\">$value[0]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (10) \"Karalmanna\"<div class=\"access-path\">$value[0]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[0]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2018-01-01\"<div class=\"access-path\">$value[0]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 22:16:26\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:28\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (7) \"Subeena\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (15) \"<EMAIL>\"<div class=\"access-path\">$value[1]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777124\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>2285-03-26T07:25:24+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9605447793\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>2274-05-21T03:56:33+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[1]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-05-18\"<div class=\"access-path\">$value[1]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 23:25:30\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:55\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Omar Al-Nasser\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value[2]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[2]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"HIST-C-001\"<div class=\"access-path\">$value[2]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537494636\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1987-01-13T00:03:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0572364484\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1988-02-20T14:08:04+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-11-09\"<div class=\"access-path\">$value[2]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (9) \"Zaid Khan\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (21) \"<EMAIL>\"<div class=\"access-path\">$value[3]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[3]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-A1-002\"<div class=\"access-path\">$value[3]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-04-02\"<div class=\"access-path\">$value[3]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Fahad Al-Faisal\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[4]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (15) \"Industrial Area\"<div class=\"access-path\">$value[4]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (12) \"PREV-002-005\"<div class=\"access-path\">$value[4]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0540420476\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1987-02-15T20:47:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0579349018\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1988-05-11T10:16:58+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-03-14\"<div class=\"access-path\">$value[4]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "startDate": "2024-05-28", "endDate": "2025-05-28", "months": "[&quot;May 2024&quot;,&quot;Jun 2024&quot;,&quot;Jul 2024&quot;,&quot;Aug 2024&quot;,&quot;Sep 2024&quot;,&quot;Oct 2024&quot;,&quot;Nov 2024&quot;,&quot;Dec 2024&quot;,&quot;Jan 2025&quot;,&quot;Feb 2025&quot;,&quot;Mar 2025&quot;,&quot;Apr 2025&quot;,&quot;May 2025&quot;]", "committedAmounts": "[360,2060,3670,3460,5820,6470,7320,8360,9430,8460,8730,9280,10020]", "paidAmounts": "[0,950,450,1230,920,530,790,980,2930,1370,2270,750,1460]", "memberCounts": "[2,7,11,12,17,17,19,23,22,22,20,24,24]", "datePaidAmounts": "[50,0,2000,450,530,1320,900,970,1160,2260,2200,1710,6640]", "fulfillmentRates": "[0,46.1,12.3,35.5,15.8,8.2,10.8,11.7,31.1,16.2,26,8.1,14.6]", "movingAverages": "[50,25,683.********33334,816.6666666666666,993.********33334,766.6666666666666,916.6666666666666,1063.********33333,1010,1463.********33333,1873.********33333,2056.*************,3516.*************]", "memberComplianceData": "{&quot;labels&quot;:[&quot;<PERSON>y Paid (100%)&quot;,&quot;Mostly Paid (75-99%)&quot;,&quot;Partially Paid (25-74%)&quot;,&quot;Minimally Paid (1-24%)&quot;,&quot;No Payments (0%)&quot;],&quot;data&quot;:[4,0,4,15,11],&quot;backgroundColor&quot;:[&quot;#28a745&quot;,&quot;#5cb85c&quot;,&quot;#ffc107&quot;,&quot;#fd7e14&quot;,&quot;#dc3545&quot;]}", "paymentMethodData": "{&quot;labels&quot;:[&quot;Cash&quot;,&quot;Check&quot;,&quot;Bank_transfer&quot;,&quot;Other&quot;],&quot;data&quot;:[55,1,1,1],&quot;backgroundColor&quot;:[&quot;#28a745&quot;,&quot;#6f42c1&quot;,&quot;#6c757d&quot;,&quot;#6c757d&quot;]}", "collectionEfficiencyData": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>currentMonthRate</dfn> =&gt; <var>double</var> 14.6<div class=\"access-path\">$value['currentMonthRate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>ytdRate</dfn> =&gt; <var>double</var> 19.1<div class=\"access-path\">$value['ytdRate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>avgMonths</dfn> =&gt; <var>double</var> 2.8<div class=\"access-path\">$value['avgMonths']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentWithBalance</dfn> =&gt; <var>double</var> 85.7<div class=\"access-path\">$value['percentWithBalance']</div></dt></dl></dd></dl></div>"}}, "session": {"_ci_previous_url": "https://halqa.mazharulirfan.com/reports", "admin_id": "2", "username": "boss", "isLoggedIn": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Accept-Encoding": "br", "Accept-Language": "en-US,en;q=0.9", "Cookie": "_ga=GA1.2.1969489433.1748457976; _gid=GA1.2.22748706.1748457976; _ga_9Q6H0QETRF=GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0; ci_session=7507f0c31e713f64812378ca01415ee3", "Host": "halqa.mazharulirfan.com", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "X-Forwarded-For": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Forwarded-Proto": "https", "X-Real-Ip": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Real-Port": "54239", "X-Forwarded-Port": "443", "X-Port": "443", "X-Lscache": "1", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Site": "none", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;macOS&quot;", "Priority": "u=0, i"}, "cookies": {"_ga": "GA1.2.1969489433.1748457976", "_gid": "GA1.2.22748706.1748457976", "_ga_9Q6H0QETRF": "GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0", "ci_session": "7507f0c31e713f64812378ca01415ee3"}, "request": "HTTPS/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.27", "phpSAPI": "litespeed", "environment": "development", "baseURL": "https://halqa.mazharulirfan.com/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}