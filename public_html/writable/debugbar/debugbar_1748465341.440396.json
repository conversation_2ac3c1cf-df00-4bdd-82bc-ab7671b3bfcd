{"url": "https://halqa.mazharulirfan.com/reports/collection-summary", "method": "GET", "isAJAX": false, "startTime": **********.349844, "totalTime": 69.4, "totalMemory": "1.413", "segmentDuration": 10, "segmentCount": 7, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.350238, "duration": 0.0023288726806640625}, {"name": "Required Before Filters", "component": "Timer", "start": **********.352569, "duration": 0.0007548332214355469}, {"name": "Routing", "component": "Timer", "start": **********.353331, "duration": 0.00047087669372558594}, {"name": "Before Filters", "component": "Timer", "start": **********.353866, "duration": 0.0017290115356445312}, {"name": "Controller", "component": "Timer", "start": **********.355598, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.355599, "duration": 0.0005190372467041016}, {"name": "After Filters", "component": "Timer", "start": **********.419, "duration": 3.314018249511719e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.419064, "duration": 0.00017499923706054688}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(100 total Queries, 100 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.95 ms", "sql": "<strong>SELECT</strong> `member_id`, `name` as `member_name`, `status`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `name` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:89", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:89", "qid": "b1791cb47135ce78cc18ad40452679b0"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "0ef4781b0a870055e9be47f2b5a7763f"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "7fb6972373737a36b0a308e6e7ee0088"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "c312531de7ae866edadc8cf1edaaf596"}, {"hover": "", "class": "", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "f64928cefc9cb42eb2cce3f44a02dc32"}, {"hover": "", "class": "", "duration": "0.39 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "8811dcaebfb7adc0995b2251e2363b1d"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "be8473297834760eb98836a0b4036b75"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "adc6362821729b04850839a7176b3915"}, {"hover": "", "class": "", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "5eb579fe43bc07e0dbea08bc7f191236"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "7938dbc1ff6a1ab2ed56c5fa0a1aedfc"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "8552b1b847bf885ad1b47f04a56b3913"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "d2074506e5ae8a2523fa735ea1e2789b"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "f88e223ce7609b6d586fc5d8ccb7699e"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "5bba23402c07a99b38f8d57c82fd1029"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "b5cfcdd6a3f90459ef895a24a5bf6d43"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "ac8bc270c059fba410ba79bc7c32cc97"}, {"hover": "", "class": "", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "2298d7ee4f47d700cc6c5254c16ca94c"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "9c5b68336b7551203783ae3a7d67bd82"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "fc474606e40fb2b96984e4a2de147564"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "03bd1794680fe0a9a82a28cd418561a9"}, {"hover": "", "class": "", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "015f4af5515f6d02df97e4f791cf412e"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "0384d4bf483d300533615c1f2d199a56"}, {"hover": "", "class": "", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "2fdfae8b828de4fd7b3855efdac179dc"}, {"hover": "", "class": "", "duration": "0.42 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "793b9ccc50f628a6bbc2780fac66964f"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "5e0d666982e593889ba36489c41b7c70"}, {"hover": "", "class": "", "duration": "0.39 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "b23cf92e6cb911c531f0edca883804fc"}, {"hover": "", "class": "", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "1a80c1a093885fe66b4a77aaa76f277b"}, {"hover": "", "class": "", "duration": "0.39 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "43dd79daaae88afa87d717a49412bde6"}, {"hover": "", "class": "", "duration": "0.42 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "3e7b9c3090cda54d45d591eb1e488734"}, {"hover": "", "class": "", "duration": "0.46 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "0cde1f1daf1a85c504e169e58a2466e8"}, {"hover": "", "class": "", "duration": "0.39 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "19849dad52381d0346e08916f84870e1"}, {"hover": "", "class": "", "duration": "0.42 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "86aecc91bde0c8058666e25b7b070bf8"}, {"hover": "", "class": "", "duration": "0.45 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "59ef99eaa2fb673c93579b7f78621ff2"}, {"hover": "", "class": "", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "0c3b260fb448cf7bf628136a4de13011"}, {"hover": "", "class": "", "duration": "0.62 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "2b39242acb8d2ab4b9c7fa7f04e944a1"}, {"hover": "", "class": "", "duration": "0.46 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "b6caef22a330997cf626000464de2ea6"}, {"hover": "", "class": "", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "b7dad37e7ca026a87532ecff33b00e0c"}, {"hover": "", "class": "", "duration": "0.47 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "b064133aa4a28deec076b979e93d6155"}, {"hover": "", "class": "", "duration": "0.43 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "a62f626250bf37ad7df5553285d17ab0"}, {"hover": "", "class": "", "duration": "0.39 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "625c89cb54ce1d010d6130b43500de59"}, {"hover": "", "class": "", "duration": "0.45 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "1c6f5d617d6af3e407f2125a3df46ba6"}, {"hover": "", "class": "", "duration": "0.44 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "74acf59c269b3ac5a786ad9b05daa628"}, {"hover": "", "class": "", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "959619b40134d951bd79736df013a856"}, {"hover": "", "class": "", "duration": "0.45 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "beeef6ab4b8a9b4585bd39c272f99c8f"}, {"hover": "", "class": "", "duration": "0.47 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "e1300491a5f733b75e10e794948e3a50"}, {"hover": "", "class": "", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "c822685392ba6ba16906c44e95e0a495"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "42cc4b2341d7bada0106ef5fa6471075"}, {"hover": "", "class": "", "duration": "0.46 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "d9b1744523c8af1e6379222a320b25bb"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "2e7a75241a96f61140884bdd7f3f308f"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "46214e63dc77ebf8337b61f819b2927d"}, {"hover": "", "class": "", "duration": "0.43 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "d07f9a12b8c48423f0379e45294e97a3"}, {"hover": "", "class": "", "duration": "0.42 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "88dcd846c11b4941d2b2d860af7fe204"}, {"hover": "", "class": "", "duration": "0.43 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "fc0d54e7118ff3c98453fe8cefd87b06"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "4063c8fa00970259950f768663c658e3"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "ec246cedcf83bf13dafc62836fe5bd7e"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "672a1b0564bddf70bdd892a54b91b2a0"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "08728bb68fadce67e018a759de28e546"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "56dc943386f1bd8b3a87d4b5f8c1ca6f"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "9a8079947a0e1f4377b57aaeb9452fb6"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "0bba7b5067e9137eb8d4ff72bc8740a2"}, {"hover": "", "class": "", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "0c307d83b0da9223ba179d0c41a951c8"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "23af51c1f4d530d2172909d9849e5c8e"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "4c2e8e93213cfbe8661e792272ce5c63"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "8f7e3dbf71d83b4f98f1a7ce90f11027"}, {"hover": "", "class": "", "duration": "0.51 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "24e78adc2bf728177413f60b237c82af"}, {"hover": "", "class": "", "duration": "0.68 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "ee2d293241112f3ba49ce038902fa95e"}, {"hover": "", "class": "", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "01cc66cd91b438924b26bc7aaf148918"}, {"hover": "", "class": "", "duration": "0.48 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "d81e3de7d4a44aa56b6030eaac8248cb"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "7c49468f32639b74e4c396618d23daf5"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "ff0961fda7686d2f54b23e6bb5ca1c22"}, {"hover": "", "class": "", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "043b92153720d8e4de5c680f0a2904b8"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "2c8bd569576a9cff9573de27d9d88977"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "527b31f614abdf770f78d3221a2aa6ee"}, {"hover": "", "class": "", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "93ff2c3c7850c88c9ab7677c7e84c751"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "d4c1efdafbbb808d931e277066e3fc16"}, {"hover": "", "class": "", "duration": "0.93 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "43d23200cfff496fe5254bc9a13d87ca"}, {"hover": "", "class": "", "duration": "1.18 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "ea4b20d2f7e16b22be94e804b1aa8ce2"}, {"hover": "", "class": "", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "22feed43bb7d465f284cc60eee568d35"}, {"hover": "", "class": "", "duration": "0.5 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "8aa2f587028e7b23d3cb912e3f0bf660"}, {"hover": "", "class": "", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "3358a25799a4c76d4ffd29ffdb36d702"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "568bb6fa5241b52c96d086934c0a6fe2"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "eb1eea9641dcacba87f3149f0797b212"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "7787d9a3591dd79b2b31e469a9243299"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "264cc2c5f19656544bdf7603b99e1ce5"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "3773be76bf6e3a9472098d7845de8e29"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "5b9b2d817205367ba3f1a160671b0bff"}, {"hover": "", "class": "", "duration": "0.6 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "08ddbefb924d39ca0af9f2afa1f9cad1"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "996207ce076fe328032b2b5406d1e708"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "679e2151a69c8fcfcfcf35b46d7f4c04"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "e76728ef71f255cffbdc6aaa3fea9bc8"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "0f7a7f644e427dd65b4880d96467988a"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "e66c19652f59beafe6a42e0ef247d852"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "c938c7df3f4e5b6311f61727f15a8020"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "0e9fc85c33317e64126cf90b089ad4ed"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "d3e157abcf606580bde3d07ff5b75f48"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "9503a6168210ce7888d6a881891af84b"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "b090df82e0d50fe9465cd72645b6c2c3"}, {"hover": "", "class": "", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:149", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:98", "function": "        App\\Models\\CollectionSummaryModel->calculateCommittedAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:149", "qid": "a046e6b16c6e5ee5a8f11474061bd627"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:208", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:101", "function": "        App\\Models\\CollectionSummaryModel->calculatePaidAmountForDateRange()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:208", "qid": "37d52a175a23ad46ee4fd9896dd75d44"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:108", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:652", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetailsAndDateFilter()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->collectionSummary()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:108", "qid": "56fe99584a882967e2a7351dd63371a2"}]}, "badgeValue": 100, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.356378, "duration": "0.000510"}, {"name": "Query", "component": "Database", "start": **********.356948, "duration": "0.000951", "query": "<strong>SELECT</strong> `member_id`, `name` as `member_name`, `status`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `name` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.358132, "duration": "0.000381", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.358632, "duration": "0.000367", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.359168, "duration": "0.000305", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.359545, "duration": "0.000547", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.360175, "duration": "0.000387", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.360649, "duration": "0.000302", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.361011, "duration": "0.000371", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.361488, "duration": "0.000358", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.361943, "duration": "0.000342", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.362392, "duration": "0.000340", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.362974, "duration": "0.000482", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.363551, "duration": "0.000350", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.363959, "duration": "0.000370", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.364386, "duration": "0.000342", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.364813, "duration": "0.000340", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.365225, "duration": "0.000409", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.365711, "duration": "0.000321", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.366125, "duration": "0.000273", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.366446, "duration": "0.000354", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.366893, "duration": "0.000498", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.367473, "duration": "0.000314", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.367846, "duration": "0.000406", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.368356, "duration": "0.000416", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.368858, "duration": "0.000375", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.369286, "duration": "0.000387", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.36979, "duration": "0.000400", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.370291, "duration": "0.000385", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.370745, "duration": "0.000415", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;"}, {"name": "Query", "component": "Database", "start": **********.371235, "duration": "0.000461", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;"}, {"name": "Query", "component": "Database", "start": **********.37179, "duration": "0.000387", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.372243, "duration": "0.000416", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.37277, "duration": "0.000452", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.373338, "duration": "0.000400", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.373811, "duration": "0.000619", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.37463, "duration": "0.000464", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.375236, "duration": "0.000406", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.375715, "duration": "0.000470", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.376285, "duration": "0.000429", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.376827, "duration": "0.000393", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.377308, "duration": "0.000446", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.377878, "duration": "0.000444", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.378433, "duration": "0.000360", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.378855, "duration": "0.000448", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.379413, "duration": "0.000468", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.379965, "duration": "0.000413", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.380461, "duration": "0.000346", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.380881, "duration": "0.000456", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.381433, "duration": "0.000372", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.381881, "duration": "0.000383", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.382346, "duration": "0.000428", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.382882, "duration": "0.000422", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.383363, "duration": "0.000432", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.383876, "duration": "0.000353", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.38433, "duration": "0.000315", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.384714, "duration": "0.000380", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.385181, "duration": "0.000347", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.385627, "duration": "0.000284", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.385969, "duration": "0.000332", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.386382, "duration": "0.000378", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.386876, "duration": "0.000361", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.387311, "duration": "0.000372", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.387766, "duration": "0.000383", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.388246, "duration": "0.000351", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.388669, "duration": "0.000515", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.389303, "duration": "0.000681", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.390096, "duration": "0.000401", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.390581, "duration": "0.000475", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;"}, {"name": "Query", "component": "Database", "start": **********.391139, "duration": "0.000313", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;"}, {"name": "Query", "component": "Database", "start": **********.39154, "duration": "0.000290", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.391888, "duration": "0.000376", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.392372, "duration": "0.000369", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.392835, "duration": "0.000344", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.393245, "duration": "0.000355", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;"}, {"name": "Query", "component": "Database", "start": **********.393655, "duration": "0.000320", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;"}, {"name": "Query", "component": "Database", "start": **********.394051, "duration": "0.000930", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.395052, "duration": "0.001177", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.396344, "duration": "0.000357", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.396794, "duration": "0.000500", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.397361, "duration": "0.000356", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.397805, "duration": "0.000308", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.39821, "duration": "0.000324", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.398611, "duration": "0.000326", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.399028, "duration": "0.000304", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.3994, "duration": "0.000272", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.399751, "duration": "0.000337", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.400172, "duration": "0.000596", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.400843, "duration": "0.000317", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.401212, "duration": "0.000259", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.40157, "duration": "0.000323", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.401975, "duration": "0.000302", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.402333, "duration": "0.000323", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.402738, "duration": "0.000306", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.403127, "duration": "0.000262", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.403436, "duration": "0.000290", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;"}, {"name": "Query", "component": "Database", "start": **********.403779, "duration": "0.000307", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;"}, {"name": "Query", "component": "Database", "start": **********.40415, "duration": "0.000323", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.404536, "duration": "0.000346", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.40496, "duration": "0.000337", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.405359, "duration": "0.000332", "query": "<strong>SELECT</strong> `payment_date`, `amount`\n<strong>FROM</strong> `payments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;\n<strong>ORDER</strong> <strong>BY</strong> `payment_date` <strong>DESC</strong>"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: layout/main.php", "component": "Views", "start": **********.418568, "duration": 0.0003781318664550781}, {"name": "View: reports/collection_summary.php", "component": "Views", "start": **********.416395, "duration": 0.002577066421508789}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 162 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Performance.php", "name": "Performance.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Controllers/Reports.php", "name": "Reports.php"}, {"path": "APPPATH/Filters/AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH/Filters/PerformanceFilter.php", "name": "PerformanceFilter.php"}, {"path": "APPPATH/Helpers/currency_helper.php", "name": "currency_helper.php"}, {"path": "APPPATH/Models/CollectionSummaryModel.php", "name": "CollectionSummaryModel.php"}, {"path": "APPPATH/Models/CommitmentModel.php", "name": "CommitmentModel.php"}, {"path": "APPPATH/Models/MemberModel.php", "name": "MemberModel.php"}, {"path": "APPPATH/Models/PaymentModel.php", "name": "PaymentModel.php"}, {"path": "APPPATH/Views/layout/main.php", "name": "main.php"}, {"path": "APPPATH/Views/reports/collection_summary.php", "name": "collection_summary.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "FCPATH/vendor/autoload.php", "name": "autoload.php"}, {"path": "FCPATH/vendor/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "FCPATH/vendor/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "FCPATH/vendor/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "FCPATH/vendor/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "FCPATH/vendor/composer/installed.php", "name": "installed.php"}, {"path": "FCPATH/vendor/composer/platform_check.php", "name": "platform_check.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "FCPATH/vendor/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}]}, "badgeValue": 162, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Reports", "method": "collectionSummary", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::index"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/change-password", "handler": "\\App\\Controllers\\Auth::changePassword"}, {"method": "GET", "route": "members", "handler": "\\App\\Controllers\\Members::index"}, {"method": "GET", "route": "members/new", "handler": "\\App\\Controllers\\Members::new"}, {"method": "GET", "route": "members/show/([0-9]+)", "handler": "\\App\\Controllers\\Members::show/$1"}, {"method": "GET", "route": "members/edit/([0-9]+)", "handler": "\\App\\Controllers\\Members::edit/$1"}, {"method": "GET", "route": "members/delete/([0-9]+)", "handler": "\\App\\Controllers\\Members::delete/$1"}, {"method": "GET", "route": "commitments", "handler": "\\App\\Controllers\\Commitments::index"}, {"method": "GET", "route": "commitments/new", "handler": "\\App\\Controllers\\Commitments::new"}, {"method": "GET", "route": "commitments/create/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::new/$1"}, {"method": "GET", "route": "commitments/show/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::show/$1"}, {"method": "GET", "route": "commitments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::edit/$1"}, {"method": "GET", "route": "commitments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::delete/$1"}, {"method": "GET", "route": "commitments/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::memberCommitments/$1"}, {"method": "GET", "route": "payments", "handler": "\\App\\Controllers\\Payments::index"}, {"method": "GET", "route": "payments/new", "handler": "\\App\\Controllers\\Payments::new"}, {"method": "GET", "route": "payments/show/([0-9]+)", "handler": "\\App\\Controllers\\Payments::show/$1"}, {"method": "GET", "route": "payments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Payments::edit/$1"}, {"method": "GET", "route": "payments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Payments::delete/$1"}, {"method": "GET", "route": "payments/member-payments/([0-9]+)", "handler": "\\App\\Controllers\\Payments::memberPayments/$1"}, {"method": "GET", "route": "payments/generate-receipt/([0-9]+)", "handler": "\\App\\Controllers\\Payments::generateReceipt/$1"}, {"method": "GET", "route": "reports", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/collection-summary", "handler": "\\App\\Controllers\\Reports::collectionSummary"}, {"method": "GET", "route": "reports/outstanding-balances", "handler": "\\App\\Controllers\\Reports::outstandingBalances"}, {"method": "GET", "route": "reports/payment-history", "handler": "\\App\\Controllers\\Reports::paymentHistory"}, {"method": "GET", "route": "reports/commitment-report", "handler": "\\App\\Controllers\\Reports::commitmentReport"}, {"method": "GET", "route": "reports/statistics", "handler": "\\App\\Controllers\\Reports::statistics"}, {"method": "GET", "route": "reports/recalculate-all-summaries", "handler": "\\App\\Controllers\\Reports::recalculateAllSummaries"}, {"method": "GET", "route": "reports/members-without-commitments", "handler": "\\App\\Controllers\\Reports::membersWithoutCommitments"}, {"method": "GET", "route": "api/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Api::getMemberCommitments/$1"}, {"method": "GET", "route": "api/payment-details/([0-9]+)/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1/$2"}, {"method": "GET", "route": "api/payment-details/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "auth/update-password", "handler": "\\App\\Controllers\\Auth::updatePassword"}, {"method": "POST", "route": "members/create", "handler": "\\App\\Controllers\\Members::create"}, {"method": "POST", "route": "members/update/([0-9]+)", "handler": "\\App\\Controllers\\Members::update/$1"}, {"method": "POST", "route": "commitments/create", "handler": "\\App\\Controllers\\Commitments::create"}, {"method": "POST", "route": "commitments/update/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::update/$1"}, {"method": "POST", "route": "payments/create", "handler": "\\App\\Controllers\\Payments::create"}, {"method": "POST", "route": "payments/update/([0-9]+)", "handler": "\\App\\Controllers\\Payments::update/$1"}]}, "badgeValue": 35, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "0.69", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "1.14", "count": 134}}}, "badgeValue": 135, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.351875, "duration": 0.0006868839263916016}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.357905, "duration": 2.8133392333984375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.358516, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.359001, "duration": 1.3113021850585938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.359475, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.360094, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.360563, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.360952, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.361384, "duration": 2.002716064453125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.361848, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.362287, "duration": 1.2874603271484375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.362734, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.363458, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.363902, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.364331, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.364729, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.365155, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.365635, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.366033, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.366399, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.366802, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.367393, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.36779, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.368253, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.368773, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.369235, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.369675, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.370192, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.370678, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.371161, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.371698, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.372179, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.372661, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.373224, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.37374, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.374434, "duration": 3.0994415283203125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.375098, "duration": 1.6927719116210938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.375645, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.376187, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.376715, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.377223, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.377756, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.378324, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.378795, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.379305, "duration": 1.3113021850585938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.379883, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.38038, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.380809, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.381338, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.381807, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.382265, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.382775, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.383305, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.383796, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.384231, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.384647, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.385095, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.38553, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.385912, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.386303, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.386762, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.387239, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.387685, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.388151, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.388599, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.389186, "duration": 1.1205673217773438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.389987, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.390499, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.391067, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.391453, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.391831, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.392265, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.392742, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.393181, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.393601, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.393976, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.394983, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.39623, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.396703, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.397296, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.397718, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.398115, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.398536, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.398939, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.399333, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.399675, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.40009, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.400769, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.401161, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.401472, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.401895, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.402278, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.402658, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.403046, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.40339, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.403727, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.404087, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.404474, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.404883, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.405298, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.405692, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.406049, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.406451, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.406806, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.407161, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.407524, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.407859, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.408168, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.408442, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.4087, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.408953, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.409215, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.409474, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.409723, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.409964, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.410258, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.410603, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.410871, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.411128, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.411395, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.411662, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.41192, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.412195, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.412457, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.412716, "duration": 1.1920928955078125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.412976, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.413288, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.413641, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.413942, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.414308, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.414629, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.41494, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.415285, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.415612, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.415912, "duration": 3.0994415283203125e-06}]}], "vars": {"varData": {"View Data": {"title": "Collection Summary", "summaries": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (28)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (28)</li><li>Contents (28)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>member_id</th><th>member_name</th><th>status</th><th>total_committed</th><th>total_paid</th><th>balance</th><th>last_payment_date</th><th>last_payment_amount</th><th>phone</th><th>whatsapp_number</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">13</td><td title=\"string (19)\">Abdullah Al-Mahmoud</td><td title=\"string (6)\">active</td><td title=\"double\">140</td><td title=\"integer\">0</td><td title=\"double\">140</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">0592620160</td><td title=\"string (10)\">0592620160</td></tr><tr><th>1</th><td title=\"string (1)\">6</td><td title=\"string (20)\">Abdulrahman Al-Saeed</td><td title=\"string (6)\">active</td><td title=\"double\">1990</td><td title=\"double\">530</td><td title=\"double\">1460</td><td title=\"string (10)\">2025-03-15</td><td title=\"string (6)\">360.00</td><td title=\"string (10)\">0517446897</td><td title=\"string (10)\">0517446897</td></tr><tr><th>2</th><td title=\"string (2)\">15</td><td title=\"string (12)\">Adnan Al-Ali</td><td title=\"string (6)\">active</td><td title=\"double\">5370</td><td title=\"double\">3070</td><td title=\"double\">2300</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (7)\">4730.00</td><td title=\"string (10)\">0568405834</td><td title=\"string (10)\">0568405834</td></tr><tr><th>3</th><td title=\"string (2)\">12</td><td title=\"string (14)\">Amjad Al-Adnan</td><td title=\"string (6)\">active</td><td title=\"double\">190</td><td title=\"integer\">0</td><td title=\"double\">190</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">0570176974</td><td title=\"string (10)\">0570176974</td></tr><tr><th>4</th><td title=\"string (2)\">31</td><td title=\"string (15)\">Badr Al-Ibrahim</td><td title=\"string (6)\">active</td><td title=\"double\">310</td><td title=\"integer\">0</td><td title=\"double\">310</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">0575605153</td><td title=\"string (10)\">0575605153</td></tr><tr><th>5</th><td title=\"string (2)\">21</td><td title=\"string (14)\">Bilal Al-Jamal</td><td title=\"string (6)\">active</td><td title=\"double\">4060</td><td title=\"double\">490</td><td title=\"double\">3570</td><td title=\"string (10)\">2025-03-10</td><td title=\"string (6)\">490.00</td><td title=\"string (10)\">0528141858</td><td title=\"string (10)\">0523298126</td></tr><tr><th>6</th><td title=\"string (2)\">18</td><td title=\"string (15)\">Bilal Al-Waleed</td><td title=\"string (6)\">active</td><td title=\"double\">3290</td><td title=\"double\">190</td><td title=\"double\">3100</td><td title=\"string (10)\">2025-05-07</td><td title=\"string (6)\">190.00</td><td title=\"string (10)\">0515351788</td><td title=\"string (10)\">0522375982</td></tr><tr><th>7</th><td title=\"string (2)\">32</td><td title=\"string (14)\">Fahad Al-Majid</td><td title=\"string (6)\">active</td><td title=\"double\">810</td><td title=\"double\">410</td><td title=\"double\">400</td><td title=\"string (10)\">2025-01-10</td><td title=\"string (6)\">100.00</td><td title=\"string (10)\">0576162670</td><td title=\"string (10)\">0570066376</td></tr><tr><th>8</th><td title=\"string (2)\">22</td><td title=\"string (16)\">Fahad Al-Mustafa</td><td title=\"string (6)\">active</td><td title=\"double\">1180</td><td title=\"double\">70</td><td title=\"double\">1110</td><td title=\"string (10)\">2025-04-14</td><td title=\"string (5)\">70.00</td><td title=\"string (10)\">0546804117</td><td title=\"string (10)\">0513423634</td></tr><tr><th>9</th><td title=\"string (2)\">26</td><td title=\"string (15)\">Faisal Al-Hamza</td><td title=\"string (6)\">active</td><td title=\"double\">1650</td><td title=\"double\">190</td><td title=\"double\">1460</td><td title=\"string (10)\">2025-02-17</td><td title=\"string (6)\">190.00</td><td title=\"string (10)\">0559259386</td><td title=\"string (10)\">0559259386</td></tr><tr><th>10</th><td title=\"string (1)\">8</td><td title=\"string (17)\">Faisal Al-Mustafa</td><td title=\"string (6)\">active</td><td title=\"double\">1110</td><td title=\"integer\">0</td><td title=\"double\">1110</td><td title=\"string (10)\">2025-05-09</td><td title=\"string (6)\">160.00</td><td title=\"string (10)\">0555562008</td><td title=\"string (10)\">0518167587</td></tr><tr><th>11</th><td title=\"string (2)\">11</td><td title=\"string (16)\">Faisal Al-Salman</td><td title=\"string (6)\">active</td><td title=\"double\">1000</td><td title=\"integer\">0</td><td title=\"double\">1000</td><td title=\"string (10)\">2024-10-31</td><td title=\"string (6)\">450.00</td><td title=\"string (10)\">0538327220</td><td title=\"string (10)\">0543727340</td></tr><tr><th>12</th><td title=\"string (2)\">33</td><td title=\"string (11)\">Faisal Khan</td><td title=\"string (6)\">active</td><td title=\"double\">1200</td><td title=\"double\">600</td><td title=\"double\">600</td><td title=\"string (10)\">2024-11-07</td><td title=\"string (6)\">300.00</td><td title=\"string (10)\">0526623146</td><td title=\"string (10)\">0526917902</td></tr><tr><th>13</th><td title=\"string (2)\">34</td><td title=\"string (15)\">Hassan Al-Ahmad</td><td title=\"string (6)\">active</td><td title=\"double\">2200</td><td title=\"double\">130</td><td title=\"double\">2070</td><td title=\"string (10)\">2025-03-09</td><td title=\"string (6)\">130.00</td><td title=\"string (10)\">0516419744</td><td title=\"string (10)\">0582425147</td></tr><tr><th>14</th><td title=\"string (2)\">30</td><td title=\"string (17)\">Ibrahim Al-Hassan</td><td title=\"string (6)\">active</td><td title=\"double\">2170</td><td title=\"integer\">0</td><td title=\"double\">2170</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">0549461895</td><td title=\"string (10)\">0582341569</td></tr><tr><th>15</th><td title=\"string (2)\">20</td><td title=\"string (18)\">Ibrahim Al-Ibrahim</td><td title=\"string (6)\">active</td><td title=\"double\">1600</td><td title=\"integer\">0</td><td title=\"double\">1600</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">0529224145</td><td title=\"string (10)\">0529224145</td></tr><tr><th>16</th><td title=\"string (2)\">14</td><td title=\"string (15)\">Ibrahim Al-Saud</td><td title=\"string (6)\">active</td><td title=\"double\">1150</td><td title=\"double\">300</td><td title=\"double\">850</td><td title=\"string (10)\">2025-05-19</td><td title=\"string (5)\">70.00</td><td title=\"string (10)\">0547398070</td><td title=\"string (10)\">0515678096</td></tr><tr><th>17</th><td title=\"string (2)\">25</td><td title=\"string (16)\">Ismail Al-Waleed</td><td title=\"string (6)\">active</td><td title=\"double\">970</td><td title=\"double\">500</td><td title=\"double\">470</td><td title=\"string (10)\">2024-07-25</td><td title=\"string (6)\">500.00</td><td title=\"string (10)\">0595811659</td><td title=\"string (10)\">0570567329</td></tr><tr><th>18</th><td title=\"string (1)\">3</td><td title=\"string (5)\">Murad</td><td title=\"string (6)\">active</td><td title=\"double\">100</td><td title=\"double\">100</td><td title=\"double\">0</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (6)\">100.00</td><td title=\"string (9)\">996677889</td><td title=\"string (0)\"></td></tr><tr><th>19</th><td title=\"string (2)\">16</td><td title=\"string (15)\">Omar Al-Mustafa</td><td title=\"string (6)\">active</td><td title=\"double\">3950</td><td title=\"double\">430</td><td title=\"double\">3520</td><td title=\"string (10)\">2025-02-02</td><td title=\"string (6)\">430.00</td><td title=\"string (10)\">0527775406</td><td title=\"string (10)\">0527775406</td></tr><tr><th>20</th><td title=\"string (2)\">24</td><td title=\"string (16)\">Rashid Al-Rashid</td><td title=\"string (6)\">active</td><td title=\"double\">2170</td><td title=\"integer\">0</td><td title=\"double\">2170</td><td title=\"string (10)\">2025-04-10</td><td title=\"string (6)\">490.00</td><td title=\"string (10)\">0513240716</td><td title=\"string (10)\">0556112667</td></tr><tr><th>21</th><td title=\"string (1)\">4</td><td title=\"string (5)\">Riyas</td><td title=\"string (6)\">active</td><td title=\"double\">1000</td><td title=\"double\">600</td><td title=\"double\">400</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (6)\">600.00</td><td title=\"string (10)\">9947777100</td><td title=\"string (10)\">9947777100</td></tr><tr><th>22</th><td title=\"string (2)\">29</td><td title=\"string (15)\">Salman Al-Bilal</td><td title=\"string (6)\">active</td><td title=\"double\">2650</td><td title=\"integer\">0</td><td title=\"double\">2650</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">0541587438</td><td title=\"string (10)\">0523101499</td></tr><tr><th>23</th><td title=\"string (2)\">27</td><td title=\"string (16)\">Salman Al-Salman</td><td title=\"string (6)\">active</td><td title=\"double\">1210</td><td title=\"double\">840</td><td title=\"double\">370</td><td title=\"string (10)\">2025-05-08</td><td title=\"string (5)\">60.00</td><td title=\"string (10)\">0582001879</td><td title=\"string (10)\">0568616961</td></tr><tr><th>24</th><td title=\"string (2)\">28</td><td title=\"string (13)\">Saud Al-Ahmad</td><td title=\"string (6)\">active</td><td title=\"double\">2680</td><td title=\"integer\">0</td><td title=\"double\">2680</td><td title=\"string (10)\">2024-11-10</td><td title=\"string (6)\">170.00</td><td title=\"string (10)\">0592485468</td><td title=\"string (10)\">0554246003</td></tr><tr><th>25</th><td title=\"string (2)\">17</td><td title=\"string (13)\">Saud Al-Bilal</td><td title=\"string (6)\">active</td><td title=\"double\">260</td><td title=\"integer\">0</td><td title=\"double\">260</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">0561548613</td><td title=\"string (10)\">0561548613</td></tr><tr><th>26</th><td title=\"string (1)\">9</td><td title=\"string (15)\">Tariq Al-Salman</td><td title=\"string (6)\">active</td><td title=\"double\">460</td><td title=\"integer\">0</td><td title=\"double\">460</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">0567188437</td><td title=\"string (10)\">0567188437</td></tr><tr><th>27</th><td title=\"string (2)\">19</td><td title=\"string (15)\">Waleed Al-Adnan</td><td title=\"string (6)\">active</td><td title=\"double\">1050</td><td title=\"integer\">0</td><td title=\"double\">1050</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">0511853916</td><td title=\"string (10)\">0565829914</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (19) \"Abdullah Al-Mahmoud\"<div class=\"access-path\">$value[0]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 140<div class=\"access-path\">$value[0]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[0]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 140<div class=\"access-path\">$value[0]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0592620160\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>1988-10-12T00:42:40+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0592620160\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>1988-10-12T00:42:40+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (20) \"Abdulrahman Al-Saeed\"<div class=\"access-path\">$value[1]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1990<div class=\"access-path\">$value[1]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 530<div class=\"access-path\">$value[1]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 1460<div class=\"access-path\">$value[1]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-15\"<div class=\"access-path\">$value[1]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"360.00\"<div class=\"access-path\">$value[1]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0517446897\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>1986-05-25T23:14:57+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0517446897\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>1986-05-25T23:14:57+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (12) \"Adnan Al-Ali\"<div class=\"access-path\">$value[2]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 5370<div class=\"access-path\">$value[2]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 3070<div class=\"access-path\">$value[2]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 2300<div class=\"access-path\">$value[2]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[2]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (7) \"4730.00\"<div class=\"access-path\">$value[2]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0568405834\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1988-01-05T18:30:34+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0568405834\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1988-01-05T18:30:34+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Amjad Al-Adnan\"<div class=\"access-path\">$value[3]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 190<div class=\"access-path\">$value[3]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[3]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 190<div class=\"access-path\">$value[3]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0570176974\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1988-01-26T06:29:34+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0570176974\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1988-01-26T06:29:34+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"31\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Badr Al-Ibrahim\"<div class=\"access-path\">$value[4]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 310<div class=\"access-path\">$value[4]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[4]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 310<div class=\"access-path\">$value[4]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0575605153\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1988-03-29T02:19:13+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0575605153\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1988-03-29T02:19:13+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[5]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Bilal Al-Jamal\"<div class=\"access-path\">$value[5]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 4060<div class=\"access-path\">$value[5]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 490<div class=\"access-path\">$value[5]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 3570<div class=\"access-path\">$value[5]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-10\"<div class=\"access-path\">$value[5]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"490.00\"<div class=\"access-path\">$value[5]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0528141858\"<div class=\"access-path\">$value[5]['phone']</div></dt><dd><pre>1986-09-26T18:04:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523298126\"<div class=\"access-path\">$value[5]['whatsapp_number']</div></dt><dd><pre>1986-08-01T16:35:26+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[6]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Bilal Al-Waleed\"<div class=\"access-path\">$value[6]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[6]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 3290<div class=\"access-path\">$value[6]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 190<div class=\"access-path\">$value[6]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 3100<div class=\"access-path\">$value[6]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-07\"<div class=\"access-path\">$value[6]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"190.00\"<div class=\"access-path\">$value[6]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0515351788\"<div class=\"access-path\">$value[6]['phone']</div></dt><dd><pre>1986-05-01T17:16:28+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0522375982\"<div class=\"access-path\">$value[6]['whatsapp_number']</div></dt><dd><pre>1986-07-22T00:26:22+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"32\"<div class=\"access-path\">$value[7]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Fahad Al-Majid\"<div class=\"access-path\">$value[7]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[7]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 810<div class=\"access-path\">$value[7]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 410<div class=\"access-path\">$value[7]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 400<div class=\"access-path\">$value[7]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-01-10\"<div class=\"access-path\">$value[7]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[7]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0576162670\"<div class=\"access-path\">$value[7]['phone']</div></dt><dd><pre>1988-04-04T13:11:10+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0570066376\"<div class=\"access-path\">$value[7]['whatsapp_number']</div></dt><dd><pre>1988-01-24T23:46:16+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[8]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (16) \"Fahad Al-Mustafa\"<div class=\"access-path\">$value[8]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[8]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1180<div class=\"access-path\">$value[8]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 70<div class=\"access-path\">$value[8]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 1110<div class=\"access-path\">$value[8]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-04-14\"<div class=\"access-path\">$value[8]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[8]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0546804117\"<div class=\"access-path\">$value[8]['phone']</div></dt><dd><pre>1987-04-30T18:01:57+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0513423634\"<div class=\"access-path\">$value[8]['whatsapp_number']</div></dt><dd><pre>1986-04-09T09:40:34+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[9]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Faisal Al-Hamza\"<div class=\"access-path\">$value[9]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[9]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1650<div class=\"access-path\">$value[9]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 190<div class=\"access-path\">$value[9]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 1460<div class=\"access-path\">$value[9]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-02-17\"<div class=\"access-path\">$value[9]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"190.00\"<div class=\"access-path\">$value[9]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0559259386\"<div class=\"access-path\">$value[9]['phone']</div></dt><dd><pre>1987-09-21T21:49:46+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0559259386\"<div class=\"access-path\">$value[9]['whatsapp_number']</div></dt><dd><pre>1987-09-21T21:49:46+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[10]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (17) \"Faisal Al-Mustafa\"<div class=\"access-path\">$value[10]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[10]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1110<div class=\"access-path\">$value[10]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[10]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 1110<div class=\"access-path\">$value[10]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-09\"<div class=\"access-path\">$value[10]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"160.00\"<div class=\"access-path\">$value[10]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0555562008\"<div class=\"access-path\">$value[10]['phone']</div></dt><dd><pre>1987-08-10T02:46:48+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0518167587\"<div class=\"access-path\">$value[10]['whatsapp_number']</div></dt><dd><pre>1986-06-03T07:26:27+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[11]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (16) \"Faisal Al-Salman\"<div class=\"access-path\">$value[11]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[11]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1000<div class=\"access-path\">$value[11]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[11]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 1000<div class=\"access-path\">$value[11]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-10-31\"<div class=\"access-path\">$value[11]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"450.00\"<div class=\"access-path\">$value[11]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0538327220\"<div class=\"access-path\">$value[11]['phone']</div></dt><dd><pre>1987-01-22T15:20:20+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0543727340\"<div class=\"access-path\">$value[11]['whatsapp_number']</div></dt><dd><pre>1987-03-26T03:22:20+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"33\"<div class=\"access-path\">$value[12]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (11) \"Faisal Khan\"<div class=\"access-path\">$value[12]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[12]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1200<div class=\"access-path\">$value[12]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 600<div class=\"access-path\">$value[12]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 600<div class=\"access-path\">$value[12]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-11-07\"<div class=\"access-path\">$value[12]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"300.00\"<div class=\"access-path\">$value[12]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0526623146\"<div class=\"access-path\">$value[12]['phone']</div></dt><dd><pre>1986-09-09T04:12:26+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0526917902\"<div class=\"access-path\">$value[12]['whatsapp_number']</div></dt><dd><pre>1986-09-12T14:05:02+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"34\"<div class=\"access-path\">$value[13]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Hassan Al-Ahmad\"<div class=\"access-path\">$value[13]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[13]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 2200<div class=\"access-path\">$value[13]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 130<div class=\"access-path\">$value[13]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 2070<div class=\"access-path\">$value[13]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-09\"<div class=\"access-path\">$value[13]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"130.00\"<div class=\"access-path\">$value[13]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0516419744\"<div class=\"access-path\">$value[13]['phone']</div></dt><dd><pre>1986-05-14T01:55:44+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0582425147\"<div class=\"access-path\">$value[13]['whatsapp_number']</div></dt><dd><pre>1988-06-16T00:45:47+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[14]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (17) \"Ibrahim Al-Hassan\"<div class=\"access-path\">$value[14]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[14]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 2170<div class=\"access-path\">$value[14]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[14]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 2170<div class=\"access-path\">$value[14]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0549461895\"<div class=\"access-path\">$value[14]['phone']</div></dt><dd><pre>1987-05-31T12:18:15+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0582341569\"<div class=\"access-path\">$value[14]['whatsapp_number']</div></dt><dd><pre>1988-06-15T01:32:49+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[15]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (18) \"Ibrahim Al-Ibrahim\"<div class=\"access-path\">$value[15]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[15]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1600<div class=\"access-path\">$value[15]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[15]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 1600<div class=\"access-path\">$value[15]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[15]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[15]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0529224145\"<div class=\"access-path\">$value[15]['phone']</div></dt><dd><pre>1986-10-09T06:42:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0529224145\"<div class=\"access-path\">$value[15]['whatsapp_number']</div></dt><dd><pre>1986-10-09T06:42:25+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>16</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[16]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[16]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Ibrahim Al-Saud\"<div class=\"access-path\">$value[16]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[16]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1150<div class=\"access-path\">$value[16]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 300<div class=\"access-path\">$value[16]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 850<div class=\"access-path\">$value[16]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-19\"<div class=\"access-path\">$value[16]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[16]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0547398070\"<div class=\"access-path\">$value[16]['phone']</div></dt><dd><pre>1987-05-07T15:01:10+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0515678096\"<div class=\"access-path\">$value[16]['whatsapp_number']</div></dt><dd><pre>1986-05-05T11:54:56+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>17</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[17]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value[17]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (16) \"Ismail Al-Waleed\"<div class=\"access-path\">$value[17]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[17]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 970<div class=\"access-path\">$value[17]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 500<div class=\"access-path\">$value[17]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 470<div class=\"access-path\">$value[17]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-07-25\"<div class=\"access-path\">$value[17]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"500.00\"<div class=\"access-path\">$value[17]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0595811659\"<div class=\"access-path\">$value[17]['phone']</div></dt><dd><pre>1988-11-17T23:14:19+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0570567329\"<div class=\"access-path\">$value[17]['whatsapp_number']</div></dt><dd><pre>1988-01-30T18:55:29+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>18</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[18]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[18]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Murad\"<div class=\"access-path\">$value[18]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[18]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 100<div class=\"access-path\">$value[18]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 100<div class=\"access-path\">$value[18]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 0<div class=\"access-path\">$value[18]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[18]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[18]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (9) \"996677889\"<div class=\"access-path\">$value[18]['phone']</div></dt><dd><pre>2001-08-01T14:58:09+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[18]['whatsapp_number']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>19</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[19]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[19]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Omar Al-Mustafa\"<div class=\"access-path\">$value[19]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[19]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 3950<div class=\"access-path\">$value[19]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 430<div class=\"access-path\">$value[19]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 3520<div class=\"access-path\">$value[19]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-02-02\"<div class=\"access-path\">$value[19]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"430.00\"<div class=\"access-path\">$value[19]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0527775406\"<div class=\"access-path\">$value[19]['phone']</div></dt><dd><pre>1986-09-22T12:16:46+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0527775406\"<div class=\"access-path\">$value[19]['whatsapp_number']</div></dt><dd><pre>1986-09-22T12:16:46+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>20</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[20]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"24\"<div class=\"access-path\">$value[20]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (16) \"Rashid Al-Rashid\"<div class=\"access-path\">$value[20]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[20]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 2170<div class=\"access-path\">$value[20]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[20]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 2170<div class=\"access-path\">$value[20]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-04-10\"<div class=\"access-path\">$value[20]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"490.00\"<div class=\"access-path\">$value[20]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0513240716\"<div class=\"access-path\">$value[20]['phone']</div></dt><dd><pre>1986-04-07T06:51:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0556112667\"<div class=\"access-path\">$value[20]['whatsapp_number']</div></dt><dd><pre>1987-08-16T11:44:27+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>21</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[21]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[21]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Riyas\"<div class=\"access-path\">$value[21]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[21]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1000<div class=\"access-path\">$value[21]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 600<div class=\"access-path\">$value[21]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 400<div class=\"access-path\">$value[21]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[21]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"600.00\"<div class=\"access-path\">$value[21]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777100\"<div class=\"access-path\">$value[21]['phone']</div></dt><dd><pre>2285-03-26T07:25:00+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9947777100\"<div class=\"access-path\">$value[21]['whatsapp_number']</div></dt><dd><pre>2285-03-26T07:25:00+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>22</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[22]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[22]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Salman Al-Bilal\"<div class=\"access-path\">$value[22]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[22]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 2650<div class=\"access-path\">$value[22]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[22]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 2650<div class=\"access-path\">$value[22]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[22]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[22]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0541587438\"<div class=\"access-path\">$value[22]['phone']</div></dt><dd><pre>1987-03-01T08:57:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523101499\"<div class=\"access-path\">$value[22]['whatsapp_number']</div></dt><dd><pre>1986-07-30T09:58:19+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>23</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[23]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"27\"<div class=\"access-path\">$value[23]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (16) \"Salman Al-Salman\"<div class=\"access-path\">$value[23]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[23]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1210<div class=\"access-path\">$value[23]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>double</var> 840<div class=\"access-path\">$value[23]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 370<div class=\"access-path\">$value[23]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-08\"<div class=\"access-path\">$value[23]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (5) \"60.00\"<div class=\"access-path\">$value[23]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0582001879\"<div class=\"access-path\">$value[23]['phone']</div></dt><dd><pre>1988-06-11T03:11:19+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0568616961\"<div class=\"access-path\">$value[23]['whatsapp_number']</div></dt><dd><pre>1988-01-08T05:09:21+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>24</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[24]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[24]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (13) \"Saud Al-Ahmad\"<div class=\"access-path\">$value[24]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[24]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 2680<div class=\"access-path\">$value[24]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[24]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 2680<div class=\"access-path\">$value[24]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-11-10\"<div class=\"access-path\">$value[24]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"170.00\"<div class=\"access-path\">$value[24]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0592485468\"<div class=\"access-path\">$value[24]['phone']</div></dt><dd><pre>1988-10-10T11:17:48+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0554246003\"<div class=\"access-path\">$value[24]['whatsapp_number']</div></dt><dd><pre>1987-07-25T21:13:23+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>25</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[25]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[25]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (13) \"Saud Al-Bilal\"<div class=\"access-path\">$value[25]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[25]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 260<div class=\"access-path\">$value[25]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[25]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 260<div class=\"access-path\">$value[25]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[25]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[25]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0561548613\"<div class=\"access-path\">$value[25]['phone']</div></dt><dd><pre>1987-10-18T09:43:33+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0561548613\"<div class=\"access-path\">$value[25]['whatsapp_number']</div></dt><dd><pre>1987-10-18T09:43:33+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>26</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[26]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[26]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Tariq Al-Salman\"<div class=\"access-path\">$value[26]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[26]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 460<div class=\"access-path\">$value[26]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[26]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 460<div class=\"access-path\">$value[26]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[26]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[26]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0567188437\"<div class=\"access-path\">$value[26]['phone']</div></dt><dd><pre>1987-12-22T16:20:37+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0567188437\"<div class=\"access-path\">$value[26]['whatsapp_number']</div></dt><dd><pre>1987-12-22T16:20:37+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>27</dfn> =&gt; <var>array</var> (10)<div class=\"access-path\">$value[27]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[27]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Waleed Al-Adnan\"<div class=\"access-path\">$value[27]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[27]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>double</var> 1050<div class=\"access-path\">$value[27]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>integer</var> 0<div class=\"access-path\">$value[27]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>double</var> 1050<div class=\"access-path\">$value[27]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[27]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[27]['last_payment_amount']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0511853916\"<div class=\"access-path\">$value[27]['phone']</div></dt><dd><pre>1986-03-22T05:38:36+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0565829914\"<div class=\"access-path\">$value[27]['whatsapp_number']</div></dt><dd><pre>1987-12-06T22:58:34+00:00\n</pre></dd></dl></dd></dl></li></ul></dd></dl></div>", "exclude_zero": "commitments", "from_date": "2025-01-01", "to_date": "2025-05-31"}}, "session": {"_ci_previous_url": "https://halqa.mazharulirfan.com/reports/collection-summary", "admin_id": "2", "username": "boss", "isLoggedIn": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Accept-Encoding": "br", "Accept-Language": "en-US,en;q=0.9", "Cookie": "_ga=GA1.2.1969489433.1748457976; _gid=GA1.2.22748706.1748457976; _ga_9Q6H0QETRF=GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0; ci_session=ff6d48739e278ba41235653988620d29", "Host": "halqa.mazharulirfan.com", "Referer": "https://halqa.mazharulirfan.com/members", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "X-Forwarded-For": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Forwarded-Proto": "https", "X-Real-Ip": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Real-Port": "59350", "X-Forwarded-Port": "443", "X-Port": "443", "X-Lscache": "1", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;macOS&quot;", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Priority": "u=0, i"}, "cookies": {"_ga": "GA1.2.1969489433.1748457976", "_gid": "GA1.2.22748706.1748457976", "_ga_9Q6H0QETRF": "GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0", "ci_session": "ff6d48739e278ba41235653988620d29"}, "request": "HTTPS/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.27", "phpSAPI": "litespeed", "environment": "development", "baseURL": "https://halqa.mazharulirfan.com/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}