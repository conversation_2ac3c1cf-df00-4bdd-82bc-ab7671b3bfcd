{"url": "https://halqa.mazharulirfan.com/reports", "method": "GET", "isAJAX": false, "startTime": **********.69695, "totalTime": 74.1, "totalMemory": "1.548", "segmentDuration": 15, "segmentCount": 5, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.697153, "duration": 0.*****************}, {"name": "Required Before Filters", "component": "Timer", "start": **********.698596, "duration": 0.0005629062652587891}, {"name": "Routing", "component": "Timer", "start": **********.699164, "duration": 0.0003790855407714844}, {"name": "Before Filters", "component": "Timer", "start": **********.699583, "duration": 0.0022859573364257812}, {"name": "Controller", "component": "Timer", "start": **********.701871, "duration": 0.*****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.701872, "duration": 0.003520965576171875}, {"name": "After Filters", "component": "Timer", "start": **********.770878, "duration": 3.695487976074219e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.770946, "duration": 0.00013494491577148438}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(100 total Queries, 81 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.73 ms", "sql": "<strong>SELECT</strong> GET_LOCK(&#039;af3a693d82ee7c46bf98e63400c41915&#039;, 300) <strong>AS</strong> ci_session_lock", "trace": [{"file": "SYSTEMPATH/Session/Handlers/Database/MySQLiHandler.php:31", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Session/Handlers/DatabaseHandler.php:117", "function": "        CodeIgniter\\Session\\Handlers\\Database\\MySQLiHandler->lockSession()", "index": "  2    "}, {"function": "        CodeIgniter\\Session\\Handlers\\DatabaseHandler->read()", "file": "[internal function]", "index": "  3    "}, {"file": "SYSTEMPATH/Session/Session.php:912", "function": "        session_start()", "index": "  4    "}, {"file": "SYSTEMPATH/Session/Session.php:243", "function": "        CodeIgniter\\Session\\Session->startSession()", "index": "  5    "}, {"file": "SYSTEMPATH/Config/Services.php:710", "function": "        CodeIgniter\\Session\\Session->start()", "index": "  6    "}, {"file": "SYSTEMPATH/Config/BaseService.php:312", "function": "        CodeIgniter\\Config\\Services::session()", "index": "  7    "}, {"file": "SYSTEMPATH/Config/BaseService.php:251", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": "  8    "}, {"file": "SYSTEMPATH/Config/Services.php:668", "function": "        CodeIgniter\\Config\\BaseService::getSharedInstance()", "index": "  9    "}, {"file": "SYSTEMPATH/Config/BaseService.php:321", "function": "        CodeIgniter\\Config\\Services::session()", "index": " 10    "}, {"file": "SYSTEMPATH/Config/BaseService.php:202", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": " 11    "}, {"file": "SYSTEMPATH/Common.php:1000", "function": "        CodeIgniter\\Config\\BaseService::get()", "index": " 12    "}, {"file": "SYSTEMPATH/Common.php:973", "function": "        service()", "index": " 13    "}, {"file": "APPPATH/Filters/AuthFilter.php:29", "function": "        session()", "index": " 14    "}, {"file": "SYSTEMPATH/Filters/Filters.php:241", "function": "        App\\Filters\\AuthFilter->before()", "index": " 15    "}, {"file": "SYSTEMPATH/Filters/Filters.php:221", "function": "        CodeIgniter\\Filters\\Filters->runBefore()", "index": " 16    "}, {"file": "SYSTEMPATH/CodeIgniter.php:479", "function": "        CodeIgniter\\Filters\\Filters->run()", "index": " 17    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 18    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 19    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 20    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 21    "}], "trace-file": "[internal function]", "qid": "53a4d65942d0fbc6eb8d101c9e1f0c20"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> `data`\n<strong>FROM</strong> `ci_sessions`\n<strong>WHERE</strong> `id` = &#039;ci_session:bc9bdee29a21a8b2ba71324aa1ce5983&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Session/Handlers/DatabaseHandler.php:135", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"function": "        CodeIgniter\\Session\\Handlers\\DatabaseHandler->read()", "file": "[internal function]", "index": "  3    "}, {"file": "SYSTEMPATH/Session/Session.php:912", "function": "        session_start()", "index": "  4    "}, {"file": "SYSTEMPATH/Session/Session.php:243", "function": "        CodeIgniter\\Session\\Session->startSession()", "index": "  5    "}, {"file": "SYSTEMPATH/Config/Services.php:710", "function": "        CodeIgniter\\Session\\Session->start()", "index": "  6    "}, {"file": "SYSTEMPATH/Config/BaseService.php:312", "function": "        CodeIgniter\\Config\\Services::session()", "index": "  7    "}, {"file": "SYSTEMPATH/Config/BaseService.php:251", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": "  8    "}, {"file": "SYSTEMPATH/Config/Services.php:668", "function": "        CodeIgniter\\Config\\BaseService::getSharedInstance()", "index": "  9    "}, {"file": "SYSTEMPATH/Config/BaseService.php:321", "function": "        CodeIgniter\\Config\\Services::session()", "index": " 10    "}, {"file": "SYSTEMPATH/Config/BaseService.php:202", "function": "        CodeIgniter\\Config\\BaseService::__callStatic()", "index": " 11    "}, {"file": "SYSTEMPATH/Common.php:1000", "function": "        CodeIgniter\\Config\\BaseService::get()", "index": " 12    "}, {"file": "SYSTEMPATH/Common.php:973", "function": "        service()", "index": " 13    "}, {"file": "APPPATH/Filters/AuthFilter.php:29", "function": "        session()", "index": " 14    "}, {"file": "SYSTEMPATH/Filters/Filters.php:241", "function": "        App\\Filters\\AuthFilter->before()", "index": " 15    "}, {"file": "SYSTEMPATH/Filters/Filters.php:221", "function": "        CodeIgniter\\Filters\\Filters->runBefore()", "index": " 16    "}, {"file": "SYSTEMPATH/CodeIgniter.php:479", "function": "        CodeIgniter\\Filters\\Filters->run()", "index": " 17    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": " 18    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": " 19    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 20    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 21    "}], "trace-file": "[internal function]", "qid": "f6c29e1aaca43ab77e7dc61d034af8b4"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:36", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:36", "qid": "9a96e445ceac23f0bbb0589ec735acc9"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:37", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:37", "qid": "1a9d37043606315469c48c4f4bc3182d"}, {"hover": "", "class": "", "duration": "0.45 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `members`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:41", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:41", "qid": "b52554e2f83ecabd7e8bb2da96c18e67"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "fd99f2b1d75d79e4821b3553a17147bb"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "fb1459ffaf9fa27456f9677d1bdb475e"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "d6c30b5a9634a3a639fb84874b4f953c"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "047d6d84a4382bf70bf8f92dc41d1d8f"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "f0482737251310d74a99308035618291"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "2d7b7f9dd619534cd1f7e0a4ac997969"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "4ca35d63b5e1f8e4efe70c068955bdc3"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "d75beb0c41a1da8fe9a4cc2cd7993c78"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "ef71b0a2d14637b2c437d625a8c64ba0"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "4ec81a23cafd79722d81c1553c98a8a9"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "ba42eaa367d6ad91efda5c7ed6cb5724"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "d83315ff060030e01d0598e6bf98dfc8"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "1450c8f5032227efd3a699d9c991b761"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "4a037911a1562ed7c4ca0baf7d4a2c70"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "d440a4f2d6c91f8b9ad18f30d3cb0433"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "662d3ed3e0cc13ec2fa64ba4b19ddf9d"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "0203bb160145623eced16780af5394bb"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "f0c0e21a6f9c5ad1f49d5da6b240196b"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "74be3f4a0f797b312fc0a8ff9e8dbf5b"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "3129fb84e869f6bdd619ab0e3a043dfe"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "0e482df33b9b08fa17eac958ef57b860"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "276ceeb6b6b01de8dbd6c7472240480f"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "43bbe9d443db60ab0d3802c271e0f912"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "f058dabfd8a6abbaf4ecfabfae42c61f"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "0eb5a0dd466b42b8f57dd68d4c38c1f1"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "fee787c4d9f9abdf55ae76ea2ee0d956"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "e716d275e519160ff021ba5bd3502fdd"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "e514a266c97d94d1dafdf30a6bb22682"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "7640f692c48b56b4e5b7d6942d4a2150"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "f63f0134135bec0ceb7c91e007cf2e87"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "eb5d28efa1597c8337af4383867ebfa6"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "f9c520ffe404474c751c14affa1674d2"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "f3d7cbcf8575d203f70c775d48d70992"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "3fb50d247eb3f9522188d1cb6933938d"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "bfcef52181268529df2d6d7f230d621f"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> <strong>SUM</strong>(`amount`) <strong>AS</strong> `amount`\n<strong>FROM</strong> `payments`\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:47", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:47", "qid": "84da7b6189f6b7b40f52558bb9ac10a0"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> <strong>SUM</strong>(`balance`) <strong>AS</strong> `balance`\n<strong>FROM</strong> `collection_summary`\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:50", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:50", "qid": "606e178a83ed6e909440144911a4898e"}, {"hover": "", "class": "", "duration": "2.35 ms", "sql": "<strong>SELECT</strong> `p`.*, `m`.`name` as `member_name`, `c`.`amount` as `commitment_amount`, `c`.`frequency` as `commitment_frequency`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `p`.`member_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `c`.`commitment_id` = `p`.`commitment_id`\n<strong>ORDER</strong> <strong>BY</strong> `p`.`payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/PaymentModel.php:113", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:53", "function": "        App\\Models\\PaymentModel->getPaymentsWithMemberDetails()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/PaymentModel.php:113", "qid": "f37bc94d19aafffa32b40c909dfc692d"}, {"hover": "", "class": "", "duration": "0.76 ms", "sql": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`, `m`.`phone`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `cs`.`balance` &gt; 0\n<strong>AND</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `cs`.`balance` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:334", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:57", "function": "        App\\Models\\CollectionSummaryModel->getMembersWithOutstandingBalances()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:334", "qid": "02e25c102de357b4a78751469c8493be"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "99b9865445bb59f0c03013b59f7c4e1f"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "b2a75ad367493b050417e798ffee281d"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "dff5b4637f3ff2313a33bcd63658ae55"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "557cf0faebb08552cec43bee6a5f3838"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "dc89b118ea37c2ab424ed6bcc6baa0fe"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "de88b65623eab0a6cf98c835fc314a6c"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "fedfd19dc637c66bb30354c8a18d8c87"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "313b87328e435261a570d37744a1d874"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "8ca9c190e574c7d358866aa95c23bf69"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "a4b236cbaf130e359d56358617dc9a10"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "fbe561afc325d1e77a0b5056885fd2ec"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "05ad7b4b6f0cdde4ac386c2a7bfa33ce"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "94523c0af011d9e37add385a8380a1bf"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "0dfafd2ac7900447b2044cd3495ebbfc"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "6555fe1af3dbe49e1d047512cc1e9a32"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "15bd47ce37a74eea57c8cb82a849ab58"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "1249490416a56ce08dc542d3af7e52ea"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "eb3a89b4b50bce3d1b1da9c360ba9e1f"}, {"hover": "", "class": "", "duration": "0.56 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "e40da5e047bdbceecaf6e840bc5af020"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "beb432a7a08278175d14a1c8768173e5"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "34b797251859b6cef628e78d0fbd8622"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "651f0c2239ff1277a355e9ed9662ea91"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "9d5a15c3dfa8f2f2186a5c14381ba0a6"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "71b831866c3a0a4c04c04597b9291080"}, {"hover": "", "class": "", "duration": "0.16 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "8fb1a25fad4b4852a805888c434faa63"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "4985f2b64880e331a6a27569c4275386"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "b05c73507c09dfb35cb1741b1c23d8ab"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "95129c7b1cae1712be74c57418330614"}, {"hover": "", "class": "", "duration": "0.16 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "5189749bb7a603483f09b75bc42fb597"}, {"hover": "", "class": "", "duration": "0.15 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "3af284aacdbad8064ea392ed16ffbb42"}, {"hover": "", "class": "", "duration": "0.63 ms", "sql": "<strong>SELECT</strong> `m`.*\n<strong>FROM</strong> `members` `m`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>AND</strong> `m`.`member_id` <strong>NOT</strong> <strong>IN</strong> (<strong>SELECT</strong> `c`.`member_id` <strong>FROM</strong> `commitments` `c` <strong>WHERE</strong> `c`.`start_date` &lt;= &#039;2025-05-28&#039; <strong>AND</strong>   ( `c`.`end_date` <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> `c`.`end_date` &gt;= &#039;2025-05-28&#039;  ))", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:993", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:75", "function": "        App\\Controllers\\Reports->getMembersWithoutActiveCommitments()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:993", "qid": "8583d9133a1c03f2bdab76f47f0fe7bd"}, {"hover": "", "class": "", "duration": "0.53 ms", "sql": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>ORDER</strong> <strong>BY</strong> `m`.`name` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:61", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:79", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetails()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:61", "qid": "7567ea099ff0a8c838b2fbe088e40caa"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> `payment_method`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `payments`\n<strong>GROUP</strong> <strong>BY</strong> `payment_method`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:124", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:124", "qid": "a597e6ee2dde82ebdeb971d7742caa62"}, {"hover": "", "class": "", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:537", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "0512bd95984ae54a5d18e69e90227021"}, {"hover": "", "class": "", "duration": "0.44 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:538", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "2e5e5c67dff5f56ee0268c1bd0232fce"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "17cb5c1c08d3a229475787ef27621555"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "0f67da9f590829a49419974f482e0e92"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "8ed2fce85c24be8a15a40b16c4e1ecdd"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "9adfca76ccfe39b82b0782fc10d38166"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "16d17474bfbbf2d674aa92b19ef8b70a"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "39ae33f006b192e713a822da726b5bd2"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "d93a76a7de3eda6722abafeabbe2eb4f"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "a296359cb96a7b3a8171da34afe3cefb"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "a23851ab42a19d1b0629fd0eff304188"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.44 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "fe6b0c0ba91e58d213822d9c62fb470a"}, {"hover": "", "class": "", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> `p`.`payment_date`, `c`.`start_date`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `p`.`commitment_id` = `c`.`commitment_id`\n<strong>WHERE</strong> `p`.`commitment_id` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `c`.`start_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `p`.`payment_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:588", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:556", "function": "        App\\Controllers\\Reports->calculateAverageMonthsToPayment()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:588", "qid": "6cb2c969a57ab52fd9436629dd8d349e"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:559", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:559", "qid": "a854afa778821bbd032acffaa83e0c57"}, {"hover": "", "class": "", "duration": "0.18 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `collection_summary`\n<strong>WHERE</strong> `balance` &gt; 0", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:560", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:560", "qid": "c112eeee8509728871e47f330b2fbaa3"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.34 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "cab5ad745d5c6e50f2d911bb1d376156"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "ffdbacccbac9b215cb429638e90bca41"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.35 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "b3b0dbac8f560b2e42af9cfdae384985"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "5eae66300103ab1ee5907bf5b06b3cfc"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "5c4dc5d2d5d2895fb88f2e4bffb7cdb2"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "7fb8be018ccc2447a7284dab9cb41820"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.43 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "f5596ee5efd45d027a7f6ca957985648"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.38 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "92bd96c02192a2617fd22cfc3cb68efa"}]}, "badgeValue": 100, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.700165, "duration": "0.000414"}, {"name": "Query", "component": "Database", "start": **********.700609, "duration": "0.000732", "query": "<strong>SELECT</strong> GET_LOCK(&#039;af3a693d82ee7c46bf98e63400c41915&#039;, 300) <strong>AS</strong> ci_session_lock"}, {"name": "Query", "component": "Database", "start": **********.701518, "duration": "0.000258", "query": "<strong>SELECT</strong> `data`\n<strong>FROM</strong> `ci_sessions`\n<strong>WHERE</strong> `id` = &#039;ci_session:bc9bdee29a21a8b2ba71324aa1ce5983&#039;"}, {"name": "Query", "component": "Database", "start": **********.705439, "duration": "0.000317", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`"}, {"name": "Query", "component": "Database", "start": **********.70583, "duration": "0.000269", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.706142, "duration": "0.000445", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `members`"}, {"name": "Query", "component": "Database", "start": **********.706678, "duration": "0.000286", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.707033, "duration": "0.000308", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;"}, {"name": "Query", "component": "Database", "start": **********.707416, "duration": "0.000247", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;"}, {"name": "Query", "component": "Database", "start": **********.707734, "duration": "0.000223", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.708013, "duration": "0.000204", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;"}, {"name": "Query", "component": "Database", "start": **********.708258, "duration": "0.000275", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.708589, "duration": "0.000242", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;"}, {"name": "Query", "component": "Database", "start": **********.708882, "duration": "0.000253", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.709191, "duration": "0.000241", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.709484, "duration": "0.000258", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;"}, {"name": "Query", "component": "Database", "start": **********.709788, "duration": "0.000301", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.71015, "duration": "0.000278", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.710476, "duration": "0.000267", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.710791, "duration": "0.000258", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.711103, "duration": "0.000247", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.711401, "duration": "0.000252", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.711709, "duration": "0.000232", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.711984, "duration": "0.000248", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.712284, "duration": "0.000242", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;"}, {"name": "Query", "component": "Database", "start": **********.712574, "duration": "0.000229", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.712849, "duration": "0.000214", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.713121, "duration": "0.000237", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.713427, "duration": "0.000252", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.713724, "duration": "0.000271", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.714051, "duration": "0.000294", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.714428, "duration": "0.000312", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.714843, "duration": "0.000324", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.715265, "duration": "0.000283", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.715642, "duration": "0.000270", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.716001, "duration": "0.000289", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.716374, "duration": "0.000270", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.716721, "duration": "0.000269", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.717081, "duration": "0.000327", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.717488, "duration": "0.000280", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.717854, "duration": "0.000314", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.718264, "duration": "0.000246", "query": "<strong>SELECT</strong> <strong>SUM</strong>(`amount`) <strong>AS</strong> `amount`\n<strong>FROM</strong> `payments`\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.718587, "duration": "0.000240", "query": "<strong>SELECT</strong> <strong>SUM</strong>(`balance`) <strong>AS</strong> `balance`\n<strong>FROM</strong> `collection_summary`\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.719104, "duration": "0.002349", "query": "<strong>SELECT</strong> `p`.*, `m`.`name` as `member_name`, `c`.`amount` as `commitment_amount`, `c`.`frequency` as `commitment_frequency`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `p`.`member_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `c`.`commitment_id` = `p`.`commitment_id`\n<strong>ORDER</strong> <strong>BY</strong> `p`.`payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.721787, "duration": "0.000764", "query": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`, `m`.`phone`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `cs`.`balance` &gt; 0\n<strong>AND</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `cs`.`balance` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.722692, "duration": "0.000224", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.723002, "duration": "0.000215", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.723301, "duration": "0.000202", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.723585, "duration": "0.000197", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.723862, "duration": "0.000222", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.724162, "duration": "0.000290", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.724531, "duration": "0.000204", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.724852, "duration": "0.000268", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.725197, "duration": "0.000372", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.725649, "duration": "0.000257", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.725987, "duration": "0.000291", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.726357, "duration": "0.000245", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.726681, "duration": "0.000208", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.726968, "duration": "0.000221", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.727266, "duration": "0.000245", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.727615, "duration": "0.000232", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.727929, "duration": "0.000236", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;"}, {"name": "Query", "component": "Database", "start": **********.728243, "duration": "0.000214", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.728533, "duration": "0.000558", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.729184, "duration": "0.000234", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.729504, "duration": "0.000211", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.729782, "duration": "0.000195", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;"}, {"name": "Query", "component": "Database", "start": **********.730035, "duration": "0.000213", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.730331, "duration": "0.000170", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.730571, "duration": "0.000160", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.73081, "duration": "0.000167", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.731037, "duration": "0.000237", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.731332, "duration": "0.000166", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.731552, "duration": "0.000160", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.731771, "duration": "0.000148", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.732331, "duration": "0.000627", "query": "<strong>SELECT</strong> `m`.*\n<strong>FROM</strong> `members` `m`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>AND</strong> `m`.`member_id` <strong>NOT</strong> <strong>IN</strong> (<strong>SELECT</strong> `c`.`member_id` <strong>FROM</strong> `commitments` `c` <strong>WHERE</strong> `c`.`start_date` &lt;= &#039;2025-05-28&#039; <strong>AND</strong>   ( `c`.`end_date` <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> `c`.`end_date` &gt;= &#039;2025-05-28&#039;  ))"}, {"name": "Query", "component": "Database", "start": **********.733105, "duration": "0.000528", "query": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>ORDER</strong> <strong>BY</strong> `m`.`name` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.733763, "duration": "0.000322", "query": "<strong>SELECT</strong> `payment_method`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `payments`\n<strong>GROUP</strong> <strong>BY</strong> `payment_method`"}, {"name": "Query", "component": "Database", "start": **********.734154, "duration": "0.000408", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.734884, "duration": "0.000437", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.735411, "duration": "0.000376", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.73595, "duration": "0.000384", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.736405, "duration": "0.000338", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.736906, "duration": "0.000349", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.737328, "duration": "0.000325", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.737811, "duration": "0.000332", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.738211, "duration": "0.000321", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.738728, "duration": "0.000378", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.739226, "duration": "0.000345", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.739834, "duration": "0.000441", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.740452, "duration": "0.000344", "query": "<strong>SELECT</strong> `p`.`payment_date`, `c`.`start_date`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `p`.`commitment_id` = `c`.`commitment_id`\n<strong>WHERE</strong> `p`.`commitment_id` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `c`.`start_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `p`.`payment_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.740966, "duration": "0.000206", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.74123, "duration": "0.000178", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `collection_summary`\n<strong>WHERE</strong> `balance` &gt; 0"}, {"name": "Query", "component": "Database", "start": **********.741479, "duration": "0.000337", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.741978, "duration": "0.000354", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.742406, "duration": "0.000346", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.742911, "duration": "0.000382", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.74344, "duration": "0.000361", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.744009, "duration": "0.000355", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.744485, "duration": "0.000434", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.745247, "duration": "0.000382", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: layout/main.php", "component": "Views", "start": **********.770004, "duration": 0.0007848739624023438}, {"name": "View: reports/dashboard.php", "component": "Views", "start": **********.768775, "duration": 0.002042055130004883}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 163 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/Database/MySQLiHandler.php", "name": "MySQLiHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/DatabaseHandler.php", "name": "DatabaseHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Performance.php", "name": "Performance.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Controllers/Reports.php", "name": "Reports.php"}, {"path": "APPPATH/Filters/AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH/Filters/PerformanceFilter.php", "name": "PerformanceFilter.php"}, {"path": "APPPATH/Helpers/currency_helper.php", "name": "currency_helper.php"}, {"path": "APPPATH/Models/CollectionSummaryModel.php", "name": "CollectionSummaryModel.php"}, {"path": "APPPATH/Models/CommitmentModel.php", "name": "CommitmentModel.php"}, {"path": "APPPATH/Models/MemberModel.php", "name": "MemberModel.php"}, {"path": "APPPATH/Models/PaymentModel.php", "name": "PaymentModel.php"}, {"path": "APPPATH/Views/layout/main.php", "name": "main.php"}, {"path": "APPPATH/Views/reports/dashboard.php", "name": "dashboard.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "FCPATH/vendor/autoload.php", "name": "autoload.php"}, {"path": "FCPATH/vendor/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "FCPATH/vendor/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "FCPATH/vendor/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "FCPATH/vendor/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "FCPATH/vendor/composer/installed.php", "name": "installed.php"}, {"path": "FCPATH/vendor/composer/platform_check.php", "name": "platform_check.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "FCPATH/vendor/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}]}, "badgeValue": 163, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Reports", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::index"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/change-password", "handler": "\\App\\Controllers\\Auth::changePassword"}, {"method": "GET", "route": "members", "handler": "\\App\\Controllers\\Members::index"}, {"method": "GET", "route": "members/new", "handler": "\\App\\Controllers\\Members::new"}, {"method": "GET", "route": "members/show/([0-9]+)", "handler": "\\App\\Controllers\\Members::show/$1"}, {"method": "GET", "route": "members/edit/([0-9]+)", "handler": "\\App\\Controllers\\Members::edit/$1"}, {"method": "GET", "route": "members/delete/([0-9]+)", "handler": "\\App\\Controllers\\Members::delete/$1"}, {"method": "GET", "route": "commitments", "handler": "\\App\\Controllers\\Commitments::index"}, {"method": "GET", "route": "commitments/new", "handler": "\\App\\Controllers\\Commitments::new"}, {"method": "GET", "route": "commitments/create/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::new/$1"}, {"method": "GET", "route": "commitments/show/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::show/$1"}, {"method": "GET", "route": "commitments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::edit/$1"}, {"method": "GET", "route": "commitments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::delete/$1"}, {"method": "GET", "route": "commitments/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::memberCommitments/$1"}, {"method": "GET", "route": "payments", "handler": "\\App\\Controllers\\Payments::index"}, {"method": "GET", "route": "payments/new", "handler": "\\App\\Controllers\\Payments::new"}, {"method": "GET", "route": "payments/show/([0-9]+)", "handler": "\\App\\Controllers\\Payments::show/$1"}, {"method": "GET", "route": "payments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Payments::edit/$1"}, {"method": "GET", "route": "payments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Payments::delete/$1"}, {"method": "GET", "route": "payments/member-payments/([0-9]+)", "handler": "\\App\\Controllers\\Payments::memberPayments/$1"}, {"method": "GET", "route": "payments/generate-receipt/([0-9]+)", "handler": "\\App\\Controllers\\Payments::generateReceipt/$1"}, {"method": "GET", "route": "reports", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/collection-summary", "handler": "\\App\\Controllers\\Reports::collectionSummary"}, {"method": "GET", "route": "reports/outstanding-balances", "handler": "\\App\\Controllers\\Reports::outstandingBalances"}, {"method": "GET", "route": "reports/payment-history", "handler": "\\App\\Controllers\\Reports::paymentHistory"}, {"method": "GET", "route": "reports/commitment-report", "handler": "\\App\\Controllers\\Reports::commitmentReport"}, {"method": "GET", "route": "reports/statistics", "handler": "\\App\\Controllers\\Reports::statistics"}, {"method": "GET", "route": "reports/recalculate-all-summaries", "handler": "\\App\\Controllers\\Reports::recalculateAllSummaries"}, {"method": "GET", "route": "reports/members-without-commitments", "handler": "\\App\\Controllers\\Reports::membersWithoutCommitments"}, {"method": "GET", "route": "api/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Api::getMemberCommitments/$1"}, {"method": "GET", "route": "api/payment-details/([0-9]+)/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1/$2"}, {"method": "GET", "route": "api/payment-details/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "auth/update-password", "handler": "\\App\\Controllers\\Auth::updatePassword"}, {"method": "POST", "route": "members/create", "handler": "\\App\\Controllers\\Members::create"}, {"method": "POST", "route": "members/update/([0-9]+)", "handler": "\\App\\Controllers\\Members::update/$1"}, {"method": "POST", "route": "commitments/create", "handler": "\\App\\Controllers\\Commitments::create"}, {"method": "POST", "route": "commitments/update/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::update/$1"}, {"method": "POST", "route": "payments/create", "handler": "\\App\\Controllers\\Payments::create"}, {"method": "POST", "route": "payments/update/([0-9]+)", "handler": "\\App\\Controllers\\Payments::update/$1"}]}, "badgeValue": 35, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "0.34", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.97", "count": 144}}}, "badgeValue": 145, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.698251, "duration": 0.0003409385681152344}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.701344, "duration": 1.9073486328125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.701778, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.705758, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.706101, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.706589, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.706966, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.707343, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.707665, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.707958, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.708218, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.708534, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.708832, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.709136, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.709433, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.709743, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.71009, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.710429, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.710744, "duration": 6.198883056640625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.71105, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.71135, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.711654, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.711942, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.712233, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.712527, "duration": 5.0067901611328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.712804, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.713064, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.713359, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.71368, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.713996, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.714347, "duration": 1.1205673217773438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.714742, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.715169, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.715549, "duration": 1.3113021850585938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.715913, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.716292, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.716645, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.716992, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.717409, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.71777, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.718169, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.718512, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.718829, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.721455, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.722552, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.722918, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.723218, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.723505, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.723783, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.724085, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.724453, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.724737, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.725121, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.725571, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.725908, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.726279, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.726604, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.726891, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.727191, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.727514, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.727849, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.728167, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.728458, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729093, "duration": 1.2874603271484375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729419, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729717, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729978, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.73025, "duration": 1.1205673217773438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.730503, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.730733, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.730978, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731276, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731499, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731713, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.73192, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.73296, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.733634, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.734086, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.734563, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.735322, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.735788, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.736335, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.736744, "duration": 7.152557373046875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.737256, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.737654, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.738143, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.738533, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.739107, "duration": 1.1205673217773438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.739572, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.740276, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.740797, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.741173, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.741409, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.741817, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.742333, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.742752, "duration": 5.9604644775390625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.743295, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.743803, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.744365, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.744921, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.74563, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.746225, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.746837, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.747311, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.747898, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.748356, "duration": 9.5367431640625e-07}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.748929, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.749408, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.750158, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.750641, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.751229, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.751697, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.752249, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.752707, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.753227, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.75364, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.754197, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.754649, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.755191, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.755753, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.756422, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.757141, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.757953, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.758662, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.759403, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.760264, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.760952, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.76172, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.762411, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.763048, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.76366, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.764183, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.764687, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.764956, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.765242, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.76553, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.76581, "duration": 9.5367431640625e-07}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.766105, "duration": 9.5367431640625e-07}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.766389, "duration": 1.1920928955078125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.766675, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.767015, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.767393, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.767737, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.768108, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.768434, "duration": 2.86102294921875e-06}]}], "vars": {"varData": {"View Data": {"title": "Dashboard", "totalMembers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 35</dt></dl></div>", "activeMembers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 35</dt></dl></div>", "totalCommitments": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>double</var> 126800</dt></dl></div>", "totalPayments": "20190.00", "totalOutstanding": "106610.00", "recentPayments": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>payment_id</th><th>member_id</th><th>commitment_id</th><th>amount</th><th>payment_date</th><th>receipt_number</th><th>receipt_book_number</th><th>payment_method</th><th>notes</th><th>payment_periods</th><th>created_at</th><th>updated_at</th><th>member_name</th><th>commitment_amount</th><th>commitment_frequency</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">2</td><td title=\"string (1)\">1</td><td title=\"string (1)\">7</td><td title=\"string (6)\">300.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050002</td><td title=\"string (1)\">1</td><td title=\"string (13)\">bank_transfer</td><td title=\"string (55)\">Payment for: October 2024, November 2024, December 2024</td><td title=\"string (31)\">[\"2024-10\",\"2024-11\",\"2024-12\"]</td><td title=\"string (19)\">2025-05-24 13:25:21</td><td title=\"string (19)\">2025-05-24 13:25:21</td><td title=\"string (14)\">Abdu Raheem KK</td><td title=\"string (6)\">100.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>1</th><td title=\"string (2)\">58</td><td title=\"string (2)\">15</td><td title=\"string (2)\">29</td><td title=\"string (7)\">4730.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050005</td><td title=\"string (1)\">4</td><td title=\"string (5)\">check</td><td title=\"string (155)\">Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, JuUTF-8</td><td title=\"string (111)\">[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"202UTF-8</td><td title=\"string (19)\">2025-05-24 16:59:14</td><td title=\"string (19)\">2025-05-24 16:59:14</td><td title=\"string (12)\">Adnan Al-Ali</td><td title=\"string (6)\">430.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>2</th><td title=\"string (1)\">1</td><td title=\"string (1)\">3</td><td title=\"string (1)\">4</td><td title=\"string (6)\">100.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050001</td><td title=\"string (1)\">1</td><td title=\"string (4)\">cash</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 12:35:13</td><td title=\"string (19)\">2025-05-24 12:35:13</td><td title=\"string (5)\">Murad</td><td title=\"string (6)\">100.00</td><td title=\"string (8)\">one-time</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"string (1)\">4</td><td title=\"string (1)\">6</td><td title=\"string (6)\">600.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050004</td><td title=\"string (1)\">3</td><td title=\"string (5)\">other</td><td title=\"string (52)\">Payment for: January 2025, February 2025, March 2025</td><td title=\"string (31)\">[\"2025-01\",\"2025-02\",\"2025-03\"]</td><td title=\"string (19)\">2025-05-24 14:13:50</td><td title=\"string (19)\">2025-05-24 14:13:50</td><td title=\"string (5)\">Riyas</td><td title=\"string (6)\">200.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>4</th><td title=\"string (2)\">18</td><td title=\"string (2)\">14</td><td title=\"string (2)\">26</td><td title=\"string (5)\">70.00</td><td title=\"string (10)\">2025-05-19</td><td title=\"string (4)\">8961</td><td title=\"string (4)\">BK02</td><td title=\"string (4)\">cash</td><td title=\"null\"><var>null</var></td><td title=\"string (7)\">2025-03</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (15)\">Ibrahim Al-Saud</td><td title=\"string (5)\">70.00</td><td title=\"string (7)\">monthly</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[0]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"300.00\"<div class=\"access-path\">$value[0]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[0]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050002\"<div class=\"access-path\">$value[0]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (13) \"bank_transfer\"<div class=\"access-path\">$value[0]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (55) \"Payment for: October 2024, November 2024, December 2024\"<div class=\"access-path\">$value[0]['notes']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (31) \"[\"2024-10\",\"2024-11\",\"2024-12\"]\"<div class=\"access-path\">$value[0]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2024-10\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2024-11\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2024-12\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[2]</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 13:25:21\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 13:25:21\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Abdu Raheem KK\"<div class=\"access-path\">$value[0]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[0]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[0]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (2) \"58\"<div class=\"access-path\">$value[1]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[1]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (7) \"4730.00\"<div class=\"access-path\">$value[1]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[1]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050005\"<div class=\"access-path\">$value[1]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (5) \"check\"<div class=\"access-path\">$value[1]['payment_method']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>notes</dfn> =&gt; <var>string</var> (155) \"Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, Jul...<div class=\"access-path\">$value[1]['notes']</div></dt><dd><pre>Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, July 2025, August 2025, September 2025, October 2025, November 2025, December 2025\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (111) \"[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"2025...<div class=\"access-path\">$value[1]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (11)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2025-02\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2025-04\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[2]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>3</dfn> =&gt; <var>string</var> (7) \"2025-05\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[3]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>4</dfn> =&gt; <var>string</var> (7) \"2025-06\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[4]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>5</dfn> =&gt; <var>string</var> (7) \"2025-07\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[5]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>6</dfn> =&gt; <var>string</var> (7) \"2025-08\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[6]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>7</dfn> =&gt; <var>string</var> (7) \"2025-09\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[7]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>8</dfn> =&gt; <var>string</var> (7) \"2025-10\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[8]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>9</dfn> =&gt; <var>string</var> (7) \"2025-11\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[9]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>10</dfn> =&gt; <var>string</var> (7) \"2025-12\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[10]</div></dt></dl></li><li><pre>[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"2025-09\",\"2025-10\",\"2025-11\",\"2025-12\"]\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:59:14\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:59:14\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (12) \"Adnan Al-Ali\"<div class=\"access-path\">$value[1]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"430.00\"<div class=\"access-path\">$value[1]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[1]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[2]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[2]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050001\"<div class=\"access-path\">$value[2]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (4) \"cash\"<div class=\"access-path\">$value[2]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_periods</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['payment_periods']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 12:35:13\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 12:35:13\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Murad\"<div class=\"access-path\">$value[2]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[2]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (8) \"one-time\"<div class=\"access-path\">$value[2]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[3]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"600.00\"<div class=\"access-path\">$value[3]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[3]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050004\"<div class=\"access-path\">$value[3]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (5) \"other\"<div class=\"access-path\">$value[3]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (52) \"Payment for: January 2025, February 2025, March 2025\"<div class=\"access-path\">$value[3]['notes']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (31) \"[\"2025-01\",\"2025-02\",\"2025-03\"]\"<div class=\"access-path\">$value[3]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2025-01\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2025-02\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[2]</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 14:13:50\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 14:13:50\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Riyas\"<div class=\"access-path\">$value[3]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"200.00\"<div class=\"access-path\">$value[3]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[3]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[4]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[4]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[4]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-19\"<div class=\"access-path\">$value[4]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (4) \"8961\"<div class=\"access-path\">$value[4]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (4) \"BK02\"<div class=\"access-path\">$value[4]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (4) \"cash\"<div class=\"access-path\">$value[4]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_periods</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">$value[4]['payment_periods']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Ibrahim Al-Saud\"<div class=\"access-path\">$value[4]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[4]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[4]['commitment_frequency']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "membersWithOutstandingBalances": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>summary_id</th><th>member_id</th><th>total_committed</th><th>total_paid</th><th>balance</th><th>last_payment_date</th><th>last_payment_amount</th><th>created_at</th><th>updated_at</th><th>member_name</th><th>status</th><th>phone</th><th>whatsapp_number</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">28</td><td title=\"string (2)\">28</td><td title=\"string (7)\">9540.00</td><td title=\"string (6)\">340.00</td><td title=\"string (7)\">9200.00</td><td title=\"string (10)\">2024-11-10</td><td title=\"string (6)\">170.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (13)\">Saud Al-Ahmad</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0592485468</td><td title=\"string (10)\">0554246003</td></tr><tr><th>1</th><td title=\"string (2)\">18</td><td title=\"string (2)\">18</td><td title=\"string (7)\">9150.00</td><td title=\"string (6)\">380.00</td><td title=\"string (7)\">8770.00</td><td title=\"string (10)\">2025-05-07</td><td title=\"string (6)\">190.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (15)\">Bilal Al-Waleed</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0515351788</td><td title=\"string (10)\">0522375982</td></tr><tr><th>2</th><td title=\"string (2)\">21</td><td title=\"string (2)\">21</td><td title=\"string (8)\">10020.00</td><td title=\"string (7)\">1480.00</td><td title=\"string (7)\">8540.00</td><td title=\"string (10)\">2025-03-10</td><td title=\"string (6)\">490.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (14)\">Bilal Al-Jamal</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0528141858</td><td title=\"string (10)\">0523298126</td></tr><tr><th>3</th><td title=\"string (2)\">30</td><td title=\"string (2)\">30</td><td title=\"string (7)\">6690.00</td><td title=\"string (4)\">0.00</td><td title=\"string (7)\">6690.00</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (17)\">Ibrahim Al-Hassan</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0549461895</td><td title=\"string (10)\">0582341569</td></tr><tr><th>4</th><td title=\"string (2)\">29</td><td title=\"string (2)\">29</td><td title=\"string (7)\">6100.00</td><td title=\"string (4)\">0.00</td><td title=\"string (7)\">6100.00</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (15)\">Salman Al-Bilal</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0541587438</td><td title=\"string (10)\">0523101499</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[0]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"9540.00\"<div class=\"access-path\">$value[0]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (6) \"340.00\"<div class=\"access-path\">$value[0]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"9200.00\"<div class=\"access-path\">$value[0]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-11-10\"<div class=\"access-path\">$value[0]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"170.00\"<div class=\"access-path\">$value[0]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (13) \"Saud Al-Ahmad\"<div class=\"access-path\">$value[0]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0592485468\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>1988-10-10T11:17:48+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0554246003\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>1987-07-25T21:13:23+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"9150.00\"<div class=\"access-path\">$value[1]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (6) \"380.00\"<div class=\"access-path\">$value[1]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8770.00\"<div class=\"access-path\">$value[1]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-07\"<div class=\"access-path\">$value[1]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"190.00\"<div class=\"access-path\">$value[1]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Bilal Al-Waleed\"<div class=\"access-path\">$value[1]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0515351788\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>1986-05-01T17:16:28+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0522375982\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>1986-07-22T00:26:22+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (8) \"10020.00\"<div class=\"access-path\">$value[2]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (7) \"1480.00\"<div class=\"access-path\">$value[2]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8540.00\"<div class=\"access-path\">$value[2]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-10\"<div class=\"access-path\">$value[2]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"490.00\"<div class=\"access-path\">$value[2]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Bilal Al-Jamal\"<div class=\"access-path\">$value[2]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0528141858\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1986-09-26T18:04:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523298126\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1986-08-01T16:35:26+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"6690.00\"<div class=\"access-path\">$value[3]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[3]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6690.00\"<div class=\"access-path\">$value[3]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (17) \"Ibrahim Al-Hassan\"<div class=\"access-path\">$value[3]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0549461895\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1987-05-31T12:18:15+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0582341569\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1988-06-15T01:32:49+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[4]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"6100.00\"<div class=\"access-path\">$value[4]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[4]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6100.00\"<div class=\"access-path\">$value[4]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Salman Al-Bilal\"<div class=\"access-path\">$value[4]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0541587438\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1987-03-01T08:57:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523101499\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1986-07-30T09:58:19+00:00\n</pre></dd></dl></dd></dl></li></ul></dd></dl></div>", "membersWithoutActiveCommitments": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>member_id</th><th>name</th><th>email</th><th>post_office</th><th>old_reference</th><th>phone</th><th>whatsapp_number</th><th>address</th><th>join_date</th><th>status</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (14)\">Abdu Raheem KK</td><td title=\"string (18)\"><EMAIL></td><td title=\"string (10)\">Karalmanna</td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777125</td><td title=\"string (10)\">9947777125</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2018-01-01</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 22:16:26</td><td title=\"string (19)\">2025-05-24 10:30:28</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (7)\">Subeena</td><td title=\"string (15)\"><EMAIL></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777124</td><td title=\"string (10)\">9605447793</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2025-05-18</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 23:25:30</td><td title=\"string (19)\">2025-05-24 10:30:55</td></tr><tr><th>2</th><td title=\"string (1)\">5</td><td title=\"string (14)\">Omar Al-Nasser</td><td title=\"string (26)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">HIST-C-001</td><td title=\"string (10)\">0537494636</td><td title=\"string (10)\">0572364484</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-11-09</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:20</td><td title=\"string (19)\">2025-05-24 21:58:20</td></tr><tr><th>3</th><td title=\"string (1)\">7</td><td title=\"string (9)\">Zaid Khan</td><td title=\"string (21)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">OLD-A1-002</td><td title=\"string (10)\">0537155671</td><td title=\"string (10)\">0537155671</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-04-02</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td></tr><tr><th>4</th><td title=\"string (2)\">10</td><td title=\"string (15)\">Fahad Al-Faisal</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (15)\">Industrial Area</td><td title=\"string (12)\">PREV-002-005</td><td title=\"string (10)\">0540420476</td><td title=\"string (10)\">0579349018</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-03-14</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Abdu Raheem KK\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (18) \"<EMAIL>\"<div class=\"access-path\">$value[0]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (10) \"Karalmanna\"<div class=\"access-path\">$value[0]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[0]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2018-01-01\"<div class=\"access-path\">$value[0]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 22:16:26\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:28\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (7) \"Subeena\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (15) \"<EMAIL>\"<div class=\"access-path\">$value[1]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777124\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>2285-03-26T07:25:24+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9605447793\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>2274-05-21T03:56:33+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[1]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-05-18\"<div class=\"access-path\">$value[1]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 23:25:30\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:55\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Omar Al-Nasser\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value[2]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[2]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"HIST-C-001\"<div class=\"access-path\">$value[2]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537494636\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1987-01-13T00:03:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0572364484\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1988-02-20T14:08:04+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-11-09\"<div class=\"access-path\">$value[2]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (9) \"Zaid Khan\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (21) \"<EMAIL>\"<div class=\"access-path\">$value[3]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[3]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-A1-002\"<div class=\"access-path\">$value[3]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-04-02\"<div class=\"access-path\">$value[3]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Fahad Al-Faisal\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[4]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (15) \"Industrial Area\"<div class=\"access-path\">$value[4]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (12) \"PREV-002-005\"<div class=\"access-path\">$value[4]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0540420476\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1987-02-15T20:47:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0579349018\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1988-05-11T10:16:58+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-03-14\"<div class=\"access-path\">$value[4]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "startDate": "2024-05-28", "endDate": "2025-05-28", "months": "[&quot;May 2024&quot;,&quot;Jun 2024&quot;,&quot;Jul 2024&quot;,&quot;Aug 2024&quot;,&quot;Sep 2024&quot;,&quot;Oct 2024&quot;,&quot;Nov 2024&quot;,&quot;Dec 2024&quot;,&quot;Jan 2025&quot;,&quot;Feb 2025&quot;,&quot;Mar 2025&quot;,&quot;Apr 2025&quot;,&quot;May 2025&quot;]", "committedAmounts": "[360,2060,3670,3460,5820,6470,7320,8360,9430,8460,8730,9280,10020]", "paidAmounts": "[0,950,450,1230,920,530,790,980,2930,1370,2270,750,1460]", "memberCounts": "[2,7,11,12,17,17,19,23,22,22,20,24,24]", "datePaidAmounts": "[50,0,2000,450,530,1320,900,970,1160,2260,2200,1710,6640]", "fulfillmentRates": "[0,46.1,12.3,35.5,15.8,8.2,10.8,11.7,31.1,16.2,26,8.1,14.6]", "movingAverages": "[50,25,683.********33334,816.6666666666666,993.********33334,766.6666666666666,916.6666666666666,1063.********33333,1010,1463.********33333,1873.********33333,2056.*************,3516.*************]", "memberComplianceData": "{&quot;labels&quot;:[&quot;<PERSON>y Paid (100%)&quot;,&quot;Mostly Paid (75-99%)&quot;,&quot;Partially Paid (25-74%)&quot;,&quot;Minimally Paid (1-24%)&quot;,&quot;No Payments (0%)&quot;],&quot;data&quot;:[4,0,4,15,11],&quot;backgroundColor&quot;:[&quot;#28a745&quot;,&quot;#5cb85c&quot;,&quot;#ffc107&quot;,&quot;#fd7e14&quot;,&quot;#dc3545&quot;]}", "paymentMethodData": "{&quot;labels&quot;:[&quot;Cash&quot;,&quot;Check&quot;,&quot;Bank_transfer&quot;,&quot;Other&quot;],&quot;data&quot;:[55,1,1,1],&quot;backgroundColor&quot;:[&quot;#28a745&quot;,&quot;#6f42c1&quot;,&quot;#6c757d&quot;,&quot;#6c757d&quot;]}", "collectionEfficiencyData": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>currentMonthRate</dfn> =&gt; <var>double</var> 14.6<div class=\"access-path\">$value['currentMonthRate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>ytdRate</dfn> =&gt; <var>double</var> 19.1<div class=\"access-path\">$value['ytdRate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>avgMonths</dfn> =&gt; <var>double</var> 2.8<div class=\"access-path\">$value['avgMonths']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentWithBalance</dfn> =&gt; <var>double</var> 85.7<div class=\"access-path\">$value['percentWithBalance']</div></dt></dl></dd></dl></div>"}}, "session": {"redirect_url": "https://halqa.mazharulirfan.com/reports", "_ci_previous_url": "https://halqa.mazharulirfan.com/reports", "admin_id": "2", "username": "boss", "isLoggedIn": "<pre>1</pre>", "success": "Login successful", "__ci_vars": "<pre>Array\n(\n    [success] =&gt; old\n)\n</pre>"}, "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Accept-Encoding": "br", "Accept-Language": "en-US,en;q=0.9", "Cookie": "ci_session=bc9bdee29a21a8b2ba71324aa1ce5983", "Host": "halqa.mazharulirfan.com", "Referer": "https://halqa.mazharulirfan.com/login", "User-Agent": "Mozilla/5.0 (Linux; Android 10; K) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Mobile Safari/537.36", "Cache-Control": "max-age=0", "X-Forwarded-For": "2409:40f3:112b:6565:c9d6:e7dd:c3c5:6f35", "X-Forwarded-Proto": "https", "X-Real-Ip": "2409:40f3:112b:6565:c9d6:e7dd:c3c5:6f35", "X-Real-Port": "46858", "X-Forwarded-Port": "443", "X-Port": "443", "X-Lscache": "1", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?1", "Sec-Ch-Ua-Platform": "&quot;Android&quot;", "Priority": "u=0, i"}, "cookies": {"ci_session": "bc9bdee29a21a8b2ba71324aa1ce5983"}, "request": "HTTPS/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.27", "phpSAPI": "litespeed", "environment": "development", "baseURL": "https://halqa.mazharulirfan.com/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}