{"url": "https://halqa.mazharulirfan.com/members", "method": "GET", "isAJAX": false, "startTime": **********.329984, "totalTime": 20.400000000000002, "totalMemory": "1.058", "segmentDuration": 5, "segmentCount": 5, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.330555, "duration": 0.0036830902099609375}, {"name": "Required Before Filters", "component": "Timer", "start": **********.33424, "duration": 0.0011811256408691406}, {"name": "Routing", "component": "Timer", "start": **********.335429, "duration": 0.0005109310150146484}, {"name": "Before Filters", "component": "Timer", "start": **********.336034, "duration": 0.0014278888702392578}, {"name": "Controller", "component": "Timer", "start": **********.337466, "duration": 0.012650012969970703}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.337467, "duration": 0.003523111343383789}, {"name": "After Filters", "component": "Timer", "start": **********.350136, "duration": 4.696846008300781e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.350216, "duration": 0.00021505355834960938}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(2 total Queries, 2 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "2.67 ms", "sql": "<strong>SELECT</strong> `m`.*, COALESCE(cs.balance, 0) as balance, `cs`.`last_payment_date`\n<strong>FROM</strong> `members` `m`\n<strong>LEFT</strong> <strong>JOIN</strong> `collection_summary` `cs` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> COALESCE(cs.balance, 0) <strong>DESC</strong>\n <strong>LIMIT</strong> 50", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/MemberModel.php:172", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Members.php:41", "function": "        App\\Models\\MemberModel->getPaginatedMembersWithBalance()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Members->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/MemberModel.php:172", "qid": "ea805c17fb31c182db17e49373560a03"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Models/MemberModel.php:193", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Members.php:50", "function": "        App\\Models\\MemberModel->getTotalMembersCount()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Members->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Models/MemberModel.php:193", "qid": "d5e55f4e650eb4dcec8da60a71fe5082"}]}, "badgeValue": 2, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.342373, "duration": "0.000574"}, {"name": "Query", "component": "Database", "start": **********.343031, "duration": "0.002669", "query": "<strong>SELECT</strong> `m`.*, COALESCE(cs.balance, 0) as balance, `cs`.`last_payment_date`\n<strong>FROM</strong> `members` `m`\n<strong>LEFT</strong> <strong>JOIN</strong> `collection_summary` `cs` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> COALESCE(cs.balance, 0) <strong>DESC</strong>\n <strong>LIMIT</strong> 50"}, {"name": "Query", "component": "Database", "start": **********.346243, "duration": "0.000333", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: layout/main.php", "component": "Views", "start": **********.349636, "duration": 0.0004119873046875}, {"name": "View: members/index.php", "component": "Views", "start": **********.347298, "duration": 0.00279998779296875}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 164 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/RESTful/BaseResource.php", "name": "BaseResource.php"}, {"path": "SYSTEMPATH/RESTful/ResourceController.php", "name": "ResourceController.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Performance.php", "name": "Performance.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/Members.php", "name": "Members.php"}, {"path": "APPPATH/Filters/AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH/Filters/PerformanceFilter.php", "name": "PerformanceFilter.php"}, {"path": "APPPATH/Helpers/currency_helper.php", "name": "currency_helper.php"}, {"path": "APPPATH/Libraries/PaginationHelper.php", "name": "PaginationHelper.php"}, {"path": "APPPATH/Models/CollectionSummaryModel.php", "name": "CollectionSummaryModel.php"}, {"path": "APPPATH/Models/CommitmentModel.php", "name": "CommitmentModel.php"}, {"path": "APPPATH/Models/MemberModel.php", "name": "MemberModel.php"}, {"path": "APPPATH/Models/PaymentModel.php", "name": "PaymentModel.php"}, {"path": "APPPATH/Views/layout/main.php", "name": "main.php"}, {"path": "APPPATH/Views/members/index.php", "name": "index.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "FCPATH/vendor/autoload.php", "name": "autoload.php"}, {"path": "FCPATH/vendor/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "FCPATH/vendor/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "FCPATH/vendor/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "FCPATH/vendor/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "FCPATH/vendor/composer/installed.php", "name": "installed.php"}, {"path": "FCPATH/vendor/composer/platform_check.php", "name": "platform_check.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "FCPATH/vendor/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}]}, "badgeValue": 164, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Members", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::index"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/change-password", "handler": "\\App\\Controllers\\Auth::changePassword"}, {"method": "GET", "route": "members", "handler": "\\App\\Controllers\\Members::index"}, {"method": "GET", "route": "members/new", "handler": "\\App\\Controllers\\Members::new"}, {"method": "GET", "route": "members/show/([0-9]+)", "handler": "\\App\\Controllers\\Members::show/$1"}, {"method": "GET", "route": "members/edit/([0-9]+)", "handler": "\\App\\Controllers\\Members::edit/$1"}, {"method": "GET", "route": "members/delete/([0-9]+)", "handler": "\\App\\Controllers\\Members::delete/$1"}, {"method": "GET", "route": "commitments", "handler": "\\App\\Controllers\\Commitments::index"}, {"method": "GET", "route": "commitments/new", "handler": "\\App\\Controllers\\Commitments::new"}, {"method": "GET", "route": "commitments/create/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::new/$1"}, {"method": "GET", "route": "commitments/show/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::show/$1"}, {"method": "GET", "route": "commitments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::edit/$1"}, {"method": "GET", "route": "commitments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::delete/$1"}, {"method": "GET", "route": "commitments/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::memberCommitments/$1"}, {"method": "GET", "route": "payments", "handler": "\\App\\Controllers\\Payments::index"}, {"method": "GET", "route": "payments/new", "handler": "\\App\\Controllers\\Payments::new"}, {"method": "GET", "route": "payments/show/([0-9]+)", "handler": "\\App\\Controllers\\Payments::show/$1"}, {"method": "GET", "route": "payments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Payments::edit/$1"}, {"method": "GET", "route": "payments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Payments::delete/$1"}, {"method": "GET", "route": "payments/member-payments/([0-9]+)", "handler": "\\App\\Controllers\\Payments::memberPayments/$1"}, {"method": "GET", "route": "payments/generate-receipt/([0-9]+)", "handler": "\\App\\Controllers\\Payments::generateReceipt/$1"}, {"method": "GET", "route": "reports", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/collection-summary", "handler": "\\App\\Controllers\\Reports::collectionSummary"}, {"method": "GET", "route": "reports/outstanding-balances", "handler": "\\App\\Controllers\\Reports::outstandingBalances"}, {"method": "GET", "route": "reports/payment-history", "handler": "\\App\\Controllers\\Reports::paymentHistory"}, {"method": "GET", "route": "reports/commitment-report", "handler": "\\App\\Controllers\\Reports::commitmentReport"}, {"method": "GET", "route": "reports/statistics", "handler": "\\App\\Controllers\\Reports::statistics"}, {"method": "GET", "route": "reports/recalculate-all-summaries", "handler": "\\App\\Controllers\\Reports::recalculateAllSummaries"}, {"method": "GET", "route": "reports/members-without-commitments", "handler": "\\App\\Controllers\\Reports::membersWithoutCommitments"}, {"method": "GET", "route": "api/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Api::getMemberCommitments/$1"}, {"method": "GET", "route": "api/payment-details/([0-9]+)/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1/$2"}, {"method": "GET", "route": "api/payment-details/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "auth/update-password", "handler": "\\App\\Controllers\\Auth::updatePassword"}, {"method": "POST", "route": "members/create", "handler": "\\App\\Controllers\\Members::create"}, {"method": "POST", "route": "members/update/([0-9]+)", "handler": "\\App\\Controllers\\Members::update/$1"}, {"method": "POST", "route": "commitments/create", "handler": "\\App\\Controllers\\Commitments::create"}, {"method": "POST", "route": "commitments/update/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::update/$1"}, {"method": "POST", "route": "payments/create", "handler": "\\App\\Controllers\\Payments::create"}, {"method": "POST", "route": "payments/update/([0-9]+)", "handler": "\\App\\Controllers\\Payments::update/$1"}]}, "badgeValue": 35, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "1.12", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "0.08", "count": 2}}}, "badgeValue": 3, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.333109, "duration": 0.0011241436004638672}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.345713, "duration": 5.698204040527344e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.34658, "duration": 1.9073486328125e-05}]}], "vars": {"varData": {"View Data": {"title": "Members", "members": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (35)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (35)</li><li>Contents (35)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>member_id</th><th>name</th><th>email</th><th>post_office</th><th>old_reference</th><th>phone</th><th>whatsapp_number</th><th>address</th><th>join_date</th><th>status</th><th>created_at</th><th>updated_at</th><th>balance</th><th>last_payment_date</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">28</td><td title=\"string (13)\">Saud Al-Ahmad</td><td title=\"string (25)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (13)\">LEGACY-01-023</td><td title=\"string (10)\">0592485468</td><td title=\"string (10)\">0554246003</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-06-11</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">9200.00</td><td title=\"string (10)\">2024-11-10</td></tr><tr><th>1</th><td title=\"string (2)\">18</td><td title=\"string (15)\">Bilal Al-Waleed</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (15)\">Industrial Area</td><td title=\"string (13)\">LEGACY-02-013</td><td title=\"string (10)\">0515351788</td><td title=\"string (10)\">0522375982</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-08-22</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">8770.00</td><td title=\"string (10)\">2025-05-07</td></tr><tr><th>2</th><td title=\"string (2)\">21</td><td title=\"string (14)\">Bilal Al-Jamal</td><td title=\"string (26)\"><EMAIL></td><td title=\"string (19)\">Central Post Office</td><td title=\"string (12)\">PREV-001-016</td><td title=\"string (10)\">0528141858</td><td title=\"string (10)\">0523298126</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-08-28</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">8540.00</td><td title=\"string (10)\">2025-03-10</td></tr><tr><th>3</th><td title=\"string (2)\">30</td><td title=\"string (17)\">Ibrahim Al-Hassan</td><td title=\"string (29)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">HIST-B-025</td><td title=\"string (10)\">0549461895</td><td title=\"string (10)\">0582341569</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-05-26</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">6690.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>4</th><td title=\"string (2)\">29</td><td title=\"string (15)\">Salman Al-Bilal</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (16)\">Residential Area</td><td title=\"string (10)\">OLD-A1-024</td><td title=\"string (10)\">0541587438</td><td title=\"string (10)\">0523101499</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-09-09</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">6100.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>5</th><td title=\"string (2)\">16</td><td title=\"string (15)\">Omar Al-Mustafa</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (16)\">Main Post Office</td><td title=\"string (10)\">OLD-E5-011</td><td title=\"string (10)\">0527775406</td><td title=\"string (10)\">0527775406</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-07-03</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">6090.00</td><td title=\"string (10)\">2025-02-02</td></tr><tr><th>6</th><td title=\"string (2)\">15</td><td title=\"string (12)\">Adnan Al-Ali</td><td title=\"string (24)\"><EMAIL></td><td title=\"string (13)\">Suburb Office</td><td title=\"string (10)\">OLD-A1-010</td><td title=\"string (10)\">0568405834</td><td title=\"string (10)\">0568405834</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-09-01</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">4980.00</td><td title=\"string (10)\">2025-05-24</td></tr><tr><th>7</th><td title=\"string (1)\">6</td><td title=\"string (20)\">Abdulrahman Al-Saeed</td><td title=\"string (32)\"><EMAIL></td><td title=\"string (11)\">East Branch</td><td title=\"string (10)\">REF005-001</td><td title=\"string (10)\">0517446897</td><td title=\"string (10)\">0517446897</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-10-26</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">4760.00</td><td title=\"string (10)\">2025-03-15</td></tr><tr><th>8</th><td title=\"string (2)\">26</td><td title=\"string (15)\">Faisal Al-Hamza</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">OLD-C3-021</td><td title=\"string (10)\">0559259386</td><td title=\"string (10)\">0559259386</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-10-21</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">4690.00</td><td title=\"string (10)\">2025-02-17</td></tr><tr><th>9</th><td title=\"string (2)\">24</td><td title=\"string (16)\">Rashid Al-Rashid</td><td title=\"string (28)\"><EMAIL></td><td title=\"string (16)\">Residential Area</td><td title=\"string (10)\">REF003-019</td><td title=\"string (10)\">0513240716</td><td title=\"string (10)\">0556112667</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2025-03-07</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">4540.00</td><td title=\"string (10)\">2025-04-10</td></tr><tr><th>10</th><td title=\"string (2)\">22</td><td title=\"string (16)\">Fahad Al-Mustafa</td><td title=\"string (28)\"><EMAIL></td><td title=\"string (19)\">Commercial District</td><td title=\"string (10)\">HIST-A-017</td><td title=\"string (10)\">0546804117</td><td title=\"string (10)\">0513423634</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-05-19</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">4460.00</td><td title=\"string (10)\">2025-04-14</td></tr><tr><th>11</th><td title=\"string (2)\">34</td><td title=\"string (15)\">Hassan Al-Ahmad</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (12)\">South Branch</td><td title=\"string (10)\">OLD-A1-029</td><td title=\"string (10)\">0516419744</td><td title=\"string (10)\">0582425147</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-08-20</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">4140.00</td><td title=\"string (10)\">2025-03-09</td></tr><tr><th>12</th><td title=\"string (2)\">20</td><td title=\"string (18)\">Ibrahim Al-Ibrahim</td><td title=\"string (30)\"><EMAIL></td><td title=\"string (19)\">Commercial District</td><td title=\"string (13)\">LEGACY-03-015</td><td title=\"string (10)\">0529224145</td><td title=\"string (10)\">0529224145</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-12-19</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">3840.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>13</th><td title=\"string (2)\">25</td><td title=\"string (16)\">Ismail Al-Waleed</td><td title=\"string (28)\"><EMAIL></td><td title=\"string (12)\">South Branch</td><td title=\"string (13)\">LEGACY-02-020</td><td title=\"string (10)\">0595811659</td><td title=\"string (10)\">0570567329</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-12-12</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">3780.00</td><td title=\"string (10)\">2024-07-25</td></tr><tr><th>14</th><td title=\"string (2)\">11</td><td title=\"string (16)\">Faisal Al-Salman</td><td title=\"string (28)\"><EMAIL></td><td title=\"string (13)\">Suburb Office</td><td title=\"string (10)\">REF005-006</td><td title=\"string (10)\">0538327220</td><td title=\"string (10)\">0543727340</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-10-13</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">3210.00</td><td title=\"string (10)\">2024-10-31</td></tr><tr><th>15</th><td title=\"string (2)\">31</td><td title=\"string (15)\">Badr Al-Ibrahim</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (13)\">Suburb Office</td><td title=\"string (13)\">LEGACY-03-026</td><td title=\"string (10)\">0575605153</td><td title=\"string (10)\">0575605153</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-08-21</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">2790.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>16</th><td title=\"string (2)\">19</td><td title=\"string (15)\">Waleed Al-Adnan</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (11)\">West Branch</td><td title=\"string (10)\">OLD-C3-014</td><td title=\"string (10)\">0511853916</td><td title=\"string (10)\">0565829914</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2025-02-14</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">2710.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>17</th><td title=\"string (1)\">8</td><td title=\"string (17)\">Faisal Al-Mustafa</td><td title=\"string (29)\"><EMAIL></td><td title=\"string (11)\">East Branch</td><td title=\"string (10)\">REF002-003</td><td title=\"string (10)\">0555562008</td><td title=\"string (10)\">0518167587</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-03-03</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">2630.00</td><td title=\"string (10)\">2025-05-09</td></tr><tr><th>18</th><td title=\"string (2)\">33</td><td title=\"string (11)\">Faisal Khan</td><td title=\"string (23)\"><EMAIL></td><td title=\"string (15)\">Industrial Area</td><td title=\"string (10)\">REF005-028</td><td title=\"string (10)\">0526623146</td><td title=\"string (10)\">0526917902</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-06-02</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">2400.00</td><td title=\"string (10)\">2024-11-07</td></tr><tr><th>19</th><td title=\"string (2)\">32</td><td title=\"string (14)\">Fahad Al-Majid</td><td title=\"string (26)\"><EMAIL></td><td title=\"string (19)\">Commercial District</td><td title=\"string (10)\">REF004-027</td><td title=\"string (10)\">0576162670</td><td title=\"string (10)\">0570066376</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-06-24</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">2140.00</td><td title=\"string (10)\">2025-01-10</td></tr><tr><th>20</th><td title=\"string (1)\">4</td><td title=\"string (5)\">Riyas</td><td title=\"string (0)\"></td><td title=\"string (14)\">Perinthalmanna</td><td title=\"string (3)\">990</td><td title=\"string (10)\">9947777100</td><td title=\"string (10)\">9947777100</td><td title=\"string (0)\"></td><td title=\"string (10)\">2025-05-24</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 11:11:41</td><td title=\"string (19)\">2025-05-24 11:11:41</td><td title=\"string (7)\">1800.00</td><td title=\"string (10)\">2025-05-24</td></tr><tr><th>21</th><td title=\"string (1)\">7</td><td title=\"string (9)\">Zaid Khan</td><td title=\"string (21)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">OLD-A1-002</td><td title=\"string (10)\">0537155671</td><td title=\"string (10)\">0537155671</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-04-02</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">1710.00</td><td title=\"string (10)\">2024-09-11</td></tr><tr><th>22</th><td title=\"string (2)\">14</td><td title=\"string (15)\">Ibrahim Al-Saud</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (19)\">Commercial District</td><td title=\"string (12)\">PREV-001-009</td><td title=\"string (10)\">0547398070</td><td title=\"string (10)\">0515678096</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2025-03-17</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">1610.00</td><td title=\"string (10)\">2025-05-19</td></tr><tr><th>23</th><td title=\"string (2)\">27</td><td title=\"string (16)\">Salman Al-Salman</td><td title=\"string (28)\"><EMAIL></td><td title=\"string (12)\">South Branch</td><td title=\"string (10)\">REF004-022</td><td title=\"string (10)\">0582001879</td><td title=\"string (10)\">0568616961</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-11-17</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">1520.00</td><td title=\"string (10)\">2025-05-08</td></tr><tr><th>24</th><td title=\"string (2)\">12</td><td title=\"string (14)\">Amjad Al-Adnan</td><td title=\"string (26)\"><EMAIL></td><td title=\"string (13)\">Suburb Office</td><td title=\"string (13)\">LEGACY-03-007</td><td title=\"string (10)\">0570176974</td><td title=\"string (10)\">0570176974</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-08-09</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">1330.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>25</th><td title=\"string (1)\">9</td><td title=\"string (15)\">Tariq Al-Salman</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (19)\">Commercial District</td><td title=\"string (10)\">OLD-B2-004</td><td title=\"string (10)\">0567188437</td><td title=\"string (10)\">0567188437</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2025-04-08</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">1320.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>26</th><td title=\"string (2)\">35</td><td title=\"string (18)\">Ismail Al-Abdullah</td><td title=\"string (30)\"><EMAIL></td><td title=\"string (19)\">Central Post Office</td><td title=\"string (10)\">REF002-030</td><td title=\"string (10)\">0529592026</td><td title=\"string (10)\">0538836358</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-12-26</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (6)\">400.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>27</th><td title=\"string (2)\">23</td><td title=\"string (11)\">Ali Al-Omar</td><td title=\"string (23)\"><EMAIL></td><td title=\"string (19)\">Central Post Office</td><td title=\"string (10)\">HIST-A-018</td><td title=\"string (10)\">0527527389</td><td title=\"string (10)\">0527527389</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-07-15</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (6)\">280.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>28</th><td title=\"string (2)\">17</td><td title=\"string (13)\">Saud Al-Bilal</td><td title=\"string (25)\"><EMAIL></td><td title=\"string (19)\">Commercial District</td><td title=\"string (10)\">REF005-012</td><td title=\"string (10)\">0561548613</td><td title=\"string (10)\">0561548613</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-08-06</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (6)\">260.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>29</th><td title=\"string (2)\">13</td><td title=\"string (19)\">Abdullah Al-Mahmoud</td><td title=\"string (31)\"><EMAIL></td><td title=\"string (11)\">West Branch</td><td title=\"string (13)\">LEGACY-03-008</td><td title=\"string (10)\">0592620160</td><td title=\"string (10)\">0592620160</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2023-09-27</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (6)\">140.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>30</th><td title=\"string (1)\">1</td><td title=\"string (14)\">Abdu Raheem KK</td><td title=\"string (18)\"><EMAIL></td><td title=\"string (10)\">Karalmanna</td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777125</td><td title=\"string (10)\">9947777125</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2018-01-01</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 22:16:26</td><td title=\"string (19)\">2025-05-24 10:30:28</td><td title=\"string (4)\">0.00</td><td title=\"string (10)\">2025-05-24</td></tr><tr><th>31</th><td title=\"string (1)\">2</td><td title=\"string (7)\">Subeena</td><td title=\"string (15)\"><EMAIL></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777124</td><td title=\"string (10)\">9605447793</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2025-05-18</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 23:25:30</td><td title=\"string (19)\">2025-05-24 10:30:55</td><td title=\"string (4)\">0.00</td><td title=\"string (10)\">2025-03-17</td></tr><tr><th>32</th><td title=\"string (1)\">3</td><td title=\"string (5)\">Murad</td><td title=\"string (17)\"><EMAIL></td><td title=\"string (10)\">Karalmanna</td><td title=\"null\"><var>null</var></td><td title=\"string (9)\">996677889</td><td title=\"null\"><var>null</var></td><td title=\"string (7)\">Nellaea</td><td title=\"string (10)\">2025-04-08</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-18 00:41:57</td><td title=\"string (19)\">2025-05-18 14:00:36</td><td title=\"string (4)\">0.00</td><td title=\"string (10)\">2025-05-24</td></tr><tr><th>33</th><td title=\"string (1)\">5</td><td title=\"string (14)\">Omar Al-Nasser</td><td title=\"string (26)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">HIST-C-001</td><td title=\"string (10)\">0537494636</td><td title=\"string (10)\">0572364484</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-11-09</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:20</td><td title=\"string (19)\">2025-05-24 21:58:20</td><td title=\"string (4)\">0.00</td><td title=\"null\"><var>null</var></td></tr><tr><th>34</th><td title=\"string (2)\">10</td><td title=\"string (15)\">Fahad Al-Faisal</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (15)\">Industrial Area</td><td title=\"string (12)\">PREV-002-005</td><td title=\"string (10)\">0540420476</td><td title=\"string (10)\">0579349018</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-03-14</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (7)\">-220.00</td><td title=\"string (10)\">2025-04-18</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (13) \"Saud Al-Ahmad\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (25) \"<EMAIL>\"<div class=\"access-path\">$value[0]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[0]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (13) \"LEGACY-01-023\"<div class=\"access-path\">$value[0]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0592485468\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>1988-10-10T11:17:48+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0554246003\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>1987-07-25T21:13:23+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[0]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-06-11\"<div class=\"access-path\">$value[0]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"9200.00\"<div class=\"access-path\">$value[0]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-11-10\"<div class=\"access-path\">$value[0]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Bilal Al-Waleed\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[1]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (15) \"Industrial Area\"<div class=\"access-path\">$value[1]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (13) \"LEGACY-02-013\"<div class=\"access-path\">$value[1]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0515351788\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>1986-05-01T17:16:28+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0522375982\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>1986-07-22T00:26:22+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[1]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-08-22\"<div class=\"access-path\">$value[1]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8770.00\"<div class=\"access-path\">$value[1]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-07\"<div class=\"access-path\">$value[1]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Bilal Al-Jamal\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value[2]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (19) \"Central Post Office\"<div class=\"access-path\">$value[2]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (12) \"PREV-001-016\"<div class=\"access-path\">$value[2]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0528141858\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1986-09-26T18:04:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523298126\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1986-08-01T16:35:26+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-08-28\"<div class=\"access-path\">$value[2]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8540.00\"<div class=\"access-path\">$value[2]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-10\"<div class=\"access-path\">$value[2]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (17) \"Ibrahim Al-Hassan\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (29) \"<EMAIL>\"<div class=\"access-path\">$value[3]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[3]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"HIST-B-025\"<div class=\"access-path\">$value[3]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0549461895\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1987-05-31T12:18:15+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0582341569\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1988-06-15T01:32:49+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-05-26\"<div class=\"access-path\">$value[3]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6690.00\"<div class=\"access-path\">$value[3]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Salman Al-Bilal\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[4]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (16) \"Residential Area\"<div class=\"access-path\">$value[4]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-A1-024\"<div class=\"access-path\">$value[4]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0541587438\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1987-03-01T08:57:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523101499\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1986-07-30T09:58:19+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-09-09\"<div class=\"access-path\">$value[4]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6100.00\"<div class=\"access-path\">$value[4]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>5</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[5]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"16\"<div class=\"access-path\">$value[5]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Omar Al-Mustafa\"<div class=\"access-path\">$value[5]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[5]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (16) \"Main Post Office\"<div class=\"access-path\">$value[5]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-E5-011\"<div class=\"access-path\">$value[5]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0527775406\"<div class=\"access-path\">$value[5]['phone']</div></dt><dd><pre>1986-09-22T12:16:46+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0527775406\"<div class=\"access-path\">$value[5]['whatsapp_number']</div></dt><dd><pre>1986-09-22T12:16:46+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[5]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-07-03\"<div class=\"access-path\">$value[5]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[5]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[5]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[5]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6090.00\"<div class=\"access-path\">$value[5]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-02-02\"<div class=\"access-path\">$value[5]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>6</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[6]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[6]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (12) \"Adnan Al-Ali\"<div class=\"access-path\">$value[6]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (24) \"<EMAIL>\"<div class=\"access-path\">$value[6]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (13) \"Suburb Office\"<div class=\"access-path\">$value[6]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-A1-010\"<div class=\"access-path\">$value[6]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0568405834\"<div class=\"access-path\">$value[6]['phone']</div></dt><dd><pre>1988-01-05T18:30:34+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0568405834\"<div class=\"access-path\">$value[6]['whatsapp_number']</div></dt><dd><pre>1988-01-05T18:30:34+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[6]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-09-01\"<div class=\"access-path\">$value[6]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[6]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[6]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[6]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"4980.00\"<div class=\"access-path\">$value[6]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[6]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>7</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[7]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[7]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (20) \"Abdulrahman Al-Saeed\"<div class=\"access-path\">$value[7]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (32) \"<EMAIL>\"<div class=\"access-path\">$value[7]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (11) \"East Branch\"<div class=\"access-path\">$value[7]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"REF005-001\"<div class=\"access-path\">$value[7]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0517446897\"<div class=\"access-path\">$value[7]['phone']</div></dt><dd><pre>1986-05-25T23:14:57+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0517446897\"<div class=\"access-path\">$value[7]['whatsapp_number']</div></dt><dd><pre>1986-05-25T23:14:57+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[7]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-10-26\"<div class=\"access-path\">$value[7]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[7]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[7]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[7]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"4760.00\"<div class=\"access-path\">$value[7]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-15\"<div class=\"access-path\">$value[7]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>8</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[8]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[8]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Faisal Al-Hamza\"<div class=\"access-path\">$value[8]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[8]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[8]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-C3-021\"<div class=\"access-path\">$value[8]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0559259386\"<div class=\"access-path\">$value[8]['phone']</div></dt><dd><pre>1987-09-21T21:49:46+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0559259386\"<div class=\"access-path\">$value[8]['whatsapp_number']</div></dt><dd><pre>1987-09-21T21:49:46+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[8]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-10-21\"<div class=\"access-path\">$value[8]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[8]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[8]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[8]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"4690.00\"<div class=\"access-path\">$value[8]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-02-17\"<div class=\"access-path\">$value[8]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>9</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[9]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"24\"<div class=\"access-path\">$value[9]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Rashid Al-Rashid\"<div class=\"access-path\">$value[9]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (28) \"<EMAIL>\"<div class=\"access-path\">$value[9]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (16) \"Residential Area\"<div class=\"access-path\">$value[9]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"REF003-019\"<div class=\"access-path\">$value[9]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0513240716\"<div class=\"access-path\">$value[9]['phone']</div></dt><dd><pre>1986-04-07T06:51:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0556112667\"<div class=\"access-path\">$value[9]['whatsapp_number']</div></dt><dd><pre>1987-08-16T11:44:27+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[9]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-03-07\"<div class=\"access-path\">$value[9]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[9]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[9]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[9]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"4540.00\"<div class=\"access-path\">$value[9]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-04-10\"<div class=\"access-path\">$value[9]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>10</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[10]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"22\"<div class=\"access-path\">$value[10]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Fahad Al-Mustafa\"<div class=\"access-path\">$value[10]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (28) \"<EMAIL>\"<div class=\"access-path\">$value[10]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (19) \"Commercial District\"<div class=\"access-path\">$value[10]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"HIST-A-017\"<div class=\"access-path\">$value[10]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0546804117\"<div class=\"access-path\">$value[10]['phone']</div></dt><dd><pre>1987-04-30T18:01:57+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0513423634\"<div class=\"access-path\">$value[10]['whatsapp_number']</div></dt><dd><pre>1986-04-09T09:40:34+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[10]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-05-19\"<div class=\"access-path\">$value[10]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[10]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[10]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[10]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"4460.00\"<div class=\"access-path\">$value[10]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-04-14\"<div class=\"access-path\">$value[10]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>11</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[11]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"34\"<div class=\"access-path\">$value[11]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Hassan Al-Ahmad\"<div class=\"access-path\">$value[11]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[11]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (12) \"South Branch\"<div class=\"access-path\">$value[11]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-A1-029\"<div class=\"access-path\">$value[11]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0516419744\"<div class=\"access-path\">$value[11]['phone']</div></dt><dd><pre>1986-05-14T01:55:44+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0582425147\"<div class=\"access-path\">$value[11]['whatsapp_number']</div></dt><dd><pre>1988-06-16T00:45:47+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[11]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-08-20\"<div class=\"access-path\">$value[11]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[11]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[11]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[11]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"4140.00\"<div class=\"access-path\">$value[11]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-09\"<div class=\"access-path\">$value[11]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>12</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[12]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"20\"<div class=\"access-path\">$value[12]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (18) \"Ibrahim Al-Ibrahim\"<div class=\"access-path\">$value[12]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (30) \"<EMAIL>\"<div class=\"access-path\">$value[12]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (19) \"Commercial District\"<div class=\"access-path\">$value[12]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (13) \"LEGACY-03-015\"<div class=\"access-path\">$value[12]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0529224145\"<div class=\"access-path\">$value[12]['phone']</div></dt><dd><pre>1986-10-09T06:42:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0529224145\"<div class=\"access-path\">$value[12]['whatsapp_number']</div></dt><dd><pre>1986-10-09T06:42:25+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-12-19\"<div class=\"access-path\">$value[12]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[12]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[12]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[12]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"3840.00\"<div class=\"access-path\">$value[12]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[12]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>13</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[13]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"25\"<div class=\"access-path\">$value[13]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Ismail Al-Waleed\"<div class=\"access-path\">$value[13]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (28) \"<EMAIL>\"<div class=\"access-path\">$value[13]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (12) \"South Branch\"<div class=\"access-path\">$value[13]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (13) \"LEGACY-02-020\"<div class=\"access-path\">$value[13]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0595811659\"<div class=\"access-path\">$value[13]['phone']</div></dt><dd><pre>1988-11-17T23:14:19+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0570567329\"<div class=\"access-path\">$value[13]['whatsapp_number']</div></dt><dd><pre>1988-01-30T18:55:29+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[13]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-12-12\"<div class=\"access-path\">$value[13]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[13]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[13]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[13]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"3780.00\"<div class=\"access-path\">$value[13]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-07-25\"<div class=\"access-path\">$value[13]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>14</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[14]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"11\"<div class=\"access-path\">$value[14]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Faisal Al-Salman\"<div class=\"access-path\">$value[14]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (28) \"<EMAIL>\"<div class=\"access-path\">$value[14]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (13) \"Suburb Office\"<div class=\"access-path\">$value[14]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"REF005-006\"<div class=\"access-path\">$value[14]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0538327220\"<div class=\"access-path\">$value[14]['phone']</div></dt><dd><pre>1987-01-22T15:20:20+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0543727340\"<div class=\"access-path\">$value[14]['whatsapp_number']</div></dt><dd><pre>1987-03-26T03:22:20+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[14]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-10-13\"<div class=\"access-path\">$value[14]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[14]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[14]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[14]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"3210.00\"<div class=\"access-path\">$value[14]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-10-31\"<div class=\"access-path\">$value[14]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>15</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[15]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"31\"<div class=\"access-path\">$value[15]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Badr Al-Ibrahim\"<div class=\"access-path\">$value[15]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[15]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (13) \"Suburb Office\"<div class=\"access-path\">$value[15]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (13) \"LEGACY-03-026\"<div class=\"access-path\">$value[15]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0575605153\"<div class=\"access-path\">$value[15]['phone']</div></dt><dd><pre>1988-03-29T02:19:13+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0575605153\"<div class=\"access-path\">$value[15]['whatsapp_number']</div></dt><dd><pre>1988-03-29T02:19:13+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[15]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-08-21\"<div class=\"access-path\">$value[15]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[15]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[15]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[15]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"2790.00\"<div class=\"access-path\">$value[15]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[15]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>16</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[16]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"19\"<div class=\"access-path\">$value[16]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Waleed Al-Adnan\"<div class=\"access-path\">$value[16]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[16]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (11) \"West Branch\"<div class=\"access-path\">$value[16]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-C3-014\"<div class=\"access-path\">$value[16]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0511853916\"<div class=\"access-path\">$value[16]['phone']</div></dt><dd><pre>1986-03-22T05:38:36+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0565829914\"<div class=\"access-path\">$value[16]['whatsapp_number']</div></dt><dd><pre>1987-12-06T22:58:34+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[16]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-02-14\"<div class=\"access-path\">$value[16]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[16]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[16]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[16]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"2710.00\"<div class=\"access-path\">$value[16]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[16]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>17</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[17]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"8\"<div class=\"access-path\">$value[17]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (17) \"Faisal Al-Mustafa\"<div class=\"access-path\">$value[17]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (29) \"<EMAIL>\"<div class=\"access-path\">$value[17]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (11) \"East Branch\"<div class=\"access-path\">$value[17]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"REF002-003\"<div class=\"access-path\">$value[17]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0555562008\"<div class=\"access-path\">$value[17]['phone']</div></dt><dd><pre>1987-08-10T02:46:48+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0518167587\"<div class=\"access-path\">$value[17]['whatsapp_number']</div></dt><dd><pre>1986-06-03T07:26:27+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[17]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-03-03\"<div class=\"access-path\">$value[17]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[17]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[17]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[17]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"2630.00\"<div class=\"access-path\">$value[17]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-09\"<div class=\"access-path\">$value[17]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>18</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[18]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"33\"<div class=\"access-path\">$value[18]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (11) \"Faisal Khan\"<div class=\"access-path\">$value[18]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (23) \"<EMAIL>\"<div class=\"access-path\">$value[18]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (15) \"Industrial Area\"<div class=\"access-path\">$value[18]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"REF005-028\"<div class=\"access-path\">$value[18]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0526623146\"<div class=\"access-path\">$value[18]['phone']</div></dt><dd><pre>1986-09-09T04:12:26+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0526917902\"<div class=\"access-path\">$value[18]['whatsapp_number']</div></dt><dd><pre>1986-09-12T14:05:02+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[18]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-06-02\"<div class=\"access-path\">$value[18]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[18]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[18]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[18]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"2400.00\"<div class=\"access-path\">$value[18]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-11-07\"<div class=\"access-path\">$value[18]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>19</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[19]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"32\"<div class=\"access-path\">$value[19]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Fahad Al-Majid\"<div class=\"access-path\">$value[19]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value[19]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (19) \"Commercial District\"<div class=\"access-path\">$value[19]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"REF004-027\"<div class=\"access-path\">$value[19]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0576162670\"<div class=\"access-path\">$value[19]['phone']</div></dt><dd><pre>1988-04-04T13:11:10+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0570066376\"<div class=\"access-path\">$value[19]['whatsapp_number']</div></dt><dd><pre>1988-01-24T23:46:16+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[19]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-06-24\"<div class=\"access-path\">$value[19]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[19]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[19]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[19]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"2140.00\"<div class=\"access-path\">$value[19]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-01-10\"<div class=\"access-path\">$value[19]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>20</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[20]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[20]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (5) \"Riyas\"<div class=\"access-path\">$value[20]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[20]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (14) \"Perinthalmanna\"<div class=\"access-path\">$value[20]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (3) \"990\"<div class=\"access-path\">$value[20]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777100\"<div class=\"access-path\">$value[20]['phone']</div></dt><dd><pre>2285-03-26T07:25:00+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9947777100\"<div class=\"access-path\">$value[20]['whatsapp_number']</div></dt><dd><pre>2285-03-26T07:25:00+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[20]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[20]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[20]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 11:11:41\"<div class=\"access-path\">$value[20]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 11:11:41\"<div class=\"access-path\">$value[20]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"1800.00\"<div class=\"access-path\">$value[20]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[20]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>21</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[21]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[21]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (9) \"Zaid Khan\"<div class=\"access-path\">$value[21]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (21) \"<EMAIL>\"<div class=\"access-path\">$value[21]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[21]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-A1-002\"<div class=\"access-path\">$value[21]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[21]['phone']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[21]['whatsapp_number']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[21]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-04-02\"<div class=\"access-path\">$value[21]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[21]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[21]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[21]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"1710.00\"<div class=\"access-path\">$value[21]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-09-11\"<div class=\"access-path\">$value[21]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>22</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[22]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[22]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Ibrahim Al-Saud\"<div class=\"access-path\">$value[22]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[22]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (19) \"Commercial District\"<div class=\"access-path\">$value[22]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (12) \"PREV-001-009\"<div class=\"access-path\">$value[22]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0547398070\"<div class=\"access-path\">$value[22]['phone']</div></dt><dd><pre>1987-05-07T15:01:10+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0515678096\"<div class=\"access-path\">$value[22]['whatsapp_number']</div></dt><dd><pre>1986-05-05T11:54:56+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[22]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-03-17\"<div class=\"access-path\">$value[22]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[22]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[22]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[22]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"1610.00\"<div class=\"access-path\">$value[22]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-19\"<div class=\"access-path\">$value[22]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>23</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[23]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"27\"<div class=\"access-path\">$value[23]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (16) \"Salman Al-Salman\"<div class=\"access-path\">$value[23]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (28) \"<EMAIL>\"<div class=\"access-path\">$value[23]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (12) \"South Branch\"<div class=\"access-path\">$value[23]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"REF004-022\"<div class=\"access-path\">$value[23]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0582001879\"<div class=\"access-path\">$value[23]['phone']</div></dt><dd><pre>1988-06-11T03:11:19+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0568616961\"<div class=\"access-path\">$value[23]['whatsapp_number']</div></dt><dd><pre>1988-01-08T05:09:21+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[23]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-11-17\"<div class=\"access-path\">$value[23]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[23]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[23]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[23]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"1520.00\"<div class=\"access-path\">$value[23]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-08\"<div class=\"access-path\">$value[23]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>24</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[24]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"12\"<div class=\"access-path\">$value[24]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Amjad Al-Adnan\"<div class=\"access-path\">$value[24]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value[24]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (13) \"Suburb Office\"<div class=\"access-path\">$value[24]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (13) \"LEGACY-03-007\"<div class=\"access-path\">$value[24]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0570176974\"<div class=\"access-path\">$value[24]['phone']</div></dt><dd><pre>1988-01-26T06:29:34+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0570176974\"<div class=\"access-path\">$value[24]['whatsapp_number']</div></dt><dd><pre>1988-01-26T06:29:34+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[24]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-08-09\"<div class=\"access-path\">$value[24]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[24]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[24]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[24]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"1330.00\"<div class=\"access-path\">$value[24]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[24]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>25</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[25]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"9\"<div class=\"access-path\">$value[25]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Tariq Al-Salman\"<div class=\"access-path\">$value[25]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[25]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (19) \"Commercial District\"<div class=\"access-path\">$value[25]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-B2-004\"<div class=\"access-path\">$value[25]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0567188437\"<div class=\"access-path\">$value[25]['phone']</div></dt><dd><pre>1987-12-22T16:20:37+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0567188437\"<div class=\"access-path\">$value[25]['whatsapp_number']</div></dt><dd><pre>1987-12-22T16:20:37+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[25]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-04-08\"<div class=\"access-path\">$value[25]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[25]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[25]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[25]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"1320.00\"<div class=\"access-path\">$value[25]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[25]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>26</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[26]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"35\"<div class=\"access-path\">$value[26]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (18) \"Ismail Al-Abdullah\"<div class=\"access-path\">$value[26]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (30) \"<EMAIL>\"<div class=\"access-path\">$value[26]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (19) \"Central Post Office\"<div class=\"access-path\">$value[26]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"REF002-030\"<div class=\"access-path\">$value[26]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0529592026\"<div class=\"access-path\">$value[26]['phone']</div></dt><dd><pre>1986-10-13T12:53:46+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0538836358\"<div class=\"access-path\">$value[26]['whatsapp_number']</div></dt><dd><pre>1987-01-28T12:45:58+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[26]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-12-26\"<div class=\"access-path\">$value[26]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[26]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[26]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[26]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (6) \"400.00\"<div class=\"access-path\">$value[26]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[26]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>27</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[27]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"23\"<div class=\"access-path\">$value[27]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (11) \"Ali Al-Omar\"<div class=\"access-path\">$value[27]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (23) \"<EMAIL>\"<div class=\"access-path\">$value[27]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (19) \"Central Post Office\"<div class=\"access-path\">$value[27]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"HIST-A-018\"<div class=\"access-path\">$value[27]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0527527389\"<div class=\"access-path\">$value[27]['phone']</div></dt><dd><pre>1986-09-19T15:23:09+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0527527389\"<div class=\"access-path\">$value[27]['whatsapp_number']</div></dt><dd><pre>1986-09-19T15:23:09+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[27]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-07-15\"<div class=\"access-path\">$value[27]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[27]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[27]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[27]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (6) \"280.00\"<div class=\"access-path\">$value[27]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[27]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>28</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[28]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"17\"<div class=\"access-path\">$value[28]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (13) \"Saud Al-Bilal\"<div class=\"access-path\">$value[28]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (25) \"<EMAIL>\"<div class=\"access-path\">$value[28]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (19) \"Commercial District\"<div class=\"access-path\">$value[28]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"REF005-012\"<div class=\"access-path\">$value[28]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0561548613\"<div class=\"access-path\">$value[28]['phone']</div></dt><dd><pre>1987-10-18T09:43:33+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0561548613\"<div class=\"access-path\">$value[28]['whatsapp_number']</div></dt><dd><pre>1987-10-18T09:43:33+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[28]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-08-06\"<div class=\"access-path\">$value[28]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[28]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[28]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[28]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (6) \"260.00\"<div class=\"access-path\">$value[28]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[28]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>29</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[29]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"13\"<div class=\"access-path\">$value[29]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (19) \"Abdullah Al-Mahmoud\"<div class=\"access-path\">$value[29]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (31) \"<EMAIL>\"<div class=\"access-path\">$value[29]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (11) \"West Branch\"<div class=\"access-path\">$value[29]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (13) \"LEGACY-03-008\"<div class=\"access-path\">$value[29]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0592620160\"<div class=\"access-path\">$value[29]['phone']</div></dt><dd><pre>1988-10-12T00:42:40+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0592620160\"<div class=\"access-path\">$value[29]['whatsapp_number']</div></dt><dd><pre>1988-10-12T00:42:40+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[29]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2023-09-27\"<div class=\"access-path\">$value[29]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[29]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[29]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[29]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (6) \"140.00\"<div class=\"access-path\">$value[29]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[29]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>30</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[30]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[30]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Abdu Raheem KK\"<div class=\"access-path\">$value[30]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (18) \"<EMAIL>\"<div class=\"access-path\">$value[30]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (10) \"Karalmanna\"<div class=\"access-path\">$value[30]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[30]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[30]['phone']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[30]['whatsapp_number']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[30]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2018-01-01\"<div class=\"access-path\">$value[30]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[30]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 22:16:26\"<div class=\"access-path\">$value[30]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:28\"<div class=\"access-path\">$value[30]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[30]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[30]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>31</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[31]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[31]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (7) \"Subeena\"<div class=\"access-path\">$value[31]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (15) \"<EMAIL>\"<div class=\"access-path\">$value[31]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[31]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[31]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777124\"<div class=\"access-path\">$value[31]['phone']</div></dt><dd><pre>2285-03-26T07:25:24+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9605447793\"<div class=\"access-path\">$value[31]['whatsapp_number']</div></dt><dd><pre>2274-05-21T03:56:33+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[31]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-05-18\"<div class=\"access-path\">$value[31]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[31]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 23:25:30\"<div class=\"access-path\">$value[31]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:55\"<div class=\"access-path\">$value[31]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[31]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-17\"<div class=\"access-path\">$value[31]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>32</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[32]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[32]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (5) \"Murad\"<div class=\"access-path\">$value[32]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (17) \"<EMAIL>\"<div class=\"access-path\">$value[32]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (10) \"Karalmanna\"<div class=\"access-path\">$value[32]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[32]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (9) \"996677889\"<div class=\"access-path\">$value[32]['phone']</div></dt><dd><pre>2001-08-01T14:58:09+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>whatsapp_number</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[32]['whatsapp_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>string</var> (7) \"Nellaea\"<div class=\"access-path\">$value[32]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-04-08\"<div class=\"access-path\">$value[32]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[32]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 00:41:57\"<div class=\"access-path\">$value[32]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-18 14:00:36\"<div class=\"access-path\">$value[32]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[32]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[32]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>33</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[33]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[33]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Omar Al-Nasser\"<div class=\"access-path\">$value[33]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value[33]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[33]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"HIST-C-001\"<div class=\"access-path\">$value[33]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537494636\"<div class=\"access-path\">$value[33]['phone']</div></dt><dd><pre>1987-01-13T00:03:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0572364484\"<div class=\"access-path\">$value[33]['whatsapp_number']</div></dt><dd><pre>1988-02-20T14:08:04+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[33]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-11-09\"<div class=\"access-path\">$value[33]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[33]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[33]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[33]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[33]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[33]['last_payment_date']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>34</dfn> =&gt; <var>array</var> (14)<div class=\"access-path\">$value[34]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[34]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Fahad Al-Faisal\"<div class=\"access-path\">$value[34]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[34]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (15) \"Industrial Area\"<div class=\"access-path\">$value[34]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (12) \"PREV-002-005\"<div class=\"access-path\">$value[34]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0540420476\"<div class=\"access-path\">$value[34]['phone']</div></dt><dd><pre>1987-02-15T20:47:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0579349018\"<div class=\"access-path\">$value[34]['whatsapp_number']</div></dt><dd><pre>1988-05-11T10:16:58+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[34]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-03-14\"<div class=\"access-path\">$value[34]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[34]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[34]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[34]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"-220.00\"<div class=\"access-path\">$value[34]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-04-18\"<div class=\"access-path\">$value[34]['last_payment_date']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "pagination": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (11)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>current_page</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value['current_page']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>per_page</dfn> =&gt; <var>integer</var> 50<div class=\"access-path\">$value['per_page']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_items</dfn> =&gt; <var>integer</var> 35<div class=\"access-path\">$value['total_items']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_pages</dfn> =&gt; <var>double</var> 1<div class=\"access-path\">$value['total_pages']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>has_previous</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value['has_previous']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>has_next</dfn> =&gt; <var>boolean</var> false<div class=\"access-path\">$value['has_next']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>previous_page</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value['previous_page']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>next_page</dfn> =&gt; <var>double</var> 1<div class=\"access-path\">$value['next_page']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>base_url</dfn> =&gt; <var>string</var> (39) \"https://halqa.mazharulirfan.com/members\"<div class=\"access-path\">$value['base_url']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>showing_from</dfn> =&gt; <var>integer</var> 1<div class=\"access-path\">$value['showing_from']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>showing_to</dfn> =&gt; <var>integer</var> 35<div class=\"access-path\">$value['showing_to']</div></dt></dl></dd></dl></div>", "search": "", "order_by": "balance", "order_dir": "DESC", "total_members": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 35</dt></dl></div>"}}, "session": {"_ci_previous_url": "https://halqa.mazharulirfan.com/members", "admin_id": "2", "username": "boss", "isLoggedIn": "<pre>1</pre>", "__ci_vars": "<pre>Array\n(\n)\n</pre>"}, "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Accept-Encoding": "br", "Accept-Language": "en-US,en;q=0.9", "Cookie": "_ga=GA1.2.1969489433.1748457976; _gid=GA1.2.22748706.1748457976; _ga_9Q6H0QETRF=GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0; ci_session=ff6d48739e278ba41235653988620d29", "Host": "halqa.mazharulirfan.com", "Referer": "https://halqa.mazharulirfan.com/reports", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "X-Forwarded-For": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Forwarded-Proto": "https", "X-Real-Ip": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Real-Port": "59350", "X-Forwarded-Port": "443", "X-Port": "443", "X-Lscache": "1", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;macOS&quot;", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Priority": "u=0, i"}, "cookies": {"_ga": "GA1.2.1969489433.1748457976", "_gid": "GA1.2.22748706.1748457976", "_ga_9Q6H0QETRF": "GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0", "ci_session": "ff6d48739e278ba41235653988620d29"}, "request": "HTTPS/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.27", "phpSAPI": "litespeed", "environment": "development", "baseURL": "https://halqa.mazharulirfan.com/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}