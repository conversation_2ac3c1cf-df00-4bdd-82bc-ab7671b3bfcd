{"url": "https://halqa.mazharulirfan.com/reports", "method": "GET", "isAJAX": false, "startTime": **********.701173, "totalTime": 84.6, "totalMemory": "1.429", "segmentDuration": 15, "segmentCount": 6, "CI_VERSION": "4.6.1", "collectors": [{"title": "Timers", "titleSafe": "timers", "titleDetails": "", "display": [], "badgeValue": null, "isEmpty": false, "hasTabContent": false, "hasLabel": false, "icon": "", "hasTimelineData": true, "timelineData": [{"name": "Bootstrap", "component": "Timer", "start": **********.701427, "duration": 0.0015671253204345703}, {"name": "Required Before Filters", "component": "Timer", "start": **********.702995, "duration": 0.0006248950958251953}, {"name": "Routing", "component": "Timer", "start": **********.703625, "duration": 0.00037097930908203125}, {"name": "Before Filters", "component": "Timer", "start": **********.704038, "duration": 0.0007371902465820312}, {"name": "Controller", "component": "Timer", "start": **********.704777, "duration": 0.****************}, {"name": "Controller Con<PERSON><PERSON><PERSON>", "component": "Timer", "start": **********.704778, "duration": 0.00043892860412597656}, {"name": "After Filters", "component": "Timer", "start": **********.785651, "duration": 3.814697265625e-05}, {"name": "Required After Filters", "component": "Timer", "start": **********.785718, "duration": 0.00012111663818359375}]}, {"title": "Database", "titleSafe": "database", "titleDetails": "(100 total Queries, 79 of them unique across 1 Connection)", "display": {"queries": [{"hover": "", "class": "", "duration": "0.68 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:36", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:36", "qid": "fb713c6958b05de512118efdcb639110"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:37", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:37", "qid": "c8d8b338efeb8529d018764ead8b5667"}, {"hover": "", "class": "", "duration": "0.59 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `members`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:41", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:41", "qid": "b957de4ca5f154ec4d1641ce84bbf47b"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "8146bc7c72afbcfbfb4b899b14c6cafa"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "af41be2fab93f286457579e0cc865ece"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "17f0f131b962bced9024d5b7a2b23971"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "facba1ab10bed867f54d4eba5e6923d2"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "bcb63ecb3e6f287d599c54c09b44091b"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "8fd8ddf2901b3aee205529fc68e9de17"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "f062814e54e9a048fb4821a02d9c28de"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "cfd6394ed8beb0f5510a03d9044e091f"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "c08bbc6f5f129e731aeae7e85a71dcae"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "7258a2ff7fc6387f3ac124b3aaed6d4c"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "a2e814bdc13481768ce6f8a96d46bcb4"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "54b9aedbd82f1b38f06926461277cb0a"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "9ee18ebec4a09dc9124c967e577cc40e"}, {"hover": "", "class": "", "duration": "0.37 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "7e78fd921a2e2f5574af64171015e48f"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "d18b72bc7998a9e050a3abec2ff95e6e"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "2eff40877af2acaf8cb4b9fefff471fd"}, {"hover": "", "class": "", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "5fd12229046e127ec6fecce8bb39869b"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "4c14cd482076caa1614cea3a212bcf21"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "34d7eeaad990c3ff58ff2f114ca294c0"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "7b3a97057770c66bc6899bf5f2c03255"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "5ae4d3a148dbe0ccb4cd80470c95a7b4"}, {"hover": "", "class": "", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "1077d3809338ae8b8c2da93072f62242"}, {"hover": "", "class": "", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "e85d44b48225f776f5c34a2477084e51"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "bd2d579495818ac73131bd81c2ce8b1f"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "b8588801da850e1cf682b4d90491f722"}, {"hover": "", "class": "", "duration": "0.32 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "8080581f7982dcc926198cfabb104eb2"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "d6036368c413e07afd4a0cab975b191e"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "200c47f6fa5e4f3f5310977bf03ef6c6"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "51049541dd346f1d69f3d0885ac86193"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "076526d6c3914011b6e2846c356f69fd"}, {"hover": "", "class": "", "duration": "0.3 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "10f972afa396849a10e1c479fabc2ccf"}, {"hover": "", "class": "", "duration": "0.29 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "bd59c5f3785db296cd94bb8aa60aba5e"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "7d1e09e51bd919d53e5bb002a9e8fbd9"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "6fbc18502bc4d43aaba12b7ca31f3d60"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:286", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:679", "function": "        CodeIgniter\\Model->doFindAll()", "index": "  3    "}, {"file": "APPPATH/Models/CommitmentModel.php:96", "function": "        CodeIgniter\\BaseModel->findAll()", "index": "  4    "}, {"file": "APPPATH/Controllers/Reports.php:43", "function": "        App\\Models\\CommitmentModel->calculateTotalCommitment()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  7    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  9    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": " 10    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 11    "}], "trace-file": "APPPATH/Models/CommitmentModel.php:96", "qid": "9ad3c7b6cf02ff7288d2da3a1817fe31"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> <strong>SUM</strong>(`amount`) <strong>AS</strong> `amount`\n<strong>FROM</strong> `payments`\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:47", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:47", "qid": "16c7e631b56255c2166e44c7aa73ec29"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> <strong>SUM</strong>(`balance`) <strong>AS</strong> `balance`\n<strong>FROM</strong> `collection_summary`\n <strong>LIMIT</strong> 1", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:330", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/BaseModel.php:717", "function": "        CodeIgniter\\Model->doFirst()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:50", "function": "        CodeIgniter\\BaseModel->first()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:50", "qid": "375e98fff1f59ba37762a73496b2a1f7"}, {"hover": "", "class": "", "duration": "1.68 ms", "sql": "<strong>SELECT</strong> `p`.*, `m`.`name` as `member_name`, `c`.`amount` as `commitment_amount`, `c`.`frequency` as `commitment_frequency`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `p`.`member_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `c`.`commitment_id` = `p`.`commitment_id`\n<strong>ORDER</strong> <strong>BY</strong> `p`.`payment_date` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/PaymentModel.php:113", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:53", "function": "        App\\Models\\PaymentModel->getPaymentsWithMemberDetails()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/PaymentModel.php:113", "qid": "ab06e1956879643986ed93c95947d54b"}, {"hover": "", "class": "", "duration": "0.64 ms", "sql": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`, `m`.`phone`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `cs`.`balance` &gt; 0\n<strong>AND</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `cs`.`balance` <strong>DESC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:334", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:57", "function": "        App\\Models\\CollectionSummaryModel->getMembersWithOutstandingBalances()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:334", "qid": "7bacc0cd321294eb827d6aa86311a2db"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "fd5b90ea6e9a62171ee93e2945b0abe2"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "7864a44a0c9ac80fd4c1737825b6e5da"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "9479303610b755a9ae5f6d388fa25e77"}, {"hover": "", "class": "", "duration": "0.17 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "abbf8e7fcc974399518502ba90abfe6f"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "32427de7e46cca13f90d8a35168c3c85"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "d7af7627a4764e100a0ca60313d19fc8"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "2e3dfac9fdc4e1b3baff4a6841b56c15"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "3bfca6a6eb8ed8e8a27ca22f2d249b55"}, {"hover": "", "class": "", "duration": "0.2 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "89f4dc1dde7dadbc528f05c143c8655d"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "4a35d55206068038fd6c07243d43d321"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "b6eaed76eba9850baefc7ac0e991f871"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "edccf2ddec9343f408858963944e68c3"}, {"hover": "", "class": "", "duration": "0.18 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "1d6aba928e04b1d4c6efdd8052b69ba0"}, {"hover": "", "class": "", "duration": "0.33 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "a18fa9483a527b6e999dd9495f6234ee"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "29093de68abf8661bbdbfd78d973baf8"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "3c7954722b25d9e34393483be5ef1708"}, {"hover": "", "class": "", "duration": "0.21 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "86ba654cf890418f1da0eed072311d38"}, {"hover": "", "class": "", "duration": "0.19 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "f79f9f0dd45960097203ba4407bf3012"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "de647ee03de5f1004950702249505bc8"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "1b7b6d26c91c8bf334f6921cfbe7d998"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "8d376f37ad2443899fe0c0e7613e03b5"}, {"hover": "", "class": "", "duration": "0.22 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "6820aae24ca49d1593672801ec2a2ebd"}, {"hover": "", "class": "", "duration": "0.31 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "07e5ef468ebe04d938255e122a138d46"}, {"hover": "", "class": "", "duration": "0.25 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "25d621a0646ea1a73bf4ef5ddbc88755"}, {"hover": "", "class": "", "duration": "0.24 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "8aceb3f23266ca67f95b7728174002e5"}, {"hover": "", "class": "", "duration": "0.28 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "479d76b9400f0f61d449edf2258b25a7"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "cbf63f119a1d0713191cec1fb2c28d53"}, {"hover": "", "class": "", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "e5ea6f167294a5c184c20b4ebc0f212e"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "945e3b58c154e7377de8f7bfc31179dc"}, {"hover": "", "class": "", "duration": "0.26 ms", "sql": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:65", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:65", "qid": "e29c4789c2ad48bb52d5a9e99cf0672b"}, {"hover": "", "class": "", "duration": "0.85 ms", "sql": "<strong>SELECT</strong> `m`.*\n<strong>FROM</strong> `members` `m`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>AND</strong> `m`.`member_id` <strong>NOT</strong> <strong>IN</strong> (<strong>SELECT</strong> `c`.`member_id` <strong>FROM</strong> `commitments` `c` <strong>WHERE</strong> `c`.`start_date` &lt;= &#039;2025-05-28&#039; <strong>AND</strong>   ( `c`.`end_date` <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> `c`.`end_date` &gt;= &#039;2025-05-28&#039;  ))", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:993", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:75", "function": "        App\\Controllers\\Reports->getMembersWithoutActiveCommitments()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Controllers/Reports.php:993", "qid": "6afc1590339bfa012ec6983309617ef8"}, {"hover": "", "class": "", "duration": "0.76 ms", "sql": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>ORDER</strong> <strong>BY</strong> `m`.`name` <strong>ASC</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Models/CollectionSummaryModel.php:61", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:79", "function": "        App\\Models\\CollectionSummaryModel->getAllSummariesWithMemberDetails()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  8    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  9    "}], "trace-file": "APPPATH/Models/CollectionSummaryModel.php:61", "qid": "6bf4d574a7286670090317048b9a234f"}, {"hover": "", "class": "", "duration": "0.4 ms", "sql": "<strong>SELECT</strong> `payment_method`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `payments`\n<strong>GROUP</strong> <strong>BY</strong> `payment_method`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:124", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  3    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  5    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  6    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  7    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": "  8    "}], "trace-file": "APPPATH/Controllers/Reports.php:124", "qid": "268e1bab6e588f1623bc8f70cb876362"}, {"hover": "", "class": "", "duration": "0.62 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:537", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "a97f43c6a2bb17ab3bfafe43bb79ee95"}, {"hover": "", "class": "", "duration": "0.61 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:538", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "aadbcdeec8b06f9020eac5850789a639"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.49 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "fd4242908902ab31df43c9de8677f155"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "c918c590d894b36adf101b9e5520bd00"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.62 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "04a6c25f9d70ebab4880fa1e2b878f76"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.62 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "7f363b7fad6083900dfa42dbcc77868b"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.55 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "4d186100341923c4fd350245b52ad6a3"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.57 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "2f41dd78390a71454900458bae92b016"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.76 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "21de4311fd0a2d036fd11d13919eed67"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.63 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "e1600348b8d184ae8d8bf3a9e5263acc"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.54 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:549", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "789aab76d56ee96fbcc8fb93d7de48c4"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.57 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:550", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "fe3ec4f6f591d4e06ea3be7d0bf14057"}, {"hover": "", "class": "", "duration": "0.51 ms", "sql": "<strong>SELECT</strong> `p`.`payment_date`, `c`.`start_date`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `p`.`commitment_id` = `c`.`commitment_id`\n<strong>WHERE</strong> `p`.`commitment_id` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `c`.`start_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `p`.`payment_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:588", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:556", "function": "        App\\Controllers\\Reports->calculateAverageMonthsToPayment()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:588", "qid": "e437c440dbc4a77a679408a8e81d00b4"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.27 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:559", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:559", "qid": "878bacbaf4810cdc845bf4521ad763dd"}, {"hover": "", "class": "", "duration": "0.23 ms", "sql": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `collection_summary`\n<strong>WHERE</strong> `balance` &gt; 0", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1733", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "SYSTEMPATH/Model.php:676", "function": "        CodeIgniter\\Database\\BaseBuilder->countAllResults()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:560", "function": "        CodeIgniter\\Model->countAllResults()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:156", "function": "        App\\Controllers\\Reports->calculateCollectionEfficiencyMetrics()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:560", "qid": "7c57df0374d1f15f77b456a37b8d37fd"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.51 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "d6d6f8d6df89e3f9bdc756f27e9ae180"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.51 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "2174b90ad87a64c5400b344186910e9b"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.51 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "295b88b5aa96c9156f0515db5ebe3724"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.51 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "fe6f7becf094cafbead48e05b4fee499"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.41 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "0584d280aa909e2f660db16984cd7a0a"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.39 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "d0bf3723492ccd00dee22df6163accac"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.36 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "248c1b3103aa6b5e1d5ac5775ad94479"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.44 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "fb63f68efc5dd9f3fe72ed737313e4fa"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.53 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:378", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:278", "function": "        App\\Controllers\\Reports->calculateTotalCommittedForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:378", "qid": "64e36a6a7be71c6a1949a333e7e78d89"}, {"hover": "This query was called more than once.", "class": "duplicate", "duration": "0.45 ms", "sql": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`", "trace": [{"file": "SYSTEMPATH/Database/BaseBuilder.php:1649", "function": "        CodeIgniter\\Database\\BaseConnection->query()", "index": "  1    "}, {"file": "APPPATH/Controllers/Reports.php:468", "function": "        CodeIgniter\\Database\\BaseBuilder->get()", "index": "  2    "}, {"file": "APPPATH/Controllers/Reports.php:282", "function": "        App\\Controllers\\Reports->calculateTotalPaidForMonth()", "index": "  3    "}, {"file": "APPPATH/Controllers/Reports.php:163", "function": "        App\\Controllers\\Reports->generateCollectionSummaryChartData()", "index": "  4    "}, {"file": "SYSTEMPATH/CodeIgniter.php:933", "function": "        App\\Controllers\\Reports->index()", "index": "  5    "}, {"file": "SYSTEMPATH/CodeIgniter.php:507", "function": "        CodeIgniter\\CodeIgniter->runController()", "index": "  6    "}, {"file": "SYSTEMPATH/CodeIgniter.php:354", "function": "        CodeIgniter\\CodeIgniter->handleRequest()", "index": "  7    "}, {"file": "SYSTEMPATH/Boot.php:334", "function": "        CodeIgniter\\CodeIgniter->run()", "index": "  8    "}, {"file": "SYSTEMPATH/Boot.php:67", "function": "        CodeIgniter\\Boot::runCodeIgniter()", "index": "  9    "}, {"file": "FCPATH/index.php:65", "function": "        CodeIgniter\\Boot::bootWeb()", "index": " 10    "}], "trace-file": "APPPATH/Controllers/Reports.php:468", "qid": "6b24409198e7d14143b8efb33a84612e"}]}, "badgeValue": 100, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADMSURBVEhLY6A3YExLSwsA4nIycQDIDIhRWEBqamo/UNF/SjDQjF6ocZgAKPkRiFeEhoYyQ4WIBiA9QAuWAPEHqBAmgLqgHcolGQD1V4DMgHIxwbCxYD+QBqcKINseKo6eWrBioPrtQBq/BcgY5ht0cUIYbBg2AJKkRxCNWkDQgtFUNJwtABr+F6igE8olGQD114HMgHIxAVDyAhA/AlpSA8RYUwoeXAPVex5qHCbIyMgwBCkAuQJIY00huDBUz/mUlBQDqHGjgBjAwAAACexpph6oHSQAAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "Connecting to Database: \"default\"", "component": "Database", "start": **********.70531, "duration": "0.000505"}, {"name": "Query", "component": "Database", "start": **********.705833, "duration": "0.000678", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`"}, {"name": "Query", "component": "Database", "start": **********.706661, "duration": "0.000314", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.707038, "duration": "0.000586", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `members`"}, {"name": "Query", "component": "Database", "start": **********.70775, "duration": "0.000329", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;1&#039;"}, {"name": "Query", "component": "Database", "start": **********.708164, "duration": "0.000258", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;2&#039;"}, {"name": "Query", "component": "Database", "start": **********.708517, "duration": "0.000232", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;3&#039;"}, {"name": "Query", "component": "Database", "start": **********.708816, "duration": "0.000263", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.709146, "duration": "0.000215", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;5&#039;"}, {"name": "Query", "component": "Database", "start": **********.709412, "duration": "0.000307", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.709828, "duration": "0.000278", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;"}, {"name": "Query", "component": "Database", "start": **********.710214, "duration": "0.000293", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.710591, "duration": "0.000282", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.710951, "duration": "0.000309", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;10&#039;"}, {"name": "Query", "component": "Database", "start": **********.711351, "duration": "0.000272", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.711723, "duration": "0.000240", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.712051, "duration": "0.000248", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.712381, "duration": "0.000369", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.712853, "duration": "0.000277", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.713238, "duration": "0.000298", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.713656, "duration": "0.000397", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.714149, "duration": "0.000311", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.714556, "duration": "0.000295", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;"}, {"name": "Query", "component": "Database", "start": **********.714947, "duration": "0.000285", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.715318, "duration": "0.000327", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.715753, "duration": "0.000552", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.716389, "duration": "0.000357", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.716814, "duration": "0.000280", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.717197, "duration": "0.000292", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.717567, "duration": "0.000325", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.718004, "duration": "0.000303", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.718402, "duration": "0.000269", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.71876, "duration": "0.000259", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.719118, "duration": "0.000294", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.71949, "duration": "0.000304", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.719866, "duration": "0.000295", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.720245, "duration": "0.000258", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.720573, "duration": "0.000306", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.720954, "duration": "0.000313", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.721359, "duration": "0.000277", "query": "<strong>SELECT</strong> <strong>SUM</strong>(`amount`) <strong>AS</strong> `amount`\n<strong>FROM</strong> `payments`\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.721707, "duration": "0.000265", "query": "<strong>SELECT</strong> <strong>SUM</strong>(`balance`) <strong>AS</strong> `balance`\n<strong>FROM</strong> `collection_summary`\n <strong>LIMIT</strong> 1"}, {"name": "Query", "component": "Database", "start": **********.722242, "duration": "0.001682", "query": "<strong>SELECT</strong> `p`.*, `m`.`name` as `member_name`, `c`.`amount` as `commitment_amount`, `c`.`frequency` as `commitment_frequency`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `p`.`member_id`\n<strong>LEFT</strong> <strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `c`.`commitment_id` = `p`.`commitment_id`\n<strong>ORDER</strong> <strong>BY</strong> `p`.`payment_date` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.724246, "duration": "0.000638", "query": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`, `m`.`phone`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>WHERE</strong> `cs`.`balance` &gt; 0\n<strong>AND</strong> `m`.`status` = &#039;active&#039;\n<strong>ORDER</strong> <strong>BY</strong> `cs`.`balance` <strong>DESC</strong>"}, {"name": "Query", "component": "Database", "start": **********.725008, "duration": "0.000243", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;28&#039;"}, {"name": "Query", "component": "Database", "start": **********.725335, "duration": "0.000195", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;18&#039;"}, {"name": "Query", "component": "Database", "start": **********.725608, "duration": "0.000191", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;21&#039;"}, {"name": "Query", "component": "Database", "start": **********.725875, "duration": "0.000170", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;30&#039;"}, {"name": "Query", "component": "Database", "start": **********.726136, "duration": "0.000230", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;29&#039;"}, {"name": "Query", "component": "Database", "start": **********.726446, "duration": "0.000218", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;16&#039;"}, {"name": "Query", "component": "Database", "start": **********.726802, "duration": "0.000208", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;15&#039;"}, {"name": "Query", "component": "Database", "start": **********.727143, "duration": "0.000211", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;6&#039;"}, {"name": "Query", "component": "Database", "start": **********.727461, "duration": "0.000205", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;26&#039;"}, {"name": "Query", "component": "Database", "start": **********.727778, "duration": "0.000194", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;24&#039;"}, {"name": "Query", "component": "Database", "start": **********.728097, "duration": "0.000229", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;22&#039;"}, {"name": "Query", "component": "Database", "start": **********.728434, "duration": "0.000215", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;34&#039;"}, {"name": "Query", "component": "Database", "start": **********.728736, "duration": "0.000177", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;20&#039;"}, {"name": "Query", "component": "Database", "start": **********.729017, "duration": "0.000335", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;25&#039;"}, {"name": "Query", "component": "Database", "start": **********.729463, "duration": "0.000212", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;11&#039;"}, {"name": "Query", "component": "Database", "start": **********.72978, "duration": "0.000208", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;31&#039;"}, {"name": "Query", "component": "Database", "start": **********.730101, "duration": "0.000209", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;19&#039;"}, {"name": "Query", "component": "Database", "start": **********.73041, "duration": "0.000186", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;8&#039;"}, {"name": "Query", "component": "Database", "start": **********.730692, "duration": "0.000224", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;33&#039;"}, {"name": "Query", "component": "Database", "start": **********.731013, "duration": "0.000222", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;32&#039;"}, {"name": "Query", "component": "Database", "start": **********.731338, "duration": "0.000216", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;4&#039;"}, {"name": "Query", "component": "Database", "start": **********.731653, "duration": "0.000219", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;7&#039;"}, {"name": "Query", "component": "Database", "start": **********.73197, "duration": "0.000314", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;14&#039;"}, {"name": "Query", "component": "Database", "start": **********.732419, "duration": "0.000252", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;27&#039;"}, {"name": "Query", "component": "Database", "start": **********.732794, "duration": "0.000242", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;12&#039;"}, {"name": "Query", "component": "Database", "start": **********.733163, "duration": "0.000276", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;9&#039;"}, {"name": "Query", "component": "Database", "start": **********.733554, "duration": "0.000274", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;35&#039;"}, {"name": "Query", "component": "Database", "start": **********.733948, "duration": "0.000271", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;23&#039;"}, {"name": "Query", "component": "Database", "start": **********.734329, "duration": "0.000256", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;17&#039;"}, {"name": "Query", "component": "Database", "start": **********.734698, "duration": "0.000261", "query": "<strong>SELECT</strong> `phone`, `whatsapp_number`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `member_id` = &#039;13&#039;"}, {"name": "Query", "component": "Database", "start": **********.735391, "duration": "0.000845", "query": "<strong>SELECT</strong> `m`.*\n<strong>FROM</strong> `members` `m`\n<strong>WHERE</strong> `m`.`status` = &#039;active&#039;\n<strong>AND</strong> `m`.`member_id` <strong>NOT</strong> <strong>IN</strong> (<strong>SELECT</strong> `c`.`member_id` <strong>FROM</strong> `commitments` `c` <strong>WHERE</strong> `c`.`start_date` &lt;= &#039;2025-05-28&#039; <strong>AND</strong>   ( `c`.`end_date` <strong>IS</strong> <strong>NULL</strong> <strong>OR</strong> `c`.`end_date` &gt;= &#039;2025-05-28&#039;  ))"}, {"name": "Query", "component": "Database", "start": **********.736455, "duration": "0.000758", "query": "<strong>SELECT</strong> `cs`.*, `m`.`name` as `member_name`, `m`.`status`\n<strong>FROM</strong> `collection_summary` `cs`\n<strong>JOIN</strong> `members` `m` <strong>ON</strong> `m`.`member_id` = `cs`.`member_id`\n<strong>ORDER</strong> <strong>BY</strong> `m`.`name` <strong>ASC</strong>"}, {"name": "Query", "component": "Database", "start": **********.737396, "duration": "0.000399", "query": "<strong>SELECT</strong> `payment_method`, <strong>COUNT</strong>(*) as count\n<strong>FROM</strong> `payments`\n<strong>GROUP</strong> <strong>BY</strong> `payment_method`"}, {"name": "Query", "component": "Database", "start": **********.737893, "duration": "0.000619", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.739054, "duration": "0.000608", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.739879, "duration": "0.000493", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.740883, "duration": "0.000555", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.741643, "duration": "0.000619", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.74277, "duration": "0.000622", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.743593, "duration": "0.000550", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.744399, "duration": "0.000569", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.745191, "duration": "0.000756", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.746557, "duration": "0.000629", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.747378, "duration": "0.000543", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.748327, "duration": "0.000574", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.749298, "duration": "0.000506", "query": "<strong>SELECT</strong> `p`.`payment_date`, `c`.`start_date`\n<strong>FROM</strong> `payments` `p`\n<strong>JOIN</strong> `commitments` `c` <strong>ON</strong> `p`.`commitment_id` = `c`.`commitment_id`\n<strong>WHERE</strong> `p`.`commitment_id` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `c`.`start_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>\n<strong>AND</strong> `p`.`payment_date` <strong>IS</strong> <strong>NOT</strong> <strong>NULL</strong>"}, {"name": "Query", "component": "Database", "start": **********.750088, "duration": "0.000274", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `members`\n<strong>WHERE</strong> `status` = &#039;active&#039;"}, {"name": "Query", "component": "Database", "start": **********.750446, "duration": "0.000235", "query": "<strong>SELECT</strong> <strong>COUNT</strong>(*) <strong>AS</strong> `numrows`\n<strong>FROM</strong> `collection_summary`\n<strong>WHERE</strong> `balance` &gt; 0"}, {"name": "Query", "component": "Database", "start": **********.750778, "duration": "0.000513", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.751509, "duration": "0.000508", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.752135, "duration": "0.000506", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.752866, "duration": "0.000512", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.753498, "duration": "0.000408", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.754139, "duration": "0.000392", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.754623, "duration": "0.000358", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.755201, "duration": "0.000439", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}, {"name": "Query", "component": "Database", "start": **********.755732, "duration": "0.000530", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `commitments`"}, {"name": "Query", "component": "Database", "start": **********.756492, "duration": "0.000450", "query": "<strong>SELECT</strong> *\n<strong>FROM</strong> `payments`"}]}, {"title": "Logs", "titleSafe": "logs", "titleDetails": "", "display": {"logs": []}, "badgeValue": null, "isEmpty": true, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAACYSURBVEhLYxgFJIHU1FSjtLS0i0D8AYj7gEKMEBkqAaAFF4D4ERCvAFrwH4gDoFIMKSkpFkB+OTEYqgUTACXfA/GqjIwMQyD9H2hRHlQKJFcBEiMGQ7VgAqCBvUgK32dmZspCpagGGNPT0/1BLqeF4bQHQJePpiIwhmrBBEADR1MRfgB0+WgqAmOoFkwANHA0FY0CUgEDAwCQ0PUpNB3kqwAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Views", "titleSafe": "views", "titleDetails": "", "display": [], "badgeValue": 2, "isEmpty": false, "hasTabContent": false, "hasLabel": true, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAADeSURBVEhL7ZSxDcIwEEWNYA0YgGmgyAaJLTcUaaBzQQEVjMEabBQxAdw53zTHiThEovGTfnE/9rsoRUxhKLOmaa6Uh7X2+UvguLCzVxN1XW9x4EYHzik033Hp3X0LO+DaQG8MDQcuq6qao4qkHuMgQggLvkPLjqh00ZgFDBacMJYFkuwFlH1mshdkZ5JPJERA9JpI6xNCBESvibQ+IURA9JpI6xNCBESvibQ+IURA9DTsuHTOrVFFxixgB/eUFlU8uKJ0eDBFOu/9EvoeKnlJS2/08Tc8NOwQ8sIfMeYFjqKDjdU2sp4AAAAASUVORK5CYII=", "hasTimelineData": true, "timelineData": [{"name": "View: layout/main.php", "component": "Views", "start": **********.785219, "duration": 0.00028705596923828125}, {"name": "View: reports/dashboard.php", "component": "Views", "start": **********.784489, "duration": 0.0011050701141357422}]}, {"title": "Files", "titleSafe": "files", "titleDetails": "( 162 )", "display": {"coreFiles": [{"path": "SYSTEMPATH/API/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/Autoloader/Autoloader.php", "name": "Autoloader.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocator.php", "name": "FileLocator.php"}, {"path": "SYSTEMPATH/Autoloader/FileLocatorInterface.php", "name": "FileLocatorInterface.php"}, {"path": "SYSTEMPATH/BaseModel.php", "name": "BaseModel.php"}, {"path": "SYSTEMPATH/Boot.php", "name": "Boot.php"}, {"path": "SYSTEMPATH/Cache/CacheFactory.php", "name": "CacheFactory.php"}, {"path": "SYSTEMPATH/Cache/CacheInterface.php", "name": "CacheInterface.php"}, {"path": "SYSTEMPATH/Cache/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Cache/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Cache/ResponseCache.php", "name": "ResponseCache.php"}, {"path": "SYSTEMPATH/CodeIgniter.php", "name": "CodeIgniter.php"}, {"path": "SYSTEMPATH/Common.php", "name": "Common.php"}, {"path": "SYSTEMPATH/Config/AutoloadConfig.php", "name": "AutoloadConfig.php"}, {"path": "SYSTEMPATH/Config/BaseConfig.php", "name": "BaseConfig.php"}, {"path": "SYSTEMPATH/Config/BaseService.php", "name": "BaseService.php"}, {"path": "SYSTEMPATH/Config/DotEnv.php", "name": "DotEnv.php"}, {"path": "SYSTEMPATH/Config/Factories.php", "name": "Factories.php"}, {"path": "SYSTEMPATH/Config/Factory.php", "name": "Factory.php"}, {"path": "SYSTEMPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "SYSTEMPATH/Config/Services.php", "name": "Services.php"}, {"path": "SYSTEMPATH/Config/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/Controller.php", "name": "Controller.php"}, {"path": "SYSTEMPATH/Cookie/CloneableCookieInterface.php", "name": "CloneableCookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/Cookie.php", "name": "Cookie.php"}, {"path": "SYSTEMPATH/Cookie/CookieInterface.php", "name": "CookieInterface.php"}, {"path": "SYSTEMPATH/Cookie/CookieStore.php", "name": "CookieStore.php"}, {"path": "SYSTEMPATH/Database/BaseBuilder.php", "name": "BaseBuilder.php"}, {"path": "SYSTEMPATH/Database/BaseConnection.php", "name": "BaseConnection.php"}, {"path": "SYSTEMPATH/Database/BaseResult.php", "name": "BaseResult.php"}, {"path": "SYSTEMPATH/Database/Config.php", "name": "Config.php"}, {"path": "SYSTEMPATH/Database/ConnectionInterface.php", "name": "ConnectionInterface.php"}, {"path": "SYSTEMPATH/Database/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Builder.php", "name": "Builder.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Connection.php", "name": "Connection.php"}, {"path": "SYSTEMPATH/Database/MySQLi/Result.php", "name": "Result.php"}, {"path": "SYSTEMPATH/Database/Query.php", "name": "Query.php"}, {"path": "SYSTEMPATH/Database/QueryInterface.php", "name": "QueryInterface.php"}, {"path": "SYSTEMPATH/Database/ResultInterface.php", "name": "ResultInterface.php"}, {"path": "SYSTEMPATH/Debug/Exceptions.php", "name": "Exceptions.php"}, {"path": "SYSTEMPATH/Debug/Timer.php", "name": "Timer.php"}, {"path": "SYSTEMPATH/Debug/Toolbar.php", "name": "Toolbar.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/BaseCollector.php", "name": "BaseCollector.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Database.php", "name": "Database.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Files.php", "name": "Files.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Logs.php", "name": "Logs.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Routes.php", "name": "Routes.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Timers.php", "name": "Timers.php"}, {"path": "SYSTEMPATH/Debug/Toolbar/Collectors/Views.php", "name": "Views.php"}, {"path": "SYSTEMPATH/Events/Events.php", "name": "Events.php"}, {"path": "SYSTEMPATH/Filters/DebugToolbar.php", "name": "DebugToolbar.php"}, {"path": "SYSTEMPATH/Filters/FilterInterface.php", "name": "FilterInterface.php"}, {"path": "SYSTEMPATH/Filters/Filters.php", "name": "Filters.php"}, {"path": "SYSTEMPATH/Filters/ForceHTTPS.php", "name": "ForceHTTPS.php"}, {"path": "SYSTEMPATH/Filters/PageCache.php", "name": "PageCache.php"}, {"path": "SYSTEMPATH/Filters/PerformanceMetrics.php", "name": "PerformanceMetrics.php"}, {"path": "SYSTEMPATH/HTTP/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "SYSTEMPATH/HTTP/Header.php", "name": "Header.php"}, {"path": "SYSTEMPATH/HTTP/IncomingRequest.php", "name": "IncomingRequest.php"}, {"path": "SYSTEMPATH/HTTP/Message.php", "name": "Message.php"}, {"path": "SYSTEMPATH/HTTP/MessageInterface.php", "name": "MessageInterface.php"}, {"path": "SYSTEMPATH/HTTP/MessageTrait.php", "name": "MessageTrait.php"}, {"path": "SYSTEMPATH/HTTP/Method.php", "name": "Method.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequest.php", "name": "OutgoingRequest.php"}, {"path": "SYSTEMPATH/HTTP/OutgoingRequestInterface.php", "name": "OutgoingRequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/Request.php", "name": "Request.php"}, {"path": "SYSTEMPATH/HTTP/RequestInterface.php", "name": "RequestInterface.php"}, {"path": "SYSTEMPATH/HTTP/RequestTrait.php", "name": "RequestTrait.php"}, {"path": "SYSTEMPATH/HTTP/Response.php", "name": "Response.php"}, {"path": "SYSTEMPATH/HTTP/ResponseInterface.php", "name": "ResponseInterface.php"}, {"path": "SYSTEMPATH/HTTP/ResponseTrait.php", "name": "ResponseTrait.php"}, {"path": "SYSTEMPATH/HTTP/SiteURI.php", "name": "SiteURI.php"}, {"path": "SYSTEMPATH/HTTP/SiteURIFactory.php", "name": "SiteURIFactory.php"}, {"path": "SYSTEMPATH/HTTP/URI.php", "name": "URI.php"}, {"path": "SYSTEMPATH/HTTP/UserAgent.php", "name": "UserAgent.php"}, {"path": "SYSTEMPATH/Helpers/array_helper.php", "name": "array_helper.php"}, {"path": "SYSTEMPATH/Helpers/filesystem_helper.php", "name": "filesystem_helper.php"}, {"path": "SYSTEMPATH/Helpers/kint_helper.php", "name": "kint_helper.php"}, {"path": "SYSTEMPATH/Helpers/url_helper.php", "name": "url_helper.php"}, {"path": "SYSTEMPATH/I18n/Time.php", "name": "Time.php"}, {"path": "SYSTEMPATH/I18n/TimeTrait.php", "name": "TimeTrait.php"}, {"path": "SYSTEMPATH/Log/Logger.php", "name": "Logger.php"}, {"path": "SYSTEMPATH/Model.php", "name": "Model.php"}, {"path": "SYSTEMPATH/Modules/Modules.php", "name": "Modules.php"}, {"path": "SYSTEMPATH/Router/RouteCollection.php", "name": "RouteCollection.php"}, {"path": "SYSTEMPATH/Router/RouteCollectionInterface.php", "name": "RouteCollectionInterface.php"}, {"path": "SYSTEMPATH/Router/Router.php", "name": "Router.php"}, {"path": "SYSTEMPATH/Router/RouterInterface.php", "name": "RouterInterface.php"}, {"path": "SYSTEMPATH/Session/Handlers/BaseHandler.php", "name": "BaseHandler.php"}, {"path": "SYSTEMPATH/Session/Handlers/FileHandler.php", "name": "FileHandler.php"}, {"path": "SYSTEMPATH/Session/Session.php", "name": "Session.php"}, {"path": "SYSTEMPATH/Session/SessionInterface.php", "name": "SessionInterface.php"}, {"path": "SYSTEMPATH/Superglobals.php", "name": "Superglobals.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/FacadeInterface.php", "name": "FacadeInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Kint.php", "name": "Kint.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AbstractRenderer.php", "name": "AbstractRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/AssetRendererTrait.php", "name": "AssetRendererTrait.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/CliRenderer.php", "name": "CliRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/ConstructableRendererInterface.php", "name": "ConstructableRendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/RichRenderer.php", "name": "RichRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Renderer/TextRenderer.php", "name": "TextRenderer.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/Utils.php", "name": "Utils.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init.php", "name": "init.php"}, {"path": "SYSTEMPATH/ThirdParty/Kint/init_helpers.php", "name": "init_helpers.php"}, {"path": "SYSTEMPATH/Traits/ConditionalTrait.php", "name": "ConditionalTrait.php"}, {"path": "SYSTEMPATH/Validation/FormatRules.php", "name": "FormatRules.php"}, {"path": "SYSTEMPATH/View/RendererInterface.php", "name": "RendererInterface.php"}, {"path": "SYSTEMPATH/View/View.php", "name": "View.php"}, {"path": "SYSTEMPATH/View/ViewDecoratorTrait.php", "name": "ViewDecoratorTrait.php"}], "userFiles": [{"path": "APPPATH/Common.php", "name": "Common.php"}, {"path": "APPPATH/Config/App.php", "name": "App.php"}, {"path": "APPPATH/Config/Autoload.php", "name": "Autoload.php"}, {"path": "APPPATH/Config/Boot/development.php", "name": "development.php"}, {"path": "APPPATH/Config/Cache.php", "name": "Cache.php"}, {"path": "APPPATH/Config/Constants.php", "name": "Constants.php"}, {"path": "APPPATH/Config/ContentSecurityPolicy.php", "name": "ContentSecurityPolicy.php"}, {"path": "APPPATH/Config/Cookie.php", "name": "Cookie.php"}, {"path": "APPPATH/Config/Database.php", "name": "Database.php"}, {"path": "APPPATH/Config/Events.php", "name": "Events.php"}, {"path": "APPPATH/Config/Exceptions.php", "name": "Exceptions.php"}, {"path": "APPPATH/Config/Feature.php", "name": "Feature.php"}, {"path": "APPPATH/Config/Filters.php", "name": "Filters.php"}, {"path": "APPPATH/Config/Kint.php", "name": "Kint.php"}, {"path": "APPPATH/Config/Logger.php", "name": "Logger.php"}, {"path": "APPPATH/Config/Modules.php", "name": "Modules.php"}, {"path": "APPPATH/Config/Optimize.php", "name": "Optimize.php"}, {"path": "APPPATH/Config/Paths.php", "name": "Paths.php"}, {"path": "APPPATH/Config/Performance.php", "name": "Performance.php"}, {"path": "APPPATH/Config/Routes.php", "name": "Routes.php"}, {"path": "APPPATH/Config/Routing.php", "name": "Routing.php"}, {"path": "APPPATH/Config/Services.php", "name": "Services.php"}, {"path": "APPPATH/Config/Session.php", "name": "Session.php"}, {"path": "APPPATH/Config/Toolbar.php", "name": "Toolbar.php"}, {"path": "APPPATH/Config/UserAgents.php", "name": "UserAgents.php"}, {"path": "APPPATH/Config/View.php", "name": "View.php"}, {"path": "APPPATH/Controllers/BaseController.php", "name": "BaseController.php"}, {"path": "APPPATH/Controllers/Reports.php", "name": "Reports.php"}, {"path": "APPPATH/Filters/AuthFilter.php", "name": "AuthFilter.php"}, {"path": "APPPATH/Filters/PerformanceFilter.php", "name": "PerformanceFilter.php"}, {"path": "APPPATH/Helpers/currency_helper.php", "name": "currency_helper.php"}, {"path": "APPPATH/Models/CollectionSummaryModel.php", "name": "CollectionSummaryModel.php"}, {"path": "APPPATH/Models/CommitmentModel.php", "name": "CommitmentModel.php"}, {"path": "APPPATH/Models/MemberModel.php", "name": "MemberModel.php"}, {"path": "APPPATH/Models/PaymentModel.php", "name": "PaymentModel.php"}, {"path": "APPPATH/Views/layout/main.php", "name": "main.php"}, {"path": "APPPATH/Views/reports/dashboard.php", "name": "dashboard.php"}, {"path": "FCPATH/index.php", "name": "index.php"}, {"path": "FCPATH/vendor/autoload.php", "name": "autoload.php"}, {"path": "FCPATH/vendor/composer/ClassLoader.php", "name": "ClassLoader.php"}, {"path": "FCPATH/vendor/composer/InstalledVersions.php", "name": "InstalledVersions.php"}, {"path": "FCPATH/vendor/composer/autoload_real.php", "name": "autoload_real.php"}, {"path": "FCPATH/vendor/composer/autoload_static.php", "name": "autoload_static.php"}, {"path": "FCPATH/vendor/composer/installed.php", "name": "installed.php"}, {"path": "FCPATH/vendor/composer/platform_check.php", "name": "platform_check.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/Escaper.php", "name": "Escaper.php"}, {"path": "FCPATH/vendor/laminas/laminas-escaper/src/EscaperInterface.php", "name": "EscaperInterface.php"}, {"path": "FCPATH/vendor/psr/log/src/LogLevel.php", "name": "LogLevel.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerAwareTrait.php", "name": "LoggerAwareTrait.php"}, {"path": "FCPATH/vendor/psr/log/src/LoggerInterface.php", "name": "LoggerInterface.php"}]}, "badgeValue": 162, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAGBSURBVEhL7ZQ9S8NQGIVTBQUncfMfCO4uLgoKbuKQOWg+OkXERRE1IAXrIHbVDrqIDuLiJgj+gro7S3dnpfq88b1FMTE3VZx64HBzzvvZWxKnj15QCcPwCD5HUfSWR+JtzgmtsUcQBEva5IIm9SwSu+95CAWbUuy67qBa32ByZEDpIaZYZSZMjjQuPcQUq8yEyYEb8FSerYeQVGbAFzJkX1PyQWLhgCz0BxTCekC1Wp0hsa6yokzhed4oje6Iz6rlJEkyIKfUEFtITVtQdAibn5rMyaYsMS+a5wTv8qeXMhcU16QZbKgl3hbs+L4/pnpdc87MElZgq10p5DxGdq8I7xrvUWUKvG3NbSK7ubngYzdJwSsF7TiOh9VOgfcEz1UayNe3JUPM1RWC5GXYgTfc75B4NBmXJnAtTfpABX0iPvEd9ezALwkplCFXcr9styiNOKc1RRZpaPM9tcqBwlWzGY1qPL9wjqRBgF5BH6j8HWh2S7MHlX8PrmbK+k/8PzjOOzx1D3i1pKTTAAAAAElFTkSuQmCC", "hasTimelineData": false, "timelineData": []}, {"title": "Routes", "titleSafe": "routes", "titleDetails": "", "display": {"matchedRoute": [{"directory": "", "controller": "\\App\\Controllers\\Reports", "method": "index", "paramCount": 0, "truePCount": 0, "params": []}], "routes": [{"method": "GET", "route": "/", "handler": "\\App\\Controllers\\Home::index"}, {"method": "GET", "route": "login", "handler": "\\App\\Controllers\\Auth::index"}, {"method": "GET", "route": "auth/logout", "handler": "\\App\\Controllers\\Auth::logout"}, {"method": "GET", "route": "auth/change-password", "handler": "\\App\\Controllers\\Auth::changePassword"}, {"method": "GET", "route": "members", "handler": "\\App\\Controllers\\Members::index"}, {"method": "GET", "route": "members/new", "handler": "\\App\\Controllers\\Members::new"}, {"method": "GET", "route": "members/show/([0-9]+)", "handler": "\\App\\Controllers\\Members::show/$1"}, {"method": "GET", "route": "members/edit/([0-9]+)", "handler": "\\App\\Controllers\\Members::edit/$1"}, {"method": "GET", "route": "members/delete/([0-9]+)", "handler": "\\App\\Controllers\\Members::delete/$1"}, {"method": "GET", "route": "commitments", "handler": "\\App\\Controllers\\Commitments::index"}, {"method": "GET", "route": "commitments/new", "handler": "\\App\\Controllers\\Commitments::new"}, {"method": "GET", "route": "commitments/create/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::new/$1"}, {"method": "GET", "route": "commitments/show/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::show/$1"}, {"method": "GET", "route": "commitments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::edit/$1"}, {"method": "GET", "route": "commitments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::delete/$1"}, {"method": "GET", "route": "commitments/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::memberCommitments/$1"}, {"method": "GET", "route": "payments", "handler": "\\App\\Controllers\\Payments::index"}, {"method": "GET", "route": "payments/new", "handler": "\\App\\Controllers\\Payments::new"}, {"method": "GET", "route": "payments/show/([0-9]+)", "handler": "\\App\\Controllers\\Payments::show/$1"}, {"method": "GET", "route": "payments/edit/([0-9]+)", "handler": "\\App\\Controllers\\Payments::edit/$1"}, {"method": "GET", "route": "payments/delete/([0-9]+)", "handler": "\\App\\Controllers\\Payments::delete/$1"}, {"method": "GET", "route": "payments/member-payments/([0-9]+)", "handler": "\\App\\Controllers\\Payments::memberPayments/$1"}, {"method": "GET", "route": "payments/generate-receipt/([0-9]+)", "handler": "\\App\\Controllers\\Payments::generateReceipt/$1"}, {"method": "GET", "route": "reports", "handler": "\\App\\Controllers\\Reports::index"}, {"method": "GET", "route": "reports/collection-summary", "handler": "\\App\\Controllers\\Reports::collectionSummary"}, {"method": "GET", "route": "reports/outstanding-balances", "handler": "\\App\\Controllers\\Reports::outstandingBalances"}, {"method": "GET", "route": "reports/payment-history", "handler": "\\App\\Controllers\\Reports::paymentHistory"}, {"method": "GET", "route": "reports/commitment-report", "handler": "\\App\\Controllers\\Reports::commitmentReport"}, {"method": "GET", "route": "reports/statistics", "handler": "\\App\\Controllers\\Reports::statistics"}, {"method": "GET", "route": "reports/recalculate-all-summaries", "handler": "\\App\\Controllers\\Reports::recalculateAllSummaries"}, {"method": "GET", "route": "reports/members-without-commitments", "handler": "\\App\\Controllers\\Reports::membersWithoutCommitments"}, {"method": "GET", "route": "api/member-commitments/([0-9]+)", "handler": "\\App\\Controllers\\Api::getMemberCommitments/$1"}, {"method": "GET", "route": "api/payment-details/([0-9]+)/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1/$2"}, {"method": "GET", "route": "api/payment-details/([0-9]+)", "handler": "\\App\\Controllers\\Api::getPaymentDetails/$1"}, {"method": "POST", "route": "auth/login", "handler": "\\App\\Controllers\\Auth::login"}, {"method": "POST", "route": "auth/update-password", "handler": "\\App\\Controllers\\Auth::updatePassword"}, {"method": "POST", "route": "members/create", "handler": "\\App\\Controllers\\Members::create"}, {"method": "POST", "route": "members/update/([0-9]+)", "handler": "\\App\\Controllers\\Members::update/$1"}, {"method": "POST", "route": "commitments/create", "handler": "\\App\\Controllers\\Commitments::create"}, {"method": "POST", "route": "commitments/update/([0-9]+)", "handler": "\\App\\Controllers\\Commitments::update/$1"}, {"method": "POST", "route": "payments/create", "handler": "\\App\\Controllers\\Payments::create"}, {"method": "POST", "route": "payments/update/([0-9]+)", "handler": "\\App\\Controllers\\Payments::update/$1"}]}, "badgeValue": 35, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAFDSURBVEhL7ZRNSsNQFIUjVXSiOFEcuQIHDpzpxC0IGYeE/BEInbWlCHEDLsSiuANdhKDjgm6ggtSJ+l25ldrmmTwIgtgDh/t37r1J+16cX0dRFMtpmu5pWAkrvYjjOB7AETzStBFW+inxu3KUJMmhludQpoflS1zXban4LYqiO224h6VLTHr8Z+z8EpIHFF9gG78nDVmW7UgTHKjsCyY98QP+pcq+g8Ku2s8G8X3f3/I8b038WZTp+bO38zxfFd+I6YY6sNUvFlSDk9CRhiAI1jX1I9Cfw7GG1UB8LAuwbU0ZwQnbRDeEN5qqBxZMLtE1ti9LtbREnMIuOXnyIf5rGIb7Wq8HmlZgwYBH7ORTcKH5E4mpjeGt9fBZcHE2GCQ3Vt7oTNPNg+FXLHnSsHkw/FR+Gg2bB8Ptzrst/v6C/wrH+QB+duli6MYJdQAAAABJRU5ErkJggg==", "hasTimelineData": false, "timelineData": []}, {"title": "Events", "titleSafe": "events", "titleDetails": "", "display": {"events": {"pre_system": {"event": "pre_system", "duration": "0.39", "count": 1}, "DBQuery": {"event": "DB<PERSON>uery", "duration": "1.41", "count": 142}}}, "badgeValue": 143, "isEmpty": false, "hasTabContent": true, "hasLabel": false, "icon": "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABgAAAAYCAYAAADgdz34AAAAAXNSR0IArs4c6QAAAARnQU1BAACxjwv8YQUAAAAJcEhZcwAADsMAAA7DAcdvqGQAAAEASURBVEhL7ZXNDcIwDIVTsRBH1uDQDdquUA6IM1xgCA6MwJUN2hk6AQzAz0vl0ETUxC5VT3zSU5w81/mRMGZysixbFEVR0jSKNt8geQU9aRpFmp/keX6AbjZ5oB74vsaN5lSzA4tLSjpBFxsjeSuRy4d2mDdQTWU7YLbXTNN05mKyovj5KL6B7q3hoy3KwdZxBlT+Ipz+jPHrBqOIynZgcZonoukb/0ckiTHqNvDXtXEAaygRbaB9FvUTjRUHsIYS0QaSp+Dw6wT4hiTmYHOcYZsdLQ2CbXa4ftuuYR4x9vYZgdb4vsFYUdmABMYeukK9/SUme3KMFQ77+Yfzh8eYF8+orDuDWU5LAAAAAElFTkSuQmCC", "hasTimelineData": true, "timelineData": [{"name": "Event: pre_system", "component": "Events", "start": **********.702606, "duration": 0.00038504600524902344}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.706514, "duration": 1.71661376953125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.706977, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.707626, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.708081, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.708425, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.708751, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.709081, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.709362, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.709721, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.710109, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.710509, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.710875, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.711262, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.711625, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.711965, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.712301, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.712753, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.713133, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.713538, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.714065, "duration": 1.2874603271484375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.714463, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.714853, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.715235, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.715648, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.716308, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.716748, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.717096, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.717491, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.717894, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.718309, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.718672, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.719021, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.719413, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.719795, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.720163, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.720504, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.72088, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.721268, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.721638, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.721973, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.723926, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.724886, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.725252, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.725531, "duration": 8.821487426757812e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.725801, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.726046, "duration": 1.0013580322265625e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.726368, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.726668, "duration": 1.811981201171875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.727013, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.727356, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.727668, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.727975, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.728329, "duration": 1.3113021850585938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.728651, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.728914, "duration": 1.2874603271484375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729354, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729677, "duration": 1.3113021850585938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.729989, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.730312, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.730597, "duration": 1.2874603271484375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.730917, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731237, "duration": 1.3113021850585938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731556, "duration": 1.2159347534179688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.731873, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.732287, "duration": 1.811981201171875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.732673, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.733039, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.733441, "duration": 1.4781951904296875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.73383, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.734221, "duration": 1.3113021850585938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.734587, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.734961, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.736239, "duration": 1.6927719116210938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.737215, "duration": 2.7894973754882812e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.737798, "duration": 1.6927719116210938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.738514, "duration": 2.002716064453125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.739665, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.740374, "duration": 1.5974044799804688e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.741439, "duration": 1.4781951904296875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.742264, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.743394, "duration": 1.4066696166992188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.744145, "duration": 1.3113021850585938e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.74497, "duration": 1.5020370483398438e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.745956, "duration": 6.008148193359375e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.747189, "duration": 3.3855438232421875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.747923, "duration": 2.1219253540039062e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.748903, "duration": 1.7881393432617188e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.749806, "duration": 2.5033950805664062e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.750363, "duration": 9.775161743164062e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.750682, "duration": 7.867813110351562e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.751292, "duration": 9.059906005859375e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.752018, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.752643, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.753379, "duration": 1.0967254638671875e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.753907, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.754532, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.754982, "duration": 8.106231689453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.755641, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.756263, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.756943, "duration": 6.9141387939453125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.757513, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.758232, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.758789, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.759443, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.759984, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.760708, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.761228, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.761895, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.762427, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.763055, "duration": 1.1920928955078125e-05}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.763653, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.764385, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.764972, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.765727, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.76628, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.76703, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.767642, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.768374, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.769162, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.769955, "duration": 2.1457672119140625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.770675, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.771387, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.772096, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.772804, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.773896, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.774656, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.775312, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.776442, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.777569, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.778489, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.778867, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.779256, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.77966, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.780022, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.780383, "duration": 3.814697265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.78197, "duration": 4.0531158447265625e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.782344, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.782708, "duration": 3.0994415283203125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.78307, "duration": 2.86102294921875e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.783427, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.783731, "duration": 1.9073486328125e-06}, {"name": "Event: DB<PERSON>uery", "component": "Events", "start": **********.78405, "duration": 3.0994415283203125e-06}]}], "vars": {"varData": {"View Data": {"title": "Dashboard", "totalMembers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 35</dt></dl></div>", "activeMembers": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>integer</var> 35</dt></dl></div>", "totalCommitments": "<div class=\"kint-rich\"><dl><dt><dfn>$value</dfn> <var>double</var> 126800</dt></dl></div>", "totalPayments": "20190.00", "totalOutstanding": "106610.00", "recentPayments": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>payment_id</th><th>member_id</th><th>commitment_id</th><th>amount</th><th>payment_date</th><th>receipt_number</th><th>receipt_book_number</th><th>payment_method</th><th>notes</th><th>payment_periods</th><th>created_at</th><th>updated_at</th><th>member_name</th><th>commitment_amount</th><th>commitment_frequency</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">2</td><td title=\"string (1)\">1</td><td title=\"string (1)\">7</td><td title=\"string (6)\">300.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050002</td><td title=\"string (1)\">1</td><td title=\"string (13)\">bank_transfer</td><td title=\"string (55)\">Payment for: October 2024, November 2024, December 2024</td><td title=\"string (31)\">[\"2024-10\",\"2024-11\",\"2024-12\"]</td><td title=\"string (19)\">2025-05-24 13:25:21</td><td title=\"string (19)\">2025-05-24 13:25:21</td><td title=\"string (14)\">Abdu Raheem KK</td><td title=\"string (6)\">100.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>1</th><td title=\"string (2)\">58</td><td title=\"string (2)\">15</td><td title=\"string (2)\">29</td><td title=\"string (7)\">4730.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050005</td><td title=\"string (1)\">4</td><td title=\"string (5)\">check</td><td title=\"string (155)\">Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, JuUTF-8</td><td title=\"string (111)\">[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"202UTF-8</td><td title=\"string (19)\">2025-05-24 16:59:14</td><td title=\"string (19)\">2025-05-24 16:59:14</td><td title=\"string (12)\">Adnan Al-Ali</td><td title=\"string (6)\">430.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>2</th><td title=\"string (1)\">1</td><td title=\"string (1)\">3</td><td title=\"string (1)\">4</td><td title=\"string (6)\">100.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050001</td><td title=\"string (1)\">1</td><td title=\"string (4)\">cash</td><td title=\"string (0)\"></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 12:35:13</td><td title=\"string (19)\">2025-05-24 12:35:13</td><td title=\"string (5)\">Murad</td><td title=\"string (6)\">100.00</td><td title=\"string (8)\">one-time</td></tr><tr><th>3</th><td title=\"string (1)\">4</td><td title=\"string (1)\">4</td><td title=\"string (1)\">6</td><td title=\"string (6)\">600.00</td><td title=\"string (10)\">2025-05-24</td><td title=\"string (14)\">RCPT2025050004</td><td title=\"string (1)\">3</td><td title=\"string (5)\">other</td><td title=\"string (52)\">Payment for: January 2025, February 2025, March 2025</td><td title=\"string (31)\">[\"2025-01\",\"2025-02\",\"2025-03\"]</td><td title=\"string (19)\">2025-05-24 14:13:50</td><td title=\"string (19)\">2025-05-24 14:13:50</td><td title=\"string (5)\">Riyas</td><td title=\"string (6)\">200.00</td><td title=\"string (7)\">monthly</td></tr><tr><th>4</th><td title=\"string (2)\">18</td><td title=\"string (2)\">14</td><td title=\"string (2)\">26</td><td title=\"string (5)\">70.00</td><td title=\"string (10)\">2025-05-19</td><td title=\"string (4)\">8961</td><td title=\"string (4)\">BK02</td><td title=\"string (4)\">cash</td><td title=\"null\"><var>null</var></td><td title=\"string (7)\">2025-03</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (15)\">Ibrahim Al-Saud</td><td title=\"string (5)\">70.00</td><td title=\"string (7)\">monthly</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[0]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[0]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"300.00\"<div class=\"access-path\">$value[0]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[0]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050002\"<div class=\"access-path\">$value[0]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (13) \"bank_transfer\"<div class=\"access-path\">$value[0]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (55) \"Payment for: October 2024, November 2024, December 2024\"<div class=\"access-path\">$value[0]['notes']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (31) \"[\"2024-10\",\"2024-11\",\"2024-12\"]\"<div class=\"access-path\">$value[0]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2024-10\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2024-11\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2024-12\"<div class=\"access-path\">json_decode($value[0]['payment_periods'], true)[2]</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 13:25:21\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 13:25:21\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Abdu Raheem KK\"<div class=\"access-path\">$value[0]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[0]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[0]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (2) \"58\"<div class=\"access-path\">$value[1]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"15\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[1]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (7) \"4730.00\"<div class=\"access-path\">$value[1]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[1]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050005\"<div class=\"access-path\">$value[1]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[1]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (5) \"check\"<div class=\"access-path\">$value[1]['payment_method']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>notes</dfn> =&gt; <var>string</var> (155) \"Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, Jul...<div class=\"access-path\">$value[1]['notes']</div></dt><dd><pre>Payment for: February 2025, March 2025, April 2025, May 2025, June 2025, July 2025, August 2025, September 2025, October 2025, November 2025, December 2025\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (111) \"[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"2025...<div class=\"access-path\">$value[1]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (11)</li><li>Contents</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2025-02\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2025-04\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[2]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>3</dfn> =&gt; <var>string</var> (7) \"2025-05\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[3]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>4</dfn> =&gt; <var>string</var> (7) \"2025-06\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[4]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>5</dfn> =&gt; <var>string</var> (7) \"2025-07\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[5]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>6</dfn> =&gt; <var>string</var> (7) \"2025-08\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[6]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>7</dfn> =&gt; <var>string</var> (7) \"2025-09\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[7]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>8</dfn> =&gt; <var>string</var> (7) \"2025-10\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[8]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>9</dfn> =&gt; <var>string</var> (7) \"2025-11\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[9]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>10</dfn> =&gt; <var>string</var> (7) \"2025-12\"<div class=\"access-path\">json_decode($value[1]['payment_periods'], true)[10]</div></dt></dl></li><li><pre>[\"2025-02\",\"2025-03\",\"2025-04\",\"2025-05\",\"2025-06\",\"2025-07\",\"2025-08\",\"2025-09\",\"2025-10\",\"2025-11\",\"2025-12\"]\n</pre></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:59:14\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:59:14\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (12) \"Adnan Al-Ali\"<div class=\"access-path\">$value[1]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"430.00\"<div class=\"access-path\">$value[1]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[1]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[2]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[2]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[2]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050001\"<div class=\"access-path\">$value[2]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[2]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (4) \"cash\"<div class=\"access-path\">$value[2]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[2]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_periods</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['payment_periods']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 12:35:13\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 12:35:13\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Murad\"<div class=\"access-path\">$value[2]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"100.00\"<div class=\"access-path\">$value[2]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (8) \"one-time\"<div class=\"access-path\">$value[2]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"4\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (1) \"6\"<div class=\"access-path\">$value[3]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (6) \"600.00\"<div class=\"access-path\">$value[3]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-24\"<div class=\"access-path\">$value[3]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (14) \"RCPT2025050004\"<div class=\"access-path\">$value[3]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (1) \"3\"<div class=\"access-path\">$value[3]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (5) \"other\"<div class=\"access-path\">$value[3]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>string</var> (52) \"Payment for: January 2025, February 2025, March 2025\"<div class=\"access-path\">$value[3]['notes']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>payment_periods</dfn> =&gt; <var>string</var> (31) \"[\"2025-01\",\"2025-02\",\"2025-03\"]\"<div class=\"access-path\">$value[3]['payment_periods']</div></dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Json (3)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>0</dfn> =&gt; <var>string</var> (7) \"2025-01\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[0]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>1</dfn> =&gt; <var>string</var> (7) \"2025-02\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[1]</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>2</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">json_decode($value[3]['payment_periods'], true)[2]</div></dt></dl></li></ul></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 14:13:50\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 14:13:50\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (5) \"Riyas\"<div class=\"access-path\">$value[3]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (6) \"200.00\"<div class=\"access-path\">$value[3]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[3]['commitment_frequency']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (15)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[4]['payment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"14\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_id</dfn> =&gt; <var>string</var> (2) \"26\"<div class=\"access-path\">$value[4]['commitment_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[4]['amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-19\"<div class=\"access-path\">$value[4]['payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_number</dfn> =&gt; <var>string</var> (4) \"8961\"<div class=\"access-path\">$value[4]['receipt_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>receipt_book_number</dfn> =&gt; <var>string</var> (4) \"BK02\"<div class=\"access-path\">$value[4]['receipt_book_number']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_method</dfn> =&gt; <var>string</var> (4) \"cash\"<div class=\"access-path\">$value[4]['payment_method']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>notes</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['notes']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>payment_periods</dfn> =&gt; <var>string</var> (7) \"2025-03\"<div class=\"access-path\">$value[4]['payment_periods']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Ibrahim Al-Saud\"<div class=\"access-path\">$value[4]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_amount</dfn> =&gt; <var>string</var> (5) \"70.00\"<div class=\"access-path\">$value[4]['commitment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>commitment_frequency</dfn> =&gt; <var>string</var> (7) \"monthly\"<div class=\"access-path\">$value[4]['commitment_frequency']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "membersWithOutstandingBalances": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>summary_id</th><th>member_id</th><th>total_committed</th><th>total_paid</th><th>balance</th><th>last_payment_date</th><th>last_payment_amount</th><th>created_at</th><th>updated_at</th><th>member_name</th><th>status</th><th>phone</th><th>whatsapp_number</th></tr></thead><tbody><tr><th>0</th><td title=\"string (2)\">28</td><td title=\"string (2)\">28</td><td title=\"string (7)\">9540.00</td><td title=\"string (6)\">340.00</td><td title=\"string (7)\">9200.00</td><td title=\"string (10)\">2024-11-10</td><td title=\"string (6)\">170.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (13)\">Saud Al-Ahmad</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0592485468</td><td title=\"string (10)\">0554246003</td></tr><tr><th>1</th><td title=\"string (2)\">18</td><td title=\"string (2)\">18</td><td title=\"string (7)\">9150.00</td><td title=\"string (6)\">380.00</td><td title=\"string (7)\">8770.00</td><td title=\"string (10)\">2025-05-07</td><td title=\"string (6)\">190.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (15)\">Bilal Al-Waleed</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0515351788</td><td title=\"string (10)\">0522375982</td></tr><tr><th>2</th><td title=\"string (2)\">21</td><td title=\"string (2)\">21</td><td title=\"string (8)\">10020.00</td><td title=\"string (7)\">1480.00</td><td title=\"string (7)\">8540.00</td><td title=\"string (10)\">2025-03-10</td><td title=\"string (6)\">490.00</td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (14)\">Bilal Al-Jamal</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0528141858</td><td title=\"string (10)\">0523298126</td></tr><tr><th>3</th><td title=\"string (2)\">30</td><td title=\"string (2)\">30</td><td title=\"string (7)\">6690.00</td><td title=\"string (4)\">0.00</td><td title=\"string (7)\">6690.00</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (17)\">Ibrahim Al-Hassan</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0549461895</td><td title=\"string (10)\">0582341569</td></tr><tr><th>4</th><td title=\"string (2)\">29</td><td title=\"string (2)\">29</td><td title=\"string (7)\">6100.00</td><td title=\"string (4)\">0.00</td><td title=\"string (7)\">6100.00</td><td title=\"null\"><var>null</var></td><td title=\"null\"><var>null</var></td><td title=\"string (19)\">2025-05-24 16:32:17</td><td title=\"string (19)\">2025-05-24 16:46:44</td><td title=\"string (15)\">Salman Al-Bilal</td><td title=\"string (6)\">active</td><td title=\"string (10)\">0541587438</td><td title=\"string (10)\">0523101499</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[0]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"28\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"9540.00\"<div class=\"access-path\">$value[0]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (6) \"340.00\"<div class=\"access-path\">$value[0]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"9200.00\"<div class=\"access-path\">$value[0]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2024-11-10\"<div class=\"access-path\">$value[0]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"170.00\"<div class=\"access-path\">$value[0]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (13) \"Saud Al-Ahmad\"<div class=\"access-path\">$value[0]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0592485468\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>1988-10-10T11:17:48+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0554246003\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>1987-07-25T21:13:23+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"18\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"9150.00\"<div class=\"access-path\">$value[1]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (6) \"380.00\"<div class=\"access-path\">$value[1]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8770.00\"<div class=\"access-path\">$value[1]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-05-07\"<div class=\"access-path\">$value[1]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"190.00\"<div class=\"access-path\">$value[1]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Bilal Al-Waleed\"<div class=\"access-path\">$value[1]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0515351788\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>1986-05-01T17:16:28+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0522375982\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>1986-07-22T00:26:22+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"21\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (8) \"10020.00\"<div class=\"access-path\">$value[2]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (7) \"1480.00\"<div class=\"access-path\">$value[2]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"8540.00\"<div class=\"access-path\">$value[2]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>string</var> (10) \"2025-03-10\"<div class=\"access-path\">$value[2]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>string</var> (6) \"490.00\"<div class=\"access-path\">$value[2]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (14) \"Bilal Al-Jamal\"<div class=\"access-path\">$value[2]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0528141858\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1986-09-26T18:04:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523298126\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1986-08-01T16:35:26+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"30\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"6690.00\"<div class=\"access-path\">$value[3]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[3]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6690.00\"<div class=\"access-path\">$value[3]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (17) \"Ibrahim Al-Hassan\"<div class=\"access-path\">$value[3]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0549461895\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1987-05-31T12:18:15+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0582341569\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1988-06-15T01:32:49+00:00\n</pre></dd></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (13)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>summary_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[4]['summary_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"29\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_committed</dfn> =&gt; <var>string</var> (7) \"6100.00\"<div class=\"access-path\">$value[4]['total_committed']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>total_paid</dfn> =&gt; <var>string</var> (4) \"0.00\"<div class=\"access-path\">$value[4]['total_paid']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>balance</dfn> =&gt; <var>string</var> (7) \"6100.00\"<div class=\"access-path\">$value[4]['balance']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_date</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>last_payment_amount</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['last_payment_amount']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:32:17\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 16:46:44\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_name</dfn> =&gt; <var>string</var> (15) \"Salman Al-Bilal\"<div class=\"access-path\">$value[4]['member_name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0541587438\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1987-03-01T08:57:18+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0523101499\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1986-07-30T09:58:19+00:00\n</pre></dd></dl></dd></dl></li></ul></dd></dl></div>", "membersWithoutActiveCommitments": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (5)</dt><dd><ul class=\"kint-tabs\"><li class=\"kint-active-tab\">Table (5)</li><li>Contents (5)</li></ul><ul class=\"kint-tab-contents\"><li class=\"kint-show\"><pre><table><thead><tr><th></th><th>member_id</th><th>name</th><th>email</th><th>post_office</th><th>old_reference</th><th>phone</th><th>whatsapp_number</th><th>address</th><th>join_date</th><th>status</th><th>created_at</th><th>updated_at</th></tr></thead><tbody><tr><th>0</th><td title=\"string (1)\">1</td><td title=\"string (14)\">Abdu Raheem KK</td><td title=\"string (18)\"><EMAIL></td><td title=\"string (10)\">Karalmanna</td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777125</td><td title=\"string (10)\">9947777125</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2018-01-01</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 22:16:26</td><td title=\"string (19)\">2025-05-24 10:30:28</td></tr><tr><th>1</th><td title=\"string (1)\">2</td><td title=\"string (7)\">Subeena</td><td title=\"string (15)\"><EMAIL></td><td title=\"string (0)\"></td><td title=\"string (0)\"></td><td title=\"string (10)\">9947777124</td><td title=\"string (10)\">9605447793</td><td title=\"string (73)\">Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road</td><td title=\"string (10)\">2025-05-18</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-17 23:25:30</td><td title=\"string (19)\">2025-05-24 10:30:55</td></tr><tr><th>2</th><td title=\"string (1)\">5</td><td title=\"string (14)\">Omar Al-Nasser</td><td title=\"string (26)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">HIST-C-001</td><td title=\"string (10)\">0537494636</td><td title=\"string (10)\">0572364484</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-11-09</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:20</td><td title=\"string (19)\">2025-05-24 21:58:20</td></tr><tr><th>3</th><td title=\"string (1)\">7</td><td title=\"string (9)\">Zaid Khan</td><td title=\"string (21)\"><EMAIL></td><td title=\"string (8)\">Downtown</td><td title=\"string (10)\">OLD-A1-002</td><td title=\"string (10)\">0537155671</td><td title=\"string (10)\">0537155671</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-04-02</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td></tr><tr><th>4</th><td title=\"string (2)\">10</td><td title=\"string (15)\">Fahad Al-Faisal</td><td title=\"string (27)\"><EMAIL></td><td title=\"string (15)\">Industrial Area</td><td title=\"string (12)\">PREV-002-005</td><td title=\"string (10)\">0540420476</td><td title=\"string (10)\">0579349018</td><td title=\"null\"><var>null</var></td><td title=\"string (10)\">2024-03-14</td><td title=\"string (6)\">active</td><td title=\"string (19)\">2025-05-24 21:58:57</td><td title=\"string (19)\">2025-05-24 21:58:57</td></tr></tbody></table></pre></li><li><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>0</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[0]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"1\"<div class=\"access-path\">$value[0]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Abdu Raheem KK\"<div class=\"access-path\">$value[0]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (18) \"<EMAIL>\"<div class=\"access-path\">$value[0]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (10) \"Karalmanna\"<div class=\"access-path\">$value[0]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[0]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[0]['phone']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9947777125\"<div class=\"access-path\">$value[0]['whatsapp_number']</div></dt><dd><pre>2285-03-26T07:25:25+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[0]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2018-01-01\"<div class=\"access-path\">$value[0]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[0]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 22:16:26\"<div class=\"access-path\">$value[0]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:28\"<div class=\"access-path\">$value[0]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>1</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[1]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"2\"<div class=\"access-path\">$value[1]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (7) \"Subeena\"<div class=\"access-path\">$value[1]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (15) \"<EMAIL>\"<div class=\"access-path\">$value[1]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (0) \"\"<div class=\"access-path\">$value[1]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"9947777124\"<div class=\"access-path\">$value[1]['phone']</div></dt><dd><pre>2285-03-26T07:25:24+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"9605447793\"<div class=\"access-path\">$value[1]['whatsapp_number']</div></dt><dd><pre>2274-05-21T03:56:33+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>address</dfn> =&gt; <var>string</var> (73) \"Noor Mahal, Pottathiparamb, Karalmanna P O Karalmanna CCST college road\"<div class=\"access-path\">$value[1]['address']</div></dt><dd><pre>Noor Mahal, Pottathiparamb, Karalmanna P O\r\nKaralmanna\r\nCCST college road\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2025-05-18\"<div class=\"access-path\">$value[1]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[1]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-17 23:25:30\"<div class=\"access-path\">$value[1]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 10:30:55\"<div class=\"access-path\">$value[1]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>2</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[2]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"5\"<div class=\"access-path\">$value[2]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (14) \"Omar Al-Nasser\"<div class=\"access-path\">$value[2]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (26) \"<EMAIL>\"<div class=\"access-path\">$value[2]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[2]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"HIST-C-001\"<div class=\"access-path\">$value[2]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537494636\"<div class=\"access-path\">$value[2]['phone']</div></dt><dd><pre>1987-01-13T00:03:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0572364484\"<div class=\"access-path\">$value[2]['whatsapp_number']</div></dt><dd><pre>1988-02-20T14:08:04+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[2]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-11-09\"<div class=\"access-path\">$value[2]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[2]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[2]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:20\"<div class=\"access-path\">$value[2]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>3</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[3]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (1) \"7\"<div class=\"access-path\">$value[3]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (9) \"Zaid Khan\"<div class=\"access-path\">$value[3]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (21) \"<EMAIL>\"<div class=\"access-path\">$value[3]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (8) \"Downtown\"<div class=\"access-path\">$value[3]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (10) \"OLD-A1-002\"<div class=\"access-path\">$value[3]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[3]['phone']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0537155671\"<div class=\"access-path\">$value[3]['whatsapp_number']</div></dt><dd><pre>1987-01-09T01:54:31+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[3]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-04-02\"<div class=\"access-path\">$value[3]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[3]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[3]['updated_at']</div></dt></dl></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>4</dfn> =&gt; <var>array</var> (12)<div class=\"access-path\">$value[4]</div></dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>member_id</dfn> =&gt; <var>string</var> (2) \"10\"<div class=\"access-path\">$value[4]['member_id']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>name</dfn> =&gt; <var>string</var> (15) \"Fahad Al-Faisal\"<div class=\"access-path\">$value[4]['name']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>email</dfn> =&gt; <var>string</var> (27) \"<EMAIL>\"<div class=\"access-path\">$value[4]['email']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>post_office</dfn> =&gt; <var>string</var> (15) \"Industrial Area\"<div class=\"access-path\">$value[4]['post_office']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>old_reference</dfn> =&gt; <var>string</var> (12) \"PREV-002-005\"<div class=\"access-path\">$value[4]['old_reference']</div></dt></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>phone</dfn> =&gt; <var>string</var> (10) \"0540420476\"<div class=\"access-path\">$value[4]['phone']</div></dt><dd><pre>1987-02-15T20:47:56+00:00\n</pre></dd></dl><dl><dt class=\"kint-parent\"><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><nav></nav><dfn>whatsapp_number</dfn> =&gt; <var>string</var> (10) \"0579349018\"<div class=\"access-path\">$value[4]['whatsapp_number']</div></dt><dd><pre>1988-05-11T10:16:58+00:00\n</pre></dd></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>address</dfn> =&gt; <var>null</var><div class=\"access-path\">$value[4]['address']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>join_date</dfn> =&gt; <var>string</var> (10) \"2024-03-14\"<div class=\"access-path\">$value[4]['join_date']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>status</dfn> =&gt; <var>string</var> (6) \"active\"<div class=\"access-path\">$value[4]['status']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>created_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['created_at']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>updated_at</dfn> =&gt; <var>string</var> (19) \"2025-05-24 21:58:57\"<div class=\"access-path\">$value[4]['updated_at']</div></dt></dl></dd></dl></li></ul></dd></dl></div>", "startDate": "2024-05-28", "endDate": "2025-05-28", "months": "[&quot;May 2024&quot;,&quot;Jun 2024&quot;,&quot;Jul 2024&quot;,&quot;Aug 2024&quot;,&quot;Sep 2024&quot;,&quot;Oct 2024&quot;,&quot;Nov 2024&quot;,&quot;Dec 2024&quot;,&quot;Jan 2025&quot;,&quot;Feb 2025&quot;,&quot;Mar 2025&quot;,&quot;Apr 2025&quot;,&quot;May 2025&quot;]", "committedAmounts": "[360,2060,3670,3460,5820,6470,7320,8360,9430,8460,8730,9280,10020]", "paidAmounts": "[0,950,450,1230,920,530,790,980,2930,1370,2270,750,1460]", "memberCounts": "[2,7,11,12,17,17,19,23,22,22,20,24,24]", "datePaidAmounts": "[50,0,2000,450,530,1320,900,970,1160,2260,2200,1710,6640]", "fulfillmentRates": "[0,46.1,12.3,35.5,15.8,8.2,10.8,11.7,31.1,16.2,26,8.1,14.6]", "movingAverages": "[50,25,683.********33334,816.6666666666666,993.********33334,766.6666666666666,916.6666666666666,1063.********33333,1010,1463.********33333,1873.********33333,2056.*************,3516.*************]", "memberComplianceData": "{&quot;labels&quot;:[&quot;<PERSON>y Paid (100%)&quot;,&quot;Mostly Paid (75-99%)&quot;,&quot;Partially Paid (25-74%)&quot;,&quot;Minimally Paid (1-24%)&quot;,&quot;No Payments (0%)&quot;],&quot;data&quot;:[4,0,4,15,11],&quot;backgroundColor&quot;:[&quot;#28a745&quot;,&quot;#5cb85c&quot;,&quot;#ffc107&quot;,&quot;#fd7e14&quot;,&quot;#dc3545&quot;]}", "paymentMethodData": "{&quot;labels&quot;:[&quot;Cash&quot;,&quot;Check&quot;,&quot;Bank_transfer&quot;,&quot;Other&quot;],&quot;data&quot;:[55,1,1,1],&quot;backgroundColor&quot;:[&quot;#28a745&quot;,&quot;#6f42c1&quot;,&quot;#6c757d&quot;,&quot;#6c757d&quot;]}", "collectionEfficiencyData": "<div class=\"kint-rich\"><dl><dt class=\"kint-parent\"><span class=\"kint-folder-trigger\" title=\"Move to folder\"></span><span class=\"kint-search-trigger\" title=\"Show search box\"></span><input type=\"text\" class=\"kint-search\" value=\"\"><nav></nav><dfn>$value</dfn> <var>array</var> (4)</dt><dd><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>currentMonthRate</dfn> =&gt; <var>double</var> 14.6<div class=\"access-path\">$value['currentMonthRate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>ytdRate</dfn> =&gt; <var>double</var> 19.1<div class=\"access-path\">$value['ytdRate']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>avgMonths</dfn> =&gt; <var>double</var> 2.8<div class=\"access-path\">$value['avgMonths']</div></dt></dl><dl><dt><span class=\"kint-access-path-trigger\" title=\"Show access path\"></span><dfn>percentWithBalance</dfn> =&gt; <var>double</var> 85.7<div class=\"access-path\">$value['percentWithBalance']</div></dt></dl></dd></dl></div>"}}, "session": {"_ci_previous_url": "https://halqa.mazharulirfan.com/reports", "admin_id": "2", "username": "boss", "isLoggedIn": "<pre>1</pre>", "success": "Login successful", "__ci_vars": "<pre>Array\n(\n    [success] =&gt; old\n)\n</pre>"}, "headers": {"Accept": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7", "Accept-Encoding": "br", "Accept-Language": "en-US,en;q=0.9", "Cookie": "_ga=GA1.2.1969489433.1748457976; _gid=GA1.2.22748706.1748457976; _ga_9Q6H0QETRF=GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0; ci_session=7fde70c20450d640f128b0dbe97f109f", "Host": "halqa.mazharulirfan.com", "Referer": "https://halqa.mazharulirfan.com/login", "User-Agent": "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Cache-Control": "max-age=0", "X-Forwarded-For": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Forwarded-Proto": "https", "X-Real-Ip": "2409:40f3:112b:6565:21c6:6cbd:de74:42cc", "X-Real-Port": "54239", "X-Forwarded-Port": "443", "X-Port": "443", "X-Lscache": "1", "Upgrade-Insecure-Requests": "1", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "navigate", "Sec-Fetch-User": "?1", "Sec-Fetch-Dest": "document", "Sec-Ch-Ua": "&quot;Chromium&quot;;v=&quot;136&quot;, &quot;Google Chrome&quot;;v=&quot;136&quot;, &quot;Not.A/Brand&quot;;v=&quot;99&quot;", "Sec-Ch-Ua-Mobile": "?0", "Sec-Ch-Ua-Platform": "&quot;macOS&quot;", "Priority": "u=0, i"}, "cookies": {"_ga": "GA1.2.1969489433.1748457976", "_gid": "GA1.2.22748706.1748457976", "_ga_9Q6H0QETRF": "GS2.2.s1748457976$o1$g0$t1748457976$j60$l0$h0", "ci_session": "7fde70c20450d640f128b0dbe97f109f"}, "request": "HTTPS/1.1", "response": {"statusCode": 200, "reason": "OK", "contentType": "text/html; charset=UTF-8", "headers": {"Content-Type": "text/html; charset=UTF-8", "X-Content-Type-Options": "nosniff", "X-Frame-Options": "DENY", "X-XSS-Protection": "1; mode=block"}}}, "config": {"ciVersion": "4.6.1", "phpVersion": "8.2.27", "phpSAPI": "litespeed", "environment": "development", "baseURL": "https://halqa.mazharulirfan.com/", "timezone": "UTC", "locale": "en", "cspEnabled": false}}