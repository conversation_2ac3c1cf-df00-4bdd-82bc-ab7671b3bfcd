<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Collection Summary</h1>
    <div>
        <a href="<?= site_url('reports/recalculate-all-summaries') ?>" class="btn btn-warning" onclick="return confirm('Are you sure you want to recalculate all summaries? This may take a moment.');">
            <i class="fas fa-sync"></i> Recalculate All Summaries
        </a>
    </div>
</div>

<!-- Date Filter Form -->
<div class="card mb-4">
    <div class="card-body">
        <form method="GET" action="<?= site_url('reports/collection-summary') ?>" class="row g-3 align-items-end">
            <div class="col-md-2">
                <label for="from_date" class="form-label">From Date</label>
                <input type="month" class="form-control" id="from_date" name="from_date" value="<?= date('Y-m', strtotime($from_date)) ?>" required>
            </div>
            <div class="col-md-2">
                <label for="to_date" class="form-label">To Date</label>
                <input type="month" class="form-control" id="to_date" name="to_date" value="<?= date('Y-m', strtotime($to_date)) ?>" required>
            </div>
            <div class="col-md-4">
                <label class="form-label">Balance Filter</label>
                <div class="btn-group w-100" role="group">
                    <input type="radio" class="btn-check" name="exclude_zero" id="show_all" value="no" <?= $exclude_zero === 'no' ? 'checked' : '' ?>>
                    <label class="btn btn-outline-primary" for="show_all">
                        <i class="fas fa-list"></i> Show All
                    </label>

                    <input type="radio" class="btn-check" name="exclude_zero" id="hide_zero_balance" value="yes" <?= $exclude_zero === 'yes' ? 'checked' : '' ?>>
                    <label class="btn btn-outline-primary" for="hide_zero_balance">
                        <i class="fas fa-filter"></i> Hide Zero Balances
                    </label>

                    <input type="radio" class="btn-check" name="exclude_zero" id="hide_zero_commitments" value="commitments" <?= ($exclude_zero === 'commitments' || empty($exclude_zero)) ? 'checked' : '' ?>>
                    <label class="btn btn-outline-primary" for="hide_zero_commitments">
                        <i class="fas fa-eye-slash"></i> Hide Zero Commitments
                    </label>
                </div>
            </div>
            <div class="col-md-2">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-search"></i> Filter
                </button>
                <a href="<?= site_url('reports/collection-summary') ?>" class="btn btn-secondary">
                    <i class="fas fa-refresh"></i> Reset
                </a>
            </div>
        </form>
        <div class="mt-2">
            <small class="text-muted">
                <i class="fas fa-info-circle"></i>
                This filter shows commitments for the selected months and payments made for those specific months, regardless of when the commitment was created or payment was made.
            </small>
        </div>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>Member ID</th>
                        <th>Member</th>
                        <th class="text-center">Contact</th>
                        <th>Total Committed</th>
                        <th>Total Paid</th>
                        <th>Balance</th>
                        <th>Last Payment</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
                    <?php
                    $totalCommitted = 0;
                    $totalPaid = 0;
                    $totalBalance = 0;
                    ?>

                    <?php foreach ($summaries as $summary): ?>
                        <?php
                        $totalCommitted += $summary['total_committed'];
                        $totalPaid += $summary['total_paid'];
                        $totalBalance += $summary['balance'];
                        ?>
                        <tr>
                            <td>
                                <span class="badge bg-secondary"><?= $summary['member_id'] ?></span>
                            </td>
                            <td>
                                <a href="<?= site_url('members/show/' . $summary['member_id']) ?>" class="<?= $summary['status'] == 'active' ? 'text-success fw-bold' : 'text-danger' ?>">
                                    <?= $summary['member_name'] ?>
                                </a>
                            </td>
                            <td class="text-center">
                                <div class="d-flex justify-content-center align-items-center gap-4">
                                    <?php if (!empty($summary['whatsapp_number'])): ?>
                                        <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $summary['whatsapp_number']) ?>?text=Hello%20<?= urlencode($summary['member_name']) ?>,%20this%20is%20a%20reminder%20about%20your%20outstanding%20balance%20of%20<?= urlencode(format_currency($summary['balance'])) ?>." target="_blank" class="text-decoration-none" data-bs-toggle="tooltip" title="WhatsApp">
                                            <i class="fab fa-whatsapp text-success fa-lg"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (!empty($summary['phone'])): ?>
                                        <a href="tel:<?= $summary['phone'] ?>" class="text-decoration-none" data-bs-toggle="tooltip" title="Call">
                                            <i class="fas fa-phone-alt text-success fa-lg"></i>
                                        </a>
                                    <?php endif; ?>

                                    <?php if (empty($summary['phone']) && empty($summary['whatsapp_number'])): ?>
                                        <span class="text-muted">—</span>
                                    <?php endif; ?>
                                </div>
                            </td>
                            <td><?= format_currency($summary['total_committed']) ?></td>
                            <td><?= format_currency($summary['total_paid']) ?></td>
                            <td class="<?= $summary['balance'] > 0 ? 'text-danger fw-bold' : 'text-success' ?>">
                                <?= format_currency($summary['balance']) ?>
                            </td>
                            <td>
                                <?php if ($summary['last_payment_date']): ?>
                                    <?= date('d M Y', strtotime($summary['last_payment_date'])) ?>
                                    <br>
                                    <small class="text-muted">
                                        Amount: <?= format_currency($summary['last_payment_amount']) ?>
                                    </small>
                                <?php else: ?>
                                    <span class="text-muted">No payments</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php if ($summary['status'] == 'active'): ?>
                                    <span class="badge bg-success">Active</span>
                                <?php else: ?>
                                    <span class="badge bg-danger">Inactive</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; ?>
                </tbody>
                <tfoot>
                    <tr class="table-dark">
                        <th></th>
                        <th>TOTAL</th>
                        <th></th>
                        <th><?= number_format($totalCommitted, 0) ?></th>
                        <th><?= number_format($totalPaid, 0) ?></th>
                        <th><?= number_format($totalBalance, 0) ?></th>
                        <th></th>
                        <th></th>
                    </tr>
                </tfoot>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Destroy existing DataTable if it exists
    if ($.fn.DataTable.isDataTable('.datatable')) {
        $('.datatable').DataTable().destroy();
    }

    // Custom DataTables configuration for collection summary
    $('.datatable').DataTable({
        responsive: true,
        order: [[0, 'desc']], // Sort by Member ID descending by default
        dom: '<"row"<"col-sm-6"f><"col-sm-6"l>><"row"<"col-sm-12"tr>><"row"<"col-sm-5"i><"col-sm-7"p>>',
        columnDefs: [
            {
                // Member ID column - numeric sorting
                targets: 0,
                type: 'num'
            },
            {
                // Member Name column - string sorting (default)
                targets: 1,
                type: 'string'
            },
            {
                // Contact column - disable sorting
                targets: 2,
                orderable: false
            },
            {
                // Total Committed column - currency sorting
                targets: 3,
                type: 'currency',
                render: function(data, type, row) {
                    if (type === 'sort' || type === 'type') {
                        // Extract numeric value for sorting
                        return parseFloat(data.replace(/[₹,\s]/g, '')) || 0;
                    }
                    return data;
                }
            },
            {
                // Total Paid column - currency sorting
                targets: 4,
                type: 'currency',
                render: function(data, type, row) {
                    if (type === 'sort' || type === 'type') {
                        // Extract numeric value for sorting
                        return parseFloat(data.replace(/[₹,\s]/g, '')) || 0;
                    }
                    return data;
                }
            },
            {
                // Balance column - currency sorting
                targets: 5,
                type: 'currency',
                render: function(data, type, row) {
                    if (type === 'sort' || type === 'type') {
                        // Extract numeric value for sorting
                        return parseFloat(data.replace(/[₹,\s]/g, '')) || 0;
                    }
                    return data;
                }
            },
            {
                // Last Payment column - date sorting
                targets: 6,
                type: 'date',
                render: function(data, type, row) {
                    if (type === 'sort' || type === 'type') {
                        // Extract date for sorting, handle "No payments" case
                        if (data.includes('No payments')) {
                            return '1900-01-01'; // Very old date for "No payments"
                        }
                        // Extract date from the HTML content
                        var dateMatch = data.match(/(\d{2}\s\w{3}\s\d{4})/);
                        if (dateMatch) {
                            return new Date(dateMatch[1]).getTime();
                        }
                        return 0;
                    }
                    return data;
                }
            },
            {
                // Status column - string sorting
                targets: 7,
                type: 'string'
            }
        ]
    });
});
</script>
<?= $this->endSection() ?>
