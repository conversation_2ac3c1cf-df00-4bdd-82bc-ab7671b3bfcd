<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit Payment</h1>
    <a href="<?= site_url('payments/show/' . $payment['payment_id']) ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Payment
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form action="<?= site_url('payments/update/' . $payment['payment_id']) ?>" method="post" id="paymentForm">
            <?= csrf_field() ?>

            <!-- Hidden field to track if payment was edited from member page -->
            <?php if (isset($returnToMember)): ?>
                <input type="hidden" name="return_to_member" value="<?= $returnToMember ?>">
            <?php endif; ?>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="member_id" class="form-label">Member <span class="text-danger">*</span></label>
                    <select class="form-select <?= session('errors.member_id') ? 'is-invalid' : '' ?>" id="member_id" name="member_id" required>
                        <option value="">Select Member</option>
                        <?php foreach ($members as $member): ?>
                            <option value="<?= $member['member_id'] ?>" <?= old('member_id', $payment['member_id']) == $member['member_id'] ? 'selected' : '' ?>>
                                <?= $member['name'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if (session('errors.member_id')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.member_id') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <label for="commitment_id" class="form-label">Commitment</label>
                    <select class="form-select <?= session('errors.commitment_id') ? 'is-invalid' : '' ?>" id="commitment_id" name="commitment_id">
                        <option value="">Select Commitment</option>
                        <?php foreach ($commitments as $commitment): ?>
                            <option value="<?= $commitment['commitment_id'] ?>" <?= old('commitment_id', $payment['commitment_id']) == $commitment['commitment_id'] ? 'selected' : '' ?>>
                                <?= format_currency_with_decimals($commitment['amount']) ?> (<?= ucfirst($commitment['frequency']) ?>)
                                <?php if ($commitment['start_date']): ?>
                                    from <?= date('d M Y', strtotime($commitment['start_date'])) ?>
                                <?php endif; ?>
                                <?php if ($commitment['end_date']): ?>
                                    to <?= date('d M Y', strtotime($commitment['end_date'])) ?>
                                <?php endif; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if (session('errors.commitment_id')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.commitment_id') ?>
                        </div>
                    <?php endif; ?>
                    <div class="form-text">Optional: Select a specific commitment this payment is for</div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">₹</span>
                        <input type="number" step="0.01" class="form-control <?= session('errors.amount') ? 'is-invalid' : '' ?>" id="amount" name="amount" value="<?= old('amount', $payment['amount']) ?>" required>
                        <?php if (session('errors.amount')): ?>
                            <div class="invalid-feedback">
                                <?= session('errors.amount') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>

                <div class="col-md-6">
                    <label for="payment_date" class="form-label">Payment Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control <?= session('errors.payment_date') ? 'is-invalid' : '' ?>" id="payment_date" name="payment_date" value="<?= old('payment_date', $payment['payment_date']) ?>" required>
                    <?php if (session('errors.payment_date')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.payment_date') ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="receipt_book_number" class="form-label">Receipt Book # <span class="text-danger">*</span></label>
                    <input type="text" class="form-control <?= session('errors.receipt_book_number') ? 'is-invalid' : '' ?>" id="receipt_book_number" name="receipt_book_number" value="<?= old('receipt_book_number', $payment['receipt_book_number'] ?? '') ?>" required>
                    <?php if (session('errors.receipt_book_number')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.receipt_book_number') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <label for="receipt_number" class="form-label">Receipt Number <span class="text-danger">*</span></label>
                    <input type="text" class="form-control <?= session('errors.receipt_number') ? 'is-invalid' : '' ?>" id="receipt_number" name="receipt_number" value="<?= old('receipt_number', $payment['receipt_number']) ?>" required>
                    <?php if (session('errors.receipt_number')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.receipt_number') ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                    <select class="form-select <?= session('errors.payment_method') ? 'is-invalid' : '' ?>" id="payment_method" name="payment_method" required>
                        <option value="">Select Payment Method</option>
                        <option value="cash" <?= old('payment_method', $payment['payment_method']) == 'cash' ? 'selected' : '' ?>>Cash</option>
                        <option value="check" <?= old('payment_method', $payment['payment_method']) == 'check' ? 'selected' : '' ?>>Check</option>
                        <option value="bank_transfer" <?= old('payment_method', $payment['payment_method']) == 'bank_transfer' ? 'selected' : '' ?>>Bank Transfer</option>
                        <option value="other" <?= old('payment_method', $payment['payment_method']) == 'other' ? 'selected' : '' ?>>Other</option>
                    </select>
                    <?php if (session('errors.payment_method')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.payment_method') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <!-- Empty column for layout balance -->
                </div>
            </div>

            <div class="mb-3">
                <label for="notes" class="form-label">Notes</label>
                <textarea class="form-control <?= session('errors.notes') ? 'is-invalid' : '' ?>" id="notes" name="notes" rows="3"><?= old('notes', $payment['notes']) ?></textarea>
                <?php if (session('errors.notes')): ?>
                    <div class="invalid-feedback">
                        <?= session('errors.notes') ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="reset" class="btn btn-secondary">Reset</button>
                <button type="submit" class="btn btn-primary">Update Payment</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        // When member is selected, load their commitments
        $('#member_id').change(function() {
            var memberId = $(this).val();
            if (memberId) {
                // Clear current options
                $('#commitment_id').html('<option value="">Select Commitment</option>');

                // Load commitments via AJAX
                $.ajax({
                    url: '<?= site_url('api/member-commitments') ?>/' + memberId,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        if (data.length > 0) {
                            $.each(data, function(key, value) {
                                var option = '<option value="' + value.commitment_id + '">';
                                option += value.amount + ' (' + value.frequency + ')';
                                if (value.start_date) {
                                    option += ' from ' + new Date(value.start_date).toLocaleDateString();
                                }
                                if (value.end_date) {
                                    option += ' to ' + new Date(value.end_date).toLocaleDateString();
                                }
                                option += '</option>';
                                $('#commitment_id').append(option);
                            });
                        } else {
                            $('#commitment_id').append('<option value="">No commitments found</option>');
                        }
                    },
                    error: function() {
                        $('#commitment_id').append('<option value="">Error loading commitments</option>');
                    }
                });
            }
        });

        // Trigger change if a member is already selected (e.g., from URL parameter)
        if ($('#member_id').val()) {
            $('#member_id').trigger('change');
        }
    });
</script>
<?= $this->endSection() ?>
