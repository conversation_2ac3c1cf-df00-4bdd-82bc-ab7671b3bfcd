<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;

class Performance extends BaseConfig
{
    /**
     * --------------------------------------------------------------------------
     * Output Compression
     * --------------------------------------------------------------------------
     *
     * Enables gzip output compression for faster page loading.
     * Only enable if your server doesn't already handle compression.
     */
    public bool $compressionEnabled = true;

    /**
     * --------------------------------------------------------------------------
     * Compression Level
     * --------------------------------------------------------------------------
     *
     * Compression level (1-9). Higher levels provide better compression
     * but use more CPU. Level 6 is a good balance.
     */
    public int $compressionLevel = 6;

    /**
     * --------------------------------------------------------------------------
     * Cache Headers
     * --------------------------------------------------------------------------
     *
     * Enable browser caching for static assets.
     */
    public bool $cacheHeaders = true;

    /**
     * --------------------------------------------------------------------------
     * Cache Duration (seconds)
     * --------------------------------------------------------------------------
     *
     * How long browsers should cache static assets.
     * 86400 = 1 day, 604800 = 1 week, 2592000 = 1 month
     */
    public int $cacheDuration = 2592000; // 1 month

    /**
     * --------------------------------------------------------------------------
     * Minify HTML
     * --------------------------------------------------------------------------
     *
     * Remove unnecessary whitespace from HTML output.
     */
    public bool $minifyHTML = true;

    /**
     * --------------------------------------------------------------------------
     * Preload Critical Resources
     * --------------------------------------------------------------------------
     *
     * Resources to preload for better performance.
     */
    public array $preloadResources = [
        'https://cdn.jsdelivr.net/npm/bootstrap@5.3.0-alpha1/dist/css/bootstrap.min.css',
        'https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css',
    ];

    /**
     * --------------------------------------------------------------------------
     * Database Query Caching
     * --------------------------------------------------------------------------
     *
     * Enable caching for database queries.
     */
    public bool $queryCache = true;

    /**
     * --------------------------------------------------------------------------
     * Query Cache Duration (seconds)
     * --------------------------------------------------------------------------
     *
     * How long to cache database queries.
     */
    public int $queryCacheDuration = 300; // 5 minutes
}
