<?php

namespace Config;

// Create a new instance of our RouteCollection class.
$routes = Services::routes();

/*
 * --------------------------------------------------------------------
 * Router Setup
 * --------------------------------------------------------------------
 */
$routes->setDefaultNamespace('App\Controllers');
$routes->setDefaultController('Home');
$routes->setDefaultMethod('index');
$routes->setTranslateURIDashes(false);
$routes->set404Override();
// The Auto Routing (Legacy) is very dangerous. It is easy to create vulnerable apps
// where controller filters or CSRF protection are bypassed.
// If you don't want to define all routes, please use the Auto Routing (Improved).
// Set `$autoRoutesImproved` to true in `app/Config/Feature.php` and set the following to true.
// $routes->setAutoRoute(false);

/*
 * --------------------------------------------------------------------
 * Route Definitions
 * --------------------------------------------------------------------
 */

// We get a performance increase by specifying the default
// route since we don't have to scan directories.
$routes->get('/', 'Home::index');

// Authentication Routes
$routes->get('login', 'Auth::index');
$routes->post('auth/login', 'Auth::login');
$routes->get('auth/logout', 'Auth::logout');
$routes->get('auth/change-password', 'Auth::changePassword');
$routes->post('auth/update-password', 'Auth::updatePassword');

// Members Routes
$routes->group('members', function ($routes) {
    $routes->get('/', 'Members::index');
    $routes->get('new', 'Members::new');
    $routes->post('create', 'Members::create');
    $routes->get('show/(:num)', 'Members::show/$1');
    $routes->get('edit/(:num)', 'Members::edit/$1');
    $routes->post('update/(:num)', 'Members::update/$1');
    $routes->get('delete/(:num)', 'Members::delete/$1');
});

// Commitments Routes
$routes->group('commitments', function ($routes) {
    $routes->get('/', 'Commitments::index');
    $routes->get('new', 'Commitments::new');
    $routes->get('create/(:num)', 'Commitments::new/$1');  // Create commitment for specific member
    $routes->post('create', 'Commitments::create');
    $routes->get('show/(:num)', 'Commitments::show/$1');
    $routes->get('edit/(:num)', 'Commitments::edit/$1');
    $routes->post('update/(:num)', 'Commitments::update/$1');
    $routes->get('delete/(:num)', 'Commitments::delete/$1');
    $routes->get('member-commitments/(:num)', 'Commitments::memberCommitments/$1');
});

// Payments Routes
$routes->group('payments', function ($routes) {
    $routes->get('/', 'Payments::index');
    $routes->get('new', 'Payments::new');
    $routes->post('create', 'Payments::create');
    $routes->get('show/(:num)', 'Payments::show/$1');
    $routes->get('edit/(:num)', 'Payments::edit/$1');
    $routes->post('update/(:num)', 'Payments::update/$1');
    $routes->get('delete/(:num)', 'Payments::delete/$1');
    $routes->get('member-payments/(:num)', 'Payments::memberPayments/$1');
    $routes->get('generate-receipt/(:num)', 'Payments::generateReceipt/$1');
});

// Reports Routes
$routes->group('reports', function ($routes) {
    $routes->get('/', 'Reports::index');
    $routes->get('collection-summary', 'Reports::collectionSummary');
    $routes->get('outstanding-balances', 'Reports::outstandingBalances');
    $routes->get('payment-history', 'Reports::paymentHistory');
    $routes->get('commitment-report', 'Reports::commitmentReport');
    $routes->get('statistics', 'Reports::statistics');
    $routes->get('recalculate-all-summaries', 'Reports::recalculateAllSummaries');
    $routes->get('members-without-commitments', 'Reports::membersWithoutCommitments');
});

// API Routes for AJAX calls
$routes->group('api', function ($routes) {
    $routes->get('member-commitments/(:num)', 'Api::getMemberCommitments/$1');
    $routes->get('payment-details/(:num)/(:num)', 'Api::getPaymentDetails/$1/$2');
    $routes->get('payment-details/(:num)', 'Api::getPaymentDetails/$1');
});



/*
 * --------------------------------------------------------------------
 * Additional Routing
 * --------------------------------------------------------------------
 *
 * There will often be times that you need additional routing and you
 * need it to be able to override any defaults in this file. Environment
 * based routes is one such time. require() additional route files here
 * to make that happen.
 *
 * You will have access to the $routes object within that file without
 * needing to reload it.
 */
if (is_file(APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php')) {
    require APPPATH . 'Config/' . ENVIRONMENT . '/Routes.php';
}
