<?php

namespace App\Models;

use CodeIgniter\Model;

class AdminModel extends Model
{
    protected $table            = 'admins';
    protected $primaryKey       = 'admin_id';
    protected $useAutoIncrement = true;
    protected $returnType       = 'array';
    protected $useSoftDeletes   = false;
    protected $protectFields    = true;
    protected $allowedFields    = [
        'username',
        'password_hash',
        'email',
        'last_login'
    ];

    protected bool $allowEmptyInserts = false;
    protected bool $updateOnlyChanged = true;

    // Dates
    protected $useTimestamps = true;
    protected $dateFormat    = 'datetime';
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';

    // Validation
    protected $validationRules = [
        'username' => 'required|min_length[3]|max_length[50]|is_unique[admins.username,admin_id,{admin_id}]',
        'password' => 'required|min_length[8]',
        'email'    => 'permit_empty|valid_email|is_unique[admins.email,admin_id,{admin_id}]',
    ];

    protected $validationMessages = [
        'username' => [
            'required' => 'Username is required',
            'min_length' => 'Username must be at least 3 characters long',
            'max_length' => 'Username cannot exceed 50 characters',
            'is_unique' => 'This username is already taken'
        ],
        'password' => [
            'required' => 'Password is required',
            'min_length' => 'Password must be at least 8 characters long'
        ],
        'email' => [
            'valid_email' => 'Please enter a valid email address',
            'is_unique' => 'This email is already registered'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    // Callbacks
    protected $allowCallbacks = true;
    protected $beforeInsert = ['hashPassword'];
    protected $beforeUpdate = ['hashPassword'];

    /**
     * Hash the password before inserting or updating
     *
     * @param array $data
     * @return array
     */
    protected function hashPassword(array $data)
    {
        if (isset($data['data']['password'])) {
            $data['data']['password_hash'] = password_hash($data['data']['password'], PASSWORD_DEFAULT);
            unset($data['data']['password']);

            // Debug output
            log_message('debug', 'Password hashed: ' . $data['data']['password_hash']);
        }

        return $data;
    }

    /**
     * Authenticate a user
     *
     * @param string $username
     * @param string $password
     * @return array|null The user data if authentication is successful, null otherwise
     */
    public function authenticate($username, $password)
    {
        $user = $this->where('username', $username)->first();

        if (!$user) {
            return null;
        }

        if (!password_verify($password, $user['password_hash'])) {
            return null;
        }

        // Update last login time
        $this->update($user['admin_id'], ['last_login' => date('Y-m-d H:i:s')]);

        return $user;
    }

    /**
     * Change a user's password
     *
     * @param int $adminId
     * @param string $currentPassword
     * @param string $newPassword
     * @return bool True if the password was changed, false otherwise
     */
    public function changePassword($adminId, $currentPassword, $newPassword)
    {
        $user = $this->find($adminId);

        if (!$user) {
            return false;
        }

        if (!password_verify($currentPassword, $user['password_hash'])) {
            return false;
        }

        return $this->update($adminId, ['password' => $newPassword]);
    }
}
