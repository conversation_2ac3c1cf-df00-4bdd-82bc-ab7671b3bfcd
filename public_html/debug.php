<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Information</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .ok{color:green;} .error{color:red;}</style>";

echo "<h2>Basic Info</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current Directory: " . getcwd() . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

echo "<h2>Critical Files</h2>";
$files = [
    'index.php' => 'Main entry point',
    '.env' => 'Environment config',
    'vendor/autoload.php' => 'Composer autoloader',
    'app/Config/App.php' => 'App configuration'
];

foreach ($files as $file => $desc) {
    echo "{$desc} ({$file}): ";
    if (file_exists($file)) {
        echo "<span class='ok'>✓ Found</span>";
        if (is_readable($file)) {
            echo " <span class='ok'>✓ Readable</span>";
        } else {
            echo " <span class='error'>✗ Not Readable</span>";
        }
    } else {
        echo "<span class='error'>✗ Missing</span>";
    }
    echo "<br>";
}

echo "<h2>Test Autoloader</h2>";
try {
    if (file_exists('vendor/autoload.php')) {
        echo "Loading autoloader...<br>";
        require_once 'vendor/autoload.php';
        echo "<span class='ok'>✓ Autoloader loaded successfully</span><br>";
        
        // Test CodeIgniter classes
        if (class_exists('CodeIgniter\\CodeIgniter')) {
            echo "<span class='ok'>✓ CodeIgniter classes available</span><br>";
        } else {
            echo "<span class='error'>✗ CodeIgniter classes not found</span><br>";
        }
    } else {
        echo "<span class='error'>✗ Autoloader file missing</span><br>";
    }
} catch (Exception $e) {
    echo "<span class='error'>✗ Exception: " . $e->getMessage() . "</span><br>";
} catch (Error $e) {
    echo "<span class='error'>✗ Fatal Error: " . $e->getMessage() . "</span><br>";
}

echo "<h2>Environment Test</h2>";
if (file_exists('.env')) {
    echo "Reading .env file...<br>";
    $env_lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
    $env_ok = true;
    foreach ($env_lines as $line) {
        if (strpos($line, 'CI_ENVIRONMENT') === 0) {
            echo "Environment setting: " . htmlspecialchars($line) . "<br>";
        }
        if (strpos($line, 'app.baseURL') === 0) {
            echo "Base URL setting: " . htmlspecialchars($line) . "<br>";
        }
    }
} else {
    echo "<span class='error'>✗ .env file missing</span><br>";
}

echo "<hr>";
echo "<p><strong>Next:</strong> Check your Hostinger error logs for the exact PHP error message</p>";
echo "<p><em>Debug completed: " . date('Y-m-d H:i:s') . "</em></p>";
?>