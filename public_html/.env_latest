#--------------------------------------------------------------------
# Example Environment file
#
# This file can be used as a starting point for your own .env file.
# Copy this file to .env and update the values as needed.
#--------------------------------------------------------------------

# ENVIRONMENT
CI_ENVIRONMENT = development

# APP CONFIGURATION - SINGLE SOURCE OF TRUTH
APP_BASE_URL = https://halqa.mazharulirfan.com/
APP_INDEX_PAGE =
APP_FORCE_HTTPS = true
APP_CSP_ENABLED = false

# Legacy app configuration (automatically set from above)
app.baseURL = https://halqa.mazharulirfan.com/
app.forceGlobalSecureRequests = true
app.sessionDriver = 'CodeIgniter\Session\Handlers\FileHandler'
app.sessionCookieName = 'ci_session'
app.sessionExpiration = 7200
app.sessionSavePath = 'writable/session'
app.sessionMatchIP = false
app.sessionTimeToUpdate = 0
app.sessionRegenerateDestroy = false
app.CSPEnabled = false

# DATABASE CONFIGURATION - SINGLE SOURCE OF TRUTH
# These values are used by all database configurations
# UPDATE THESE WITH YOUR HOSTINGER DATABASE CREDENTIALS
DB_HOSTNAME = localhost
DB_DATABASE = u888771413_halqa
DB_USERNAME = u888771413_halqa
DB_PASSWORD = Muhammed1234#
DB_DRIVER = MySQLi
DB_PREFIX =
DB_PORT = 3306
DB_CHARSET = utf8mb4
DB_COLLATION = utf8mb4_general_ci

# SESSION CONFIGURATION
session.driver = 'CodeIgniter\Session\Handlers\FileHandler'
session.cookieName = 'ci_session'
session.expiration = 7200
session.savePath = 'writable/session'
session.regenerateDestroy = false
session.DBGroup = 'default'
session.matchIP = false
session.timeToUpdate = 0

# SECURITY
security.csrfProtection = 'cookie'
security.tokenRandomize = false
security.tokenName = 'csrf_token_name'
security.headerName = 'X-CSRF-TOKEN'
security.cookieName = 'csrf_cookie_name'
security.expires = 7200
security.regenerate = true
security.redirect = true
security.samesite = 'Lax'

# COOKIE
cookie.prefix = ''
cookie.expires = 0
cookie.path = '/'
cookie.domain = ''
cookie.secure = false
cookie.httponly = false
cookie.samesite = 'Lax'
cookie.raw = false

# ENCRYPTION
encryption.key = hex2bin:2413fb3709b05939f04cf2e92f7d0897fc2596f9ad0b8a9ea855c7bfebaae892
encryption.driver = OpenSSL
encryption.blockSize = 16
encryption.digest = SHA512

# HONEYPOT
honeypot.hidden = 'true'
honeypot.label = 'Fill This Field'
honeypot.name = 'honeypot'
honeypot.template = '<label>{label}</label><input type="text" name="{name}" value=""/>'
honeypot.container = '<div style="display:none">{template}</div>'

# CONTENT SECURITY POLICY
contentsecuritypolicy.reportOnly = false
contentsecuritypolicy.defaultSrc = 'none'
contentsecuritypolicy.scriptSrc = 'self'
contentsecuritypolicy.styleSrc = 'self'
contentsecuritypolicy.imageSrc = 'self'
contentsecuritypolicy.baseURI = null
contentsecuritypolicy.childSrc = null
contentsecuritypolicy.connectSrc = 'self'
contentsecuritypolicy.fontSrc = null
contentsecuritypolicy.formAction = null
contentsecuritypolicy.frameAncestors = null
contentsecuritypolicy.frameSrc = null
contentsecuritypolicy.mediaSrc = null
contentsecuritypolicy.objectSrc = null
contentsecuritypolicy.pluginTypes = null
contentsecuritypolicy.reportURI = null
contentsecuritypolicy.sandbox = false
contentsecuritypolicy.upgradeInsecureRequests = false
contentsecuritypolicy.styleNonceTag = '{csp-style-nonce}'
contentsecuritypolicy.scriptNonceTag = '{csp-script-nonce}'
contentsecuritypolicy.autoNonce = true

# CORS
cors.allowedOrigins = "*"
cors.allowedOriginsPatterns = ""
cors.allowedMethods = "GET,POST,OPTIONS,PUT,DELETE"
cors.allowedHeaders = "*"
cors.exposedHeaders = ""
cors.maxAge = 7200
cors.supportsCredentials = false

# CACHE
cache.handler = 'file'
cache.backupHandler = 'dummy'
cache.ttl = 60
cache.prefix = 'ci_'

# LOGGER
logger.threshold = 4
