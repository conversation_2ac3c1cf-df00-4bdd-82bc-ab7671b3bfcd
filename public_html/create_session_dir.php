<?php
/**
 * Create Session Directory
 */

echo "<h1>📁 Creating Session Directory</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;}</style>";

try {
    // Define FCPATH if not already defined
    if (!defined('FCPATH')) {
        define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
    }
    
    // Load paths
    require FCPATH . 'app/Config/Paths.php';
    $paths = new Config\Paths();
    
    // Get the writable directory
    $writableDir = $paths->writableDirectory;
    $sessionDir = $writableDir . '/session';
    
    echo "<div class='success'>";
    echo "<h2>📍 Directory Information</h2>";
    echo "Writable Directory: $writableDir<br>";
    echo "Session Directory: $sessionDir<br>";
    echo "</div>";
    
    // Check if writable directory exists
    if (!is_dir($writableDir)) {
        echo "<div class='error'>";
        echo "<h2>❌ Writable directory doesn't exist: $writableDir</h2>";
        echo "</div>";
        exit;
    }
    
    echo "<div class='success'>";
    echo "<h2>✅ Writable directory exists</h2>";
    echo "Permissions: " . substr(sprintf('%o', fileperms($writableDir)), -4) . "<br>";
    echo "</div>";
    
    // Create session directory if it doesn't exist
    if (!is_dir($sessionDir)) {
        if (mkdir($sessionDir, 0755, true)) {
            echo "<div class='success'>";
            echo "<h2>✅ Session directory created successfully!</h2>";
            echo "Path: $sessionDir<br>";
            echo "Permissions: 0755<br>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<h2>❌ Failed to create session directory</h2>";
            echo "Path: $sessionDir<br>";
            echo "</div>";
            exit;
        }
    } else {
        echo "<div class='success'>";
        echo "<h2>✅ Session directory already exists</h2>";
        echo "Path: $sessionDir<br>";
        echo "Permissions: " . substr(sprintf('%o', fileperms($sessionDir)), -4) . "<br>";
        echo "</div>";
    }
    
    // Check if it's writable
    if (is_writable($sessionDir)) {
        echo "<div class='success'>";
        echo "<h2>✅ Session directory is writable!</h2>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h2>❌ Session directory is not writable</h2>";
        echo "</div>";
        
        // Try to fix permissions
        if (chmod($sessionDir, 0755)) {
            echo "<div class='success'>";
            echo "<h2>✅ Fixed permissions to 0755</h2>";
            echo "</div>";
        } else {
            echo "<div class='error'>";
            echo "<h2>❌ Could not fix permissions</h2>";
            echo "</div>";
        }
    }
    
    // Test creating a file
    $testFile = $sessionDir . '/test_' . time() . '.txt';
    if (file_put_contents($testFile, 'test')) {
        echo "<div class='success'>";
        echo "<h2>✅ Successfully created test file</h2>";
        echo "File: $testFile<br>";
        echo "</div>";
        
        // Clean up test file
        unlink($testFile);
        echo "<div class='success'>";
        echo "<h2>✅ Test file cleaned up</h2>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h2>❌ Could not create test file</h2>";
        echo "File: $testFile<br>";
        echo "</div>";
    }
    
    echo "<div class='success'>";
    echo "<h2>🎉 Session Directory Setup Complete!</h2>";
    echo "<p><strong>Your site should now work without session errors!</strong></p>";
    echo "<p><a href='../login'>🔐 Try Login Page</a></p>";
    echo "<p><a href='../'>🏠 Visit Main Site</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ Error: " . $e->getMessage() . "</h2>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "</div>";
}

?>
