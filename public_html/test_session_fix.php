<?php
/**
 * Test Session Fix
 */

echo "<h1>🧪 Testing Session Fix</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;} .info{background:#e7f3ff;padding:15px;margin:10px 0;border-left:4px solid #2196F3;}</style>";

try {
    // Define FCPATH if not already defined
    if (!defined('FCPATH')) {
        define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
    }
    
    // Change to correct directory
    if (getcwd() . DIRECTORY_SEPARATOR !== FCPATH) {
        chdir(FCPATH);
    }
    
    // Load paths
    require FCPATH . 'app/Config/Paths.php';
    $paths = new Config\Paths();
    
    // Load autoloader
    require_once $paths->systemDirectory . '/../autoload.php';
    
    echo "<div class='success'>";
    echo "<h2>✅ Autoloader Loaded</h2>";
    echo "</div>";
    
    // Load session configuration
    require_once $paths->appDirectory . '/Config/Session.php';
    $sessionConfig = new Config\Session();
    
    echo "<div class='info'>";
    echo "<h2>📋 Session Configuration</h2>";
    echo "Driver: " . $sessionConfig->driver . "<br>";
    echo "Save Path: " . $sessionConfig->savePath . "<br>";
    echo "Cookie Name: " . $sessionConfig->cookieName . "<br>";
    echo "Expiration: " . $sessionConfig->expiration . " seconds<br>";
    echo "Time to Update: " . $sessionConfig->timeToUpdate . "<br>";
    echo "Regenerate Destroy: " . ($sessionConfig->regenerateDestroy ? 'true' : 'false') . "<br>";
    echo "</div>";
    
    // Check environment variables
    echo "<div class='info'>";
    echo "<h2>🌍 Environment Variables</h2>";
    echo "session.driver: " . env('session.driver', 'not set') . "<br>";
    echo "session.savePath: " . env('session.savePath', 'not set') . "<br>";
    echo "app.sessionDriver: " . env('app.sessionDriver', 'not set') . "<br>";
    echo "app.sessionSavePath: " . env('app.sessionSavePath', 'not set') . "<br>";
    echo "</div>";
    
    // Test database connection
    require_once $paths->appDirectory . '/Config/Database.php';
    $dbConfig = new Config\Database();
    
    $db = new mysqli(
        $dbConfig->default['hostname'],
        $dbConfig->default['username'],
        $dbConfig->default['password'],
        $dbConfig->default['database']
    );
    
    if ($db->connect_error) {
        throw new Exception("Database connection failed: " . $db->connect_error);
    }
    
    echo "<div class='success'>";
    echo "<h2>✅ Database Connected</h2>";
    echo "</div>";
    
    // Check if sessions table exists
    $result = $db->query("SHOW TABLES LIKE 'ci_sessions'");
    
    if ($result->num_rows > 0) {
        echo "<div class='success'>";
        echo "<h2>✅ Sessions Table Exists</h2>";
        
        // Count sessions
        $count = $db->query("SELECT COUNT(*) as count FROM ci_sessions");
        $countRow = $count->fetch_assoc();
        echo "Current sessions: " . $countRow['count'] . "<br>";
        echo "</div>";
    } else {
        echo "<div class='error'>";
        echo "<h2>❌ Sessions Table Missing</h2>";
        echo "<p><a href='setup_database_sessions.php'>🔧 Create Sessions Table</a></p>";
        echo "</div>";
    }
    
    $db->close();
    
    // Try to boot CodeIgniter
    echo "<div class='info'>";
    echo "<h2>🚀 Testing CodeIgniter Boot</h2>";
    echo "</div>";
    
    require_once $paths->systemDirectory . '/Boot.php';
    
    // This should work without session errors now
    $app = CodeIgniter\Boot::bootWeb($paths);
    
    echo "<div class='success'>";
    echo "<h2>🎉 SUCCESS! CodeIgniter Booted Without Session Errors!</h2>";
    echo "<p><strong>Your site should now work perfectly!</strong></p>";
    echo "<p><a href='../login'>🔐 Test Login Page</a></p>";
    echo "<p><a href='../'>🏠 Visit Main Site</a></p>";
    echo "</div>";
    
} catch (Exception $e) {
    echo "<div class='error'>";
    echo "<h2>❌ Error: " . $e->getMessage() . "</h2>";
    echo "File: " . $e->getFile() . "<br>";
    echo "Line: " . $e->getLine() . "<br>";
    echo "</div>";
}

?>

<script>
// Auto-redirect to test the site
setTimeout(function() {
    if (confirm('Session configuration fixed! Test the login page now?')) {
        window.location.href = '../login';
    }
}, 3000);
</script>
