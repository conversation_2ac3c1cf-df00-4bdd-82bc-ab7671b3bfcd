<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Payments for <?= $member['name'] ?></h1>
    <div>
        <a href="<?= site_url('payments/new?member_id=' . $member['member_id']) ?>" class="btn btn-primary">
            <i class="fas fa-plus"></i> Add New Payment
        </a>
        <a href="<?= site_url('members/show/' . $member['member_id']) ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Member
        </a>
    </div>
</div>

<div class="card">
    <div class="card-body">
        <?php if (empty($payments)): ?>
            <div class="alert alert-info">
                No payments found for this member.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Receipt #</th>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Method</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($payments as $payment): ?>
                            <tr>
                                <td><?= $payment['payment_id'] ?></td>
                                <td><?= $payment['receipt_number'] ?></td>
                                <td><?= format_currency_with_decimals($payment['amount']) ?></td>
                                <td><?= date('d M Y', strtotime($payment['payment_date'])) ?></td>
                                <td><?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?></td>
                                <td><?= $payment['notes'] ?? '-' ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('payments/show/' . $payment['payment_id']) ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= site_url('payments/generate-receipt/' . $payment['payment_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Receipt">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                        <a href="<?= site_url('payments/edit/' . $payment['payment_id']) ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $payment['payment_id'] ?>" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>
                                    
                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal<?= $payment['payment_id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $payment['payment_id'] ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel<?= $payment['payment_id'] ?>">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete this payment? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <a href="<?= site_url('payments/delete/' . $payment['payment_id']) ?>" class="btn btn-danger">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
