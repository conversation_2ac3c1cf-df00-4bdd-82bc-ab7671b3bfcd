<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Receipt</h1>
    <div>
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fas fa-print"></i> Print Receipt
        </button>
        <a href="<?= site_url('payments') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Payments
        </a>
    </div>
</div>

<div class="card" id="receipt">
    <div class="card-body">
        <div class="row mb-4">
            <div class="col-md-6">
                <h2 class="text-primary"><?= $organization['name'] ?></h2>
                <p>
                    <?= $organization['address'] ?><br>
                    Phone: <?= $organization['phone'] ?><br>
                    Email: <?= $organization['email'] ?>
                </p>
            </div>
            <div class="col-md-6 text-md-end">
                <h1 class="text-danger">RECEIPT</h1>
                <h4>Receipt #: <?= $payment['receipt_number'] ?></h4>
                <p>Date: <?= date('d M Y', strtotime($payment['payment_date'])) ?></p>
            </div>
        </div>

        <hr class="my-4">

        <div class="row mb-4">
            <div class="col-md-6">
                <h5>Received From:</h5>
                <p>
                    <strong><?= $member['name'] ?></strong><br>
                    <?php if (!empty($member['address'])): ?>
                        <?= $member['address'] ?><br>
                    <?php endif; ?>
                    <?php if (!empty($member['phone'])): ?>
                        Phone: <?= $member['phone'] ?><br>
                    <?php endif; ?>
                    <?php if (!empty($member['email'])): ?>
                        Email: <?= $member['email'] ?>
                    <?php endif; ?>
                </p>
            </div>
            <div class="col-md-6">
                <h5>Payment Details:</h5>
                <p>
                    <strong>Amount:</strong> <?= format_currency_with_decimals($payment['amount']) ?><br>
                    <strong>Payment Method:</strong> <?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?><br>
                    <?php if ($commitment): ?>
                        <strong>For Commitment:</strong> <?= format_currency_with_decimals($commitment['amount']) ?> (<?= ucfirst($commitment['frequency']) ?>)<br>
                        <strong>Commitment Period:</strong>
                        <?= date('d M Y', strtotime($commitment['start_date'])) ?>
                        <?php if ($commitment['end_date']): ?>
                            to <?= date('d M Y', strtotime($commitment['end_date'])) ?>
                        <?php endif; ?>
                        <?php if (!empty($payment['payment_periods'])): ?>
                            <br><strong>Payment For:</strong>
                            <?php
                            $periods = json_decode($payment['payment_periods'], true);
                            if (is_array($periods) && !empty($periods)):
                                echo implode(', ', $periods);
                            endif;
                            ?>
                        <?php endif; ?>
                    <?php endif; ?>
                </p>
            </div>
        </div>

        <div class="row">
            <div class="col-12">
                <h5>Payment Amount:</h5>
                <div class="bg-light p-3 rounded mb-3">
                    <h3 class="text-center mb-0">
                        <?= amount_in_words($payment['amount']) ?>
                    </h3>
                </div>
            </div>
        </div>

        <?php if (!empty($payment['notes'])): ?>
            <div class="row mb-4">
                <div class="col-12">
                    <h5>Notes:</h5>
                    <p><?= nl2br($payment['notes']) ?></p>
                </div>
            </div>
        <?php endif; ?>

        <hr class="my-4">

        <div class="row">
            <div class="col-md-6">
                <h5>Received By:</h5>
                <div class="mt-4 pt-4 border-top">
                    <p class="text-center">Authorized Signature</p>
                </div>
            </div>
            <div class="col-md-6">
                <h5>Received With Thanks:</h5>
                <div class="mt-4 pt-4 border-top">
                    <p class="text-center">Member Signature</p>
                </div>
            </div>
        </div>
    </div>
</div>

<style type="text/css" media="print">
    @media print {
        body * {
            visibility: hidden;
        }
        #receipt, #receipt * {
            visibility: visible;
        }
        #receipt {
            position: absolute;
            left: 0;
            top: 0;
            width: 100%;
            border: none;
        }
        .card {
            border: none !important;
        }
    }
</style>

<?= $this->endSection() ?>
