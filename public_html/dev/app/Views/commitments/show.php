<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Commitment Details</h1>
    <div>
        <a href="<?= site_url('commitments/edit/' . $commitment['commitment_id']) ?>" class="btn btn-warning">
            <i class="fas fa-edit"></i> Edit Commitment
        </a>
        <a href="<?= site_url('members/show/' . $commitment['member_id']) ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Member
        </a>
    </div>
</div>

<div class="row">
    <!-- Commitment Information -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">Commitment Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tr>
                        <th width="30%">ID</th>
                        <td><?= $commitment['commitment_id'] ?></td>
                    </tr>
                    <tr>
                        <th>Member</th>
                        <td>
                            <a href="<?= site_url('members/show/' . $member['member_id']) ?>">
                                <?= $member['name'] ?>
                            </a>
                        </td>
                    </tr>
                    <tr>
                        <th>Amount</th>
                        <td><?= format_currency_with_decimals($commitment['amount']) ?></td>
                    </tr>
                    <tr>
                        <th>Frequency</th>
                        <td><?= ucfirst($commitment['frequency']) ?></td>
                    </tr>
                    <tr>
                        <th>Start Date</th>
                        <td><?= date('d M Y', strtotime($commitment['start_date'])) ?></td>
                    </tr>
                    <tr>
                        <th>End Date</th>
                        <td>
                            <?= $commitment['end_date'] ? date('d M Y', strtotime($commitment['end_date'])) : 'Ongoing' ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Notes</th>
                        <td><?= $commitment['notes'] ?? '-' ?></td>
                    </tr>
                    <tr>
                        <th>Created At</th>
                        <td><?= date('d M Y H:i', strtotime($commitment['created_at'])) ?></td>
                    </tr>
                    <tr>
                        <th>Updated At</th>
                        <td><?= date('d M Y H:i', strtotime($commitment['updated_at'])) ?></td>
                    </tr>
                </table>
            </div>
        </div>
    </div>
    
    <!-- Related Payments -->
    <div class="col-md-6">
        <div class="card">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="card-title mb-0">Related Payments</h5>
                <a href="<?= site_url('payments/new?member_id=' . $commitment['member_id'] . '&commitment_id=' . $commitment['commitment_id']) ?>" class="btn btn-sm btn-light">
                    <i class="fas fa-plus"></i> Add Payment
                </a>
            </div>
            <div class="card-body">
                <?php 
                $relatedPayments = [];
                if (isset($payments)) {
                    foreach ($payments as $payment) {
                        if ($payment['commitment_id'] == $commitment['commitment_id']) {
                            $relatedPayments[] = $payment;
                        }
                    }
                }
                ?>
                
                <?php if (empty($relatedPayments)): ?>
                    <p class="text-muted">No payments linked to this commitment.</p>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-striped table-hover">
                            <thead>
                                <tr>
                                    <th>ID</th>
                                    <th>Receipt #</th>
                                    <th>Amount</th>
                                    <th>Date</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($relatedPayments as $payment): ?>
                                    <tr>
                                        <td><?= $payment['payment_id'] ?></td>
                                        <td><?= $payment['receipt_number'] ?></td>
                                        <td><?= format_currency_with_decimals($payment['amount']) ?></td>
                                        <td><?= date('d M Y', strtotime($payment['payment_date'])) ?></td>
                                        <td>
                                            <div class="btn-group" role="group">
                                                <a href="<?= site_url('payments/show/' . $payment['payment_id']) ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View">
                                                    <i class="fas fa-eye"></i>
                                                </a>
                                                <a href="<?= site_url('payments/generate-receipt/' . $payment['payment_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Receipt">
                                                    <i class="fas fa-file-invoice"></i>
                                                </a>
                                            </div>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>
