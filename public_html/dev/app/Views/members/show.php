<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Member Details: <?= $member['name'] ?></h1>
    <div>
        <a href="<?= site_url('members/edit/' . $member['member_id']) ?>" class="btn btn-warning">
            <i class="fas fa-edit"></i> Edit Member
        </a>
        <a href="<?= site_url('members') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Members
        </a>
    </div>
</div>

<div class="row">
    <!-- Member Information -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="card-title mb-0">Member Information</h5>
            </div>
            <div class="card-body">
                <table class="table table-striped">
                    <tr>
                        <th width="30%">ID</th>
                        <td><?= $member['member_id'] ?></td>
                    </tr>
                    <tr>
                        <th>Name</th>
                        <td><?= $member['name'] ?></td>
                    </tr>
                    <tr>
                        <th>Email</th>
                        <td><?= $member['email'] ?? '-' ?></td>
                    </tr>
                    <tr>
                        <th>Post Office</th>
                        <td><?= $member['post_office'] ?? '-' ?></td>
                    </tr>
                    <tr>
                        <th>Old Reference</th>
                        <td><?= $member['old_reference'] ?? '-' ?></td>
                    </tr>
                    <tr>
                        <th>Phone</th>
                        <td>
                            <?php if (!empty($member['phone'])): ?>
                                <div class="d-flex align-items-center">
                                    <a href="tel:<?= $member['phone'] ?>" class="text-decoration-none" data-bs-toggle="tooltip" title="Call">
                                        <i class="fas fa-phone-alt text-success fa-lg"></i>
                                    </a>
                                    <span class="ms-2"><?= $member['phone'] ?></span>
                                </div>
                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>WhatsApp</th>
                        <td>
                            <?php if (!empty($member['whatsapp_number'])): ?>
                                <div class="d-flex align-items-center">
                                    <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $member['whatsapp_number']) ?>" target="_blank" class="text-decoration-none" data-bs-toggle="tooltip" title="WhatsApp">
                                        <i class="fab fa-whatsapp text-success fa-lg"></i>
                                    </a>
                                    <span class="ms-2"><?= $member['whatsapp_number'] ?></span>
                                </div>
                            <?php else: ?>
                                -
                            <?php endif; ?>
                        </td>
                    </tr>
                    <tr>
                        <th>Address</th>
                        <td><?= $member['address'] ?? '-' ?></td>
                    </tr>
                    <tr>
                        <th>Join Date</th>
                        <td><?= date('d M Y', strtotime($member['join_date'])) ?></td>
                    </tr>
                    <tr>
                        <th>Status</th>
                        <td>
                            <?php if ($member['status'] == 'active'): ?>
                                <span class="badge bg-success">Active</span>
                            <?php else: ?>
                                <span class="badge bg-danger">Inactive</span>
                            <?php endif; ?>
                        </td>
                    </tr>
                </table>
            </div>
        </div>
    </div>

    <!-- Financial Summary -->
    <div class="col-md-6">
        <div class="card mb-4">
            <div class="card-header bg-success text-white">
                <h5 class="card-title mb-0">Financial Summary</h5>
            </div>
            <div class="card-body">
                <?php if (isset($member['summary']) && $member['summary']): ?>
                    <table class="table table-striped">
                        <tr>
                            <th width="40%">Total Committed</th>
                            <td><?= format_currency($member['summary']['total_committed']) ?></td>
                        </tr>
                        <tr>
                            <th>Total Paid</th>
                            <td><?= format_currency($member['summary']['total_paid']) ?></td>
                        </tr>
                        <tr>
                            <th>Balance</th>
                            <td class="<?= $member['summary']['balance'] > 0 ? 'text-danger fw-bold' : 'text-success' ?>">
                                <?= format_currency($member['summary']['balance']) ?>
                            </td>
                        </tr>
                        <tr>
                            <th>Last Payment</th>
                            <td>
                                <?php if ($member['summary']['last_payment_date']): ?>
                                    <?= date('d M Y', strtotime($member['summary']['last_payment_date'])) ?>
                                    <br>
                                    <small class="text-muted">
                                        Amount: <?= format_currency($member['summary']['last_payment_amount']) ?>
                                    </small>
                                <?php else: ?>
                                    <span class="text-muted">No payments yet</span>
                                <?php endif; ?>
                            </td>
                        </tr>
                    </table>
                <?php else: ?>
                    <p class="text-muted">No financial summary available.</p>
                <?php endif; ?>

                <!-- Buttons removed as requested -->
            </div>
        </div>
    </div>
</div>

<!-- Commitments -->
<div class="card mb-4">
    <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Commitments</h5>
        <a href="<?= site_url('commitments/new?member_id=' . $member['member_id']) ?>" class="btn btn-sm btn-light">
            <i class="fas fa-plus"></i> Add Commitment
        </a>
    </div>
    <div class="card-body">
        <?php if (isset($member['commitments']) && !empty($member['commitments'])): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Amount</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Total Due</th>
                            <th>Total Paid</th>
                            <th>Balance</th>
                            <th>Notes</th>
                            <th>Frequency</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($member['commitments'] as $commitment): ?>
                            <tr>
                                <td><?= $commitment['commitment_id'] ?></td>
                                <td><?= format_currency($commitment['amount']) ?></td>
                                <td><?= date('d M Y', strtotime($commitment['start_date'])) ?></td>
                                <td>
                                    <?= $commitment['end_date'] ? date('d M Y', strtotime($commitment['end_date'])) : 'Ongoing' ?>
                                </td>
                                <td>
                                    <?php if ($commitment['frequency'] == 'one-time'): ?>
                                        <span data-bs-toggle="tooltip" title="One-time payment">
                                            <?= format_currency($commitment['amount']) ?> × 1 = <?= format_currency($commitment['total_due']) ?>
                                        </span>
                                    <?php else: ?>
                                        <span data-bs-toggle="tooltip" title="<?= $commitment['periods'] ?> <?= ucfirst($commitment['frequency']) ?> period(s)">
                                            <?= format_currency($commitment['amount']) ?> × <?= $commitment['periods'] ?> = <?= format_currency($commitment['total_due']) ?>
                                        </span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($commitment['total_paid'] > 0): ?>
                                        <?php if ($commitment['frequency'] == 'one-time'): ?>
                                            <span data-bs-toggle="tooltip" title="One-time payment">
                                                <?= format_currency($commitment['amount']) ?> × 1 = <?= format_currency($commitment['total_paid']) ?>
                                            </span>
                                        <?php else: ?>
                                            <span data-bs-toggle="tooltip" title="<?= $commitment['paid_periods'] ?> <?= ucfirst($commitment['frequency']) ?> period(s) paid">
                                                <?= format_currency($commitment['amount']) ?> × <?= $commitment['paid_periods'] ?> = <?= format_currency($commitment['total_paid']) ?>
                                            </span>
                                        <?php endif; ?>
                                    <?php else: ?>
                                        0
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($commitment['balance'] > 0): ?>
                                        <div class="d-flex align-items-center">
                                            <?php if ($commitment['frequency'] == 'one-time'): ?>
                                                <span class="badge bg-danger me-2" data-bs-toggle="tooltip" title="One-time payment remaining">
                                                    <?= format_currency($commitment['amount']) ?> × 1 = <?= format_currency($commitment['balance']) ?>
                                                </span>
                                            <?php else: ?>
                                                <span class="badge bg-danger me-2" data-bs-toggle="tooltip" title="<?= $commitment['remaining_periods'] ?> <?= ucfirst($commitment['frequency']) ?> period(s) remaining">
                                                    <?= format_currency($commitment['amount']) ?> × <?= $commitment['remaining_periods'] ?> = <?= format_currency($commitment['balance']) ?>
                                                </span>
                                            <?php endif; ?>
                                            <a href="<?= site_url('payments/new?member_id=' . $member['member_id'] . '&commitment_id=' . $commitment['commitment_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Pay">
                                                <i class="fas fa-money-bill-wave"></i>
                                            </a>
                                        </div>
                                    <?php else: ?>
                                        <span class="badge bg-success">Paid</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= $commitment['notes'] ?? '-' ?></td>
                                <td><?= ucfirst($commitment['frequency']) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('commitments/edit/' . $commitment['commitment_id']) ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $commitment['commitment_id'] ?>" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal<?= $commitment['commitment_id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $commitment['commitment_id'] ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel<?= $commitment['commitment_id'] ?>">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete this commitment? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <a href="<?= site_url('commitments/delete/' . $commitment['commitment_id']) ?>" class="btn btn-danger">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-muted">No commitments found for this member.</p>
        <?php endif; ?>
    </div>
</div>

<!-- Payments -->
<div class="card">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">Payments</h5>
        <a href="<?= site_url('payments/new?member_id=' . $member['member_id']) ?>" class="btn btn-sm btn-light">
            <i class="fas fa-plus"></i> Add Payment
        </a>
    </div>
    <div class="card-body">
        <?php if (isset($member['payments']) && !empty($member['payments'])): ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Receipt Book #</th>
                            <th>Receipt #</th>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Method</th>
                            <th>Notes</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($member['payments'] as $payment): ?>
                            <tr>
                                <td><?= $payment['payment_id'] ?></td>
                                <td><?= $payment['receipt_book_number'] ?? '-' ?></td>
                                <td><?= $payment['receipt_number'] ?></td>
                                <td><?= format_currency($payment['amount']) ?></td>
                                <td><?= date('d M Y', strtotime($payment['payment_date'])) ?></td>
                                <td><?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?></td>
                                <td><?= $payment['notes'] ?? '-' ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('payments/generate-receipt/' . $payment['payment_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Receipt">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                        <a href="<?= site_url('payments/edit/' . $payment['payment_id']) ?>" class="btn btn-sm btn-warning" data-bs-toggle="tooltip" title="Edit">
                                            <i class="fas fa-edit"></i>
                                        </a>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deletePaymentModal<?= $payment['payment_id'] ?>" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deletePaymentModal<?= $payment['payment_id'] ?>" tabindex="-1" aria-labelledby="deletePaymentModalLabel<?= $payment['payment_id'] ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deletePaymentModalLabel<?= $payment['payment_id'] ?>">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete this payment? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <a href="<?= site_url('payments/delete/' . $payment['payment_id']) ?>" class="btn btn-danger">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        <?php else: ?>
            <p class="text-muted">No payments found for this member.</p>
        <?php endif; ?>
    </div>
</div>

<?= $this->endSection() ?>
