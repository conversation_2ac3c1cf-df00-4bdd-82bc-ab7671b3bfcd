<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit Member: <?= $member['name'] ?></h1>
    <a href="<?= site_url('members/show/' . $member['member_id']) ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Member
    </a>
</div>

<div class="card">
    <div class="card-body">
        <form action="<?= site_url('members/update/' . $member['member_id']) ?>" method="post">
            <?= csrf_field() ?>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="name" class="form-label">Name <span class="text-danger">*</span></label>
                    <input type="text" class="form-control <?= session('errors.name') ? 'is-invalid' : '' ?>" id="name" name="name" value="<?= old('name', $member['name']) ?>" required>
                    <?php if (session('errors.name')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.name') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <label for="post_office" class="form-label">Post Office</label>
                    <input type="text" class="form-control <?= session('errors.post_office') ? 'is-invalid' : '' ?>" id="post_office" name="post_office" value="<?= old('post_office', $member['post_office'] ?? '') ?>">
                    <?php if (session('errors.post_office')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.post_office') ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="old_reference" class="form-label">Old Reference</label>
                    <input type="text" class="form-control <?= session('errors.old_reference') ? 'is-invalid' : '' ?>" id="old_reference" name="old_reference" value="<?= old('old_reference', $member['old_reference'] ?? '') ?>">
                    <?php if (session('errors.old_reference')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.old_reference') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <label for="phone" class="form-label">Phone</label>
                    <input type="text" class="form-control <?= session('errors.phone') ? 'is-invalid' : '' ?>" id="phone" name="phone" value="<?= old('phone', $member['phone']) ?>">
                    <?php if (session('errors.phone')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.phone') ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="mb-3">
                <label for="address" class="form-label">Address</label>
                <textarea class="form-control <?= session('errors.address') ? 'is-invalid' : '' ?>" id="address" name="address" rows="3"><?= old('address', $member['address']) ?></textarea>
                <?php if (session('errors.address')): ?>
                    <div class="invalid-feedback">
                        <?= session('errors.address') ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="whatsapp_number" class="form-label">WhatsApp Number</label>
                    <div class="input-group">
                        <input type="text" class="form-control <?= session('errors.whatsapp_number') ? 'is-invalid' : '' ?>" id="whatsapp_number" name="whatsapp_number" value="<?= old('whatsapp_number', $member['whatsapp_number'] ?? '') ?>">
                        <button class="btn btn-outline-secondary" type="button" id="copyPhoneBtn">Same as phone</button>
                    </div>
                    <?php if (session('errors.whatsapp_number')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.whatsapp_number') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <label for="email" class="form-label">Email</label>
                    <input type="email" class="form-control <?= session('errors.email') ? 'is-invalid' : '' ?>" id="email" name="email" value="<?= old('email', $member['email']) ?>">
                    <?php if (session('errors.email')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.email') ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="join_date" class="form-label">Join Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control <?= session('errors.join_date') ? 'is-invalid' : '' ?>" id="join_date" name="join_date" value="<?= old('join_date', $member['join_date']) ?>" required>
                    <?php if (session('errors.join_date')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.join_date') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <!-- Empty column for alignment -->
                </div>
            </div>

            <div class="mb-3">
                <label for="status" class="form-label">Status <span class="text-danger">*</span></label>
                <select class="form-select <?= session('errors.status') ? 'is-invalid' : '' ?>" id="status" name="status" required>
                    <option value="active" <?= old('status', $member['status']) == 'active' ? 'selected' : '' ?>>Active</option>
                    <option value="inactive" <?= old('status', $member['status']) == 'inactive' ? 'selected' : '' ?>>Inactive</option>
                </select>
                <?php if (session('errors.status')): ?>
                    <div class="invalid-feedback">
                        <?= session('errors.status') ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="reset" class="btn btn-secondary">Reset</button>
                <button type="submit" class="btn btn-primary">Update Member</button>
            </div>
        </form>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    document.getElementById('copyPhoneBtn').addEventListener('click', function() {
        const phoneInput = document.getElementById('phone');
        const whatsappInput = document.getElementById('whatsapp_number');

        if (phoneInput && whatsappInput) {
            whatsappInput.value = phoneInput.value;
        }
    });
});
</script>

<?= $this->endSection() ?>
