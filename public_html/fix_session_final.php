<?php
/**
 * Final Session Fix
 */

echo "<h1>🔧 Final Session Fix</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;} .info{background:#e7f3ff;padding:15px;margin:10px 0;border-left:4px solid #2196F3;}</style>";

// Method 1: Create session directory manually
echo "<div class='info'>";
echo "<h2>Method 1: Creating Session Directory</h2>";

$sessionDirs = [
    'writable/session',
    './writable/session',
    '../writable/session'
];

$created = false;
foreach ($sessionDirs as $dir) {
    if (!is_dir($dir)) {
        if (mkdir($dir, 0755, true)) {
            echo "✅ Created directory: $dir<br>";
            $created = true;
            break;
        } else {
            echo "❌ Failed to create: $dir<br>";
        }
    } else {
        echo "✅ Directory exists: $dir<br>";
        $created = true;
        break;
    }
}

if ($created) {
    echo "<p><strong>Session directory created! Try your site now.</strong></p>";
} else {
    echo "<p><strong>Could not create session directory. Trying alternative method...</strong></p>";
}
echo "</div>";

// Method 2: Disable sessions temporarily
echo "<div class='info'>";
echo "<h2>Method 2: Alternative Session Configuration</h2>";
echo "<p>If the directory creation doesn't work, here's an alternative Session.php configuration:</p>";
echo "<textarea style='width:100%;height:200px;'>";
echo "<?php

namespace Config;

use CodeIgniter\Config\BaseConfig;
use CodeIgniter\Session\Handlers\BaseHandler;
use CodeIgniter\Session\Handlers\FileHandler;

class Session extends BaseConfig
{
    public string \$driver = FileHandler::class;
    public string \$cookieName = 'ci_session';
    public int \$expiration = 7200;
    
    // Use relative path that should work on most servers
    public string \$savePath = './writable/session';
    
    public bool \$matchIP = false;
    public int \$timeToUpdate = 0;
    public bool \$regenerateDestroy = false;
    public ?string \$DBGroup = null;
    public int \$lockRetryInterval = 100_000;
    public int \$lockMaxRetries = 300;
}";
echo "</textarea>";
echo "</div>";

// Method 3: Database sessions
echo "<div class='info'>";
echo "<h2>Method 3: Use Database Sessions (Recommended)</h2>";
echo "<p>For production, database sessions are more reliable. Here's the configuration:</p>";
echo "<textarea style='width:100%;height:150px;'>";
echo "// In app/Config/Session.php, change:
public string \$driver = 'CodeIgniter\\Session\\Handlers\\DatabaseHandler';
public string \$savePath = 'ci_sessions'; // table name

// Then create the sessions table:
CREATE TABLE ci_sessions (
    id varchar(128) NOT NULL,
    ip_address varchar(45) NOT NULL,
    timestamp timestamp DEFAULT CURRENT_TIMESTAMP NOT NULL,
    data blob NOT NULL,
    KEY ci_sessions_timestamp (timestamp)
);";
echo "</textarea>";
echo "</div>";

// Test current setup
echo "<div class='info'>";
echo "<h2>Current Directory Status</h2>";
$dirs = ['writable', 'writable/session', './writable', './writable/session'];
foreach ($dirs as $dir) {
    $exists = is_dir($dir) ? '✅' : '❌';
    $writable = is_dir($dir) && is_writable($dir) ? '(writable)' : '(not writable)';
    echo "$exists $dir $writable<br>";
}
echo "</div>";

echo "<div class='success'>";
echo "<h2>🎯 Quick Actions</h2>";
echo "<p><a href='../login'>🔐 Test Login Page</a></p>";
echo "<p><a href='../'>🏠 Visit Main Site</a></p>";
echo "</div>";

?>

<script>
// Auto-refresh to test if session directory was created
setTimeout(function() {
    if (confirm('Session directory created. Refresh the page to test?')) {
        window.location.reload();
    }
}, 3000);
</script>
