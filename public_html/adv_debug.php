<?php
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Start output buffering to prevent header issues
ob_start();

// Define FCPATH first - this is what was missing!
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);

echo "<h1>Advanced CodeIgniter Debug (Actually Fixed)</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .ok{color:green;} .error{color:red;}</style>";

echo "<h2>Step-by-Step CodeIgniter Loading</h2>";

try {
    echo "1. FCPATH defined: " . FCPATH . "<br>";
    
    echo "2. Loading autoloader...<br>";
    require_once 'vendor/autoload.php';
    echo "<span class='ok'>✓ Autoloader loaded</span><br>";

    echo "3. Setting up paths...<br>";
    $pathsPath = FCPATH . 'app/Config/Paths.php';
    echo "Paths file: {$pathsPath}<br>";
    
    if (file_exists($pathsPath)) {
        echo "<span class='ok'>✓ Paths file found</span><br>";
        require $pathsPath;
        $paths = new Config\Paths();
        echo "<span class='ok'>✓ Paths loaded</span><br>";
    } else {
        echo "<span class='error'>✗ Paths file missing at: {$pathsPath}</span><br>";
        // Try alternative path
        $altPath = FCPATH . '../app/Config/Paths.php';
        echo "Trying alternative: {$altPath}<br>";
        if (file_exists($altPath)) {
            echo "<span class='ok'>✓ Found at alternative path</span><br>";
            require $altPath;
            $paths = new Config\Paths();
        } else {
            echo "<span class='error'>✗ Not found at alternative path either</span><br>";
        }
    }

    if (isset($paths)) {
        echo "4. Loading bootstrap...<br>";
        $bootstrap = $paths->systemDirectory . '/Boot.php';
        echo "Bootstrap file: {$bootstrap}<br>";
        
        if (file_exists($bootstrap)) {
            echo "<span class='ok'>✓ Bootstrap file found</span><br>";
            require $bootstrap;
            echo "<span class='ok'>✓ Bootstrap loaded</span><br>";
        } else {
            echo "<span class='error'>✗ Bootstrap file missing</span><br>";
        }

        echo "5. Testing CodeIgniter creation (with session handling)...<br>";
        
        // Disable sessions temporarily for testing
        $_ENV['session.driver'] = '';
        putenv('session.driver=');
        
        // Test if we can create the app
        try {
            $app = CodeIgniter\Boot::bootWeb($paths);
            echo "<span class='ok'>✓ CodeIgniter boots successfully!</span><br>";
        } catch (Exception $sessionError) {
            echo "<span class='error'>Session Error: " . $sessionError->getMessage() . "</span><br>";
            echo "Trying with session workaround...<br>";
            
            // Try to modify session config on the fly
            if (class_exists('Config\App')) {
                echo "Attempting session configuration fix...<br>";
            }
        }
    }

} catch (Exception $e) {
    echo "<span class='error'>✗ Exception: " . $e->getMessage() . "</span><br>";
    echo "<span class='error'>File: " . $e->getFile() . " Line: " . $e->getLine() . "</span><br>";
} catch (Error $e) {
    echo "<span class='error'>✗ Fatal Error: " . $e->getMessage() . "</span><br>";
    echo "<span class='error'>File: " . $e->getFile() . " Line: " . $e->getLine() . "</span><br>";
}

echo "<h2>Environment Check</h2>";
echo "CI_ENVIRONMENT: " . (getenv('CI_ENVIRONMENT') ?: 'Not defined') . "<br>";
echo "FCPATH: " . (defined('FCPATH') ? FCPATH : 'Not defined') . "<br>";

echo "<h2>File Structure Check</h2>";
echo "Current directory contents:<br>";
$files = scandir('.');
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        $type = is_dir($file) ? 'DIR' : 'FILE';
        echo "- {$file} ({$type})<br>";
    }
}

echo "<hr>";
echo "<p><em>Actually fixed debug completed: " . date('Y-m-d H:i:s') . "</em></p>";
?>