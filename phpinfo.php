<?php
/**
 * PHP Version and Configuration Check
 */

echo "<h1>PHP Version Check</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .info{background:#e7f3ff;padding:15px;margin:10px 0;border-left:4px solid #2196F3;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;}</style>";

echo "<div class='info'>";
echo "<h2>Current PHP Version</h2>";
echo "<strong>PHP Version: " . phpversion() . "</strong><br>";
echo "PHP SAPI: " . php_sapi_name() . "<br>";
echo "Server Software: " . $_SERVER['SERVER_SOFTWARE'] . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "</div>";

// Check if version meets CodeIgniter requirements
$php_version = phpversion();
$version_parts = explode('.', $php_version);
$major = (int)$version_parts[0];
$minor = (int)$version_parts[1];

echo "<div class='" . (($major > 8 || ($major == 8 && $minor >= 1)) ? 'success' : 'error') . "'>";
echo "<h2>CodeIgniter 4 Compatibility</h2>";
if ($major > 8 || ($major == 8 && $minor >= 1)) {
    echo "✅ <strong>PHP version is compatible with CodeIgniter 4</strong><br>";
    echo "Required: PHP >= 8.1.0<br>";
    echo "Current: PHP $php_version<br>";
} else {
    echo "❌ <strong>PHP version is NOT compatible with CodeIgniter 4</strong><br>";
    echo "Required: PHP >= 8.1.0<br>";
    echo "Current: PHP $php_version<br>";
    echo "<br><strong>Action needed:</strong> Update PHP version in Hostinger control panel";
}
echo "</div>";

// Test autoload
echo "<div class='info'>";
echo "<h2>Composer Autoload Test</h2>";
if (file_exists('vendor/autoload.php')) {
    echo "✅ vendor/autoload.php exists<br>";
    
    try {
        ob_start();
        require_once 'vendor/autoload.php';
        $output = ob_get_clean();
        
        if (empty($output)) {
            echo "✅ Autoload included successfully (no errors)<br>";
            
            // Test CodeIgniter class
            if (class_exists('CodeIgniter\\Boot')) {
                echo "✅ CodeIgniter\\Boot class is available<br>";
            } else {
                echo "❌ CodeIgniter\\Boot class not found<br>";
            }
        } else {
            echo "⚠️ Autoload included with output:<br>";
            echo "<pre>" . htmlspecialchars($output) . "</pre>";
        }
        
    } catch (Exception $e) {
        echo "❌ Autoload error: " . htmlspecialchars($e->getMessage()) . "<br>";
    } catch (Error $e) {
        echo "❌ Fatal error: " . htmlspecialchars($e->getMessage()) . "<br>";
    }
} else {
    echo "❌ vendor/autoload.php not found<br>";
}
echo "</div>";

// Show some key PHP settings
echo "<div class='info'>";
echo "<h2>Key PHP Settings</h2>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "Max Execution Time: " . ini_get('max_execution_time') . " seconds<br>";
echo "Upload Max Filesize: " . ini_get('upload_max_filesize') . "<br>";
echo "Post Max Size: " . ini_get('post_max_size') . "<br>";
echo "Display Errors: " . (ini_get('display_errors') ? 'On' : 'Off') . "<br>";
echo "Error Reporting: " . error_reporting() . "<br>";
echo "</div>";

// Show loaded extensions
echo "<div class='info'>";
echo "<h2>Important PHP Extensions</h2>";
$required_extensions = ['mbstring', 'intl', 'json', 'mysqlnd', 'curl', 'gd'];
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    $status = $loaded ? '✅' : '❌';
    echo "$status $ext<br>";
}
echo "</div>";

echo "<div class='info'>";
echo "<h2>Quick Actions</h2>";
echo "<p><a href='debug_check.php'>🔍 Run Debug Check</a></p>";
echo "<p><a href='../'>🏠 Go to Main Site</a></p>";
echo "<p><strong>Note:</strong> Delete this file after testing for security!</p>";
echo "</div>";

// Optional: Show full phpinfo (commented out for security)
// echo "<hr>";
// phpinfo();
?>
