<?php
/**
 * CodeIgniter Boot Test - Step by Step
 */

echo "<h1>CodeIgniter Boot Process Test</h1>";
echo "<style>body{font-family:Aria<PERSON>;margin:20px;} .info{background:#e7f3ff;padding:15px;margin:10px 0;border-left:4px solid #2196F3;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;}</style>";

// Step 1: Check FCPATH
echo "<div class='info'>";
echo "<h2>Step 1: FCPATH Definition</h2>";
if (!defined('FCPATH')) {
    define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
    echo "✅ FCPATH defined as: " . FCPATH . "<br>";
} else {
    echo "✅ FCPATH already defined as: " . FCPATH . "<br>";
}
echo "</div>";

// Step 2: Check Paths.php
echo "<div class='info'>";
echo "<h2>Step 2: Loading Paths Configuration</h2>";
$paths_file = FCPATH . 'app/Config/Paths.php';
if (file_exists($paths_file)) {
    echo "✅ Paths.php exists<br>";
    try {
        require $paths_file;
        $paths = new Config\Paths();
        echo "✅ Paths object created successfully<br>";
        echo "System Directory: " . $paths->systemDirectory . "<br>";
        echo "App Directory: " . $paths->appDirectory . "<br>";
        echo "Writable Directory: " . $paths->writableDirectory . "<br>";
        
        // Check if system directory actually exists
        if (file_exists($paths->systemDirectory)) {
            echo "✅ System directory exists at configured path<br>";
        } else {
            echo "❌ System directory MISSING at: " . $paths->systemDirectory . "<br>";
        }
        
    } catch (Exception $e) {
        echo "❌ Error loading Paths: " . $e->getMessage() . "<br>";
    }
} else {
    echo "❌ Paths.php missing at: $paths_file<br>";
}
echo "</div>";

// Step 3: Check System Boot.php
echo "<div class='info'>";
echo "<h2>Step 3: System Boot.php Check</h2>";
if (isset($paths)) {
    $boot_file = $paths->systemDirectory . '/Boot.php';
    if (file_exists($boot_file)) {
        echo "✅ Boot.php exists at: $boot_file<br>";
        echo "File size: " . filesize($boot_file) . " bytes<br>";
        
        // Try to include it
        try {
            require $boot_file;
            echo "✅ Boot.php included successfully<br>";
            
            // Check if Boot class exists
            if (class_exists('CodeIgniter\\Boot')) {
                echo "✅ CodeIgniter\\Boot class is available<br>";
            } else {
                echo "❌ CodeIgniter\\Boot class not found after including Boot.php<br>";
            }
            
        } catch (Exception $e) {
            echo "❌ Error including Boot.php: " . $e->getMessage() . "<br>";
        } catch (Error $e) {
            echo "❌ Fatal error including Boot.php: " . $e->getMessage() . "<br>";
        }
        
    } else {
        echo "❌ Boot.php missing at: $boot_file<br>";
    }
} else {
    echo "❌ Cannot check Boot.php - Paths not loaded<br>";
}
echo "</div>";

// Step 4: Try the actual boot process
echo "<div class='info'>";
echo "<h2>Step 4: Attempt Boot Process</h2>";
if (isset($paths) && class_exists('CodeIgniter\\Boot')) {
    try {
        echo "Attempting to boot CodeIgniter...<br>";
        // This is what index.php does
        ob_start();
        $result = CodeIgniter\Boot::bootWeb($paths);
        $output = ob_get_clean();
        echo "✅ Boot process completed<br>";
        echo "Output length: " . strlen($output) . " characters<br>";
        if (strlen($output) > 0) {
            echo "First 200 chars of output: " . htmlspecialchars(substr($output, 0, 200)) . "<br>";
        }
    } catch (Exception $e) {
        echo "❌ Boot process failed: " . $e->getMessage() . "<br>";
        echo "Error file: " . $e->getFile() . " line " . $e->getLine() . "<br>";
    } catch (Error $e) {
        echo "❌ Fatal error during boot: " . $e->getMessage() . "<br>";
        echo "Error file: " . $e->getFile() . " line " . $e->getLine() . "<br>";
    }
} else {
    echo "❌ Cannot attempt boot - prerequisites not met<br>";
}
echo "</div>";

// Step 5: Environment check
echo "<div class='info'>";
echo "<h2>Step 5: Environment Information</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Error Reporting: " . error_reporting() . "<br>";
echo "Display Errors: " . ini_get('display_errors') . "<br>";
echo "Log Errors: " . ini_get('log_errors') . "<br>";
echo "Memory Limit: " . ini_get('memory_limit') . "<br>";
echo "</div>";

?>
