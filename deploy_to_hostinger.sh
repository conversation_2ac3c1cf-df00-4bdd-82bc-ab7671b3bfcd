#!/bin/bash
echo "🚀 Preparing Halqa for Hostinger..."
DEPLOY_DIR="hostinger_deploy"
rm -rf "$DEPLOY_DIR"
mkdir -p "$DEPLOY_DIR"
cp -r public/* "$DEPLOY_DIR/"
cp -r app "$DEPLOY_DIR/"
cp -r writable "$DEPLOY_DIR/"
[ -d "vendor" ] && cp -r vendor "$DEPLOY_DIR/"
[ -f ".env.production" ] && cp .env.production "$DEPLOY_DIR/.env"
[ -f "spark" ] && cp spark "$DEPLOY_DIR/"
[ -f "preload.php" ] && cp preload.php "$DEPLOY_DIR/"
chmod -R 777 "$DEPLOY_DIR/writable"
echo "✅ Files ready in: $DEPLOY_DIR/"
