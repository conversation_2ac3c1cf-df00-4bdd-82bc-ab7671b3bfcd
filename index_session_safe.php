<?php

use CodeIgniter\Boot;
use Config\Paths;

// Prevent any output before headers
ob_start();

// Disable session auto-start
ini_set('session.auto_start', 0);

// Define FCPATH
define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);

// Change to correct directory
if (getcwd() . DIRECTORY_SEPARATOR !== FCPATH) {
    chdir(FCPATH);
}

// Load paths
require FCPATH . 'app/Config/Paths.php';
$paths = new Paths();

// Load framework
require $paths->systemDirectory . '/Boot.php';

// Boot the application
exit(Boot::bootWeb($paths));
