<?php
/**
 * Detailed Debug Check for CodeIgniter 4 Path Issues
 */

echo "<h1>Detailed CodeIgniter 4 Path Debug</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .info{background:#e7f3ff;padding:15px;margin:10px 0;border-left:4px solid #2196F3;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;}</style>";

echo "<div class='info'>";
echo "<h2>Current Working Directory Analysis</h2>";
echo "Current Directory: " . getcwd() . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
echo "Script Filename: " . $_SERVER['SCRIPT_FILENAME'] . "<br>";
echo "</div>";

echo "<div class='info'>";
echo "<h2>Directory Listing - Root Level</h2>";
$files = scandir('.');
foreach ($files as $file) {
    if ($file != '.' && $file != '..') {
        $type = is_dir($file) ? '[DIR]' : '[FILE]';
        echo "$type $file<br>";
    }
}
echo "</div>";

echo "<div class='info'>";
echo "<h2>System Directory Investigation</h2>";

// Check different possible locations for system directory
$possible_system_paths = [
    'system',
    './system',
    'vendor/codeigniter4/framework/system',
    './vendor/codeigniter4/framework/system',
    '../system',
    'app/../system'
];

foreach ($possible_system_paths as $path) {
    $exists = file_exists($path);
    $status = $exists ? "✅ EXISTS" : "❌ MISSING";
    echo "$path: $status<br>";
    
    if ($exists && is_dir($path)) {
        // Check for Boot.php specifically
        $boot_path = $path . '/Boot.php';
        $boot_exists = file_exists($boot_path);
        $boot_status = $boot_exists ? "✅ Boot.php EXISTS" : "❌ Boot.php MISSING";
        echo "&nbsp;&nbsp;&nbsp;$boot_status<br>";
        
        if ($boot_exists) {
            echo "&nbsp;&nbsp;&nbsp;Boot.php size: " . filesize($boot_path) . " bytes<br>";
        }
    }
}
echo "</div>";

echo "<div class='info'>";
echo "<h2>Vendor Directory Check</h2>";
if (file_exists('vendor')) {
    echo "✅ vendor directory exists<br>";
    if (file_exists('vendor/autoload.php')) {
        echo "✅ vendor/autoload.php exists<br>";
    } else {
        echo "❌ vendor/autoload.php missing<br>";
    }
    
    if (file_exists('vendor/codeigniter4')) {
        echo "✅ vendor/codeigniter4 exists<br>";
        if (file_exists('vendor/codeigniter4/framework')) {
            echo "✅ vendor/codeigniter4/framework exists<br>";
            if (file_exists('vendor/codeigniter4/framework/system')) {
                echo "✅ vendor/codeigniter4/framework/system exists<br>";
                if (file_exists('vendor/codeigniter4/framework/system/Boot.php')) {
                    echo "✅ vendor/codeigniter4/framework/system/Boot.php exists<br>";
                    echo "&nbsp;&nbsp;&nbsp;Size: " . filesize('vendor/codeigniter4/framework/system/Boot.php') . " bytes<br>";
                } else {
                    echo "❌ vendor/codeigniter4/framework/system/Boot.php missing<br>";
                }
            } else {
                echo "❌ vendor/codeigniter4/framework/system missing<br>";
            }
        } else {
            echo "❌ vendor/codeigniter4/framework missing<br>";
        }
    } else {
        echo "❌ vendor/codeigniter4 missing<br>";
    }
} else {
    echo "❌ vendor directory missing<br>";
}
echo "</div>";

echo "<div class='info'>";
echo "<h2>Paths.php Configuration Check</h2>";
if (file_exists('app/Config/Paths.php')) {
    echo "✅ app/Config/Paths.php exists<br>";
    $paths_content = file_get_contents('app/Config/Paths.php');
    
    // Extract systemDirectory from Paths.php
    if (preg_match('/systemDirectory\s*=\s*[\'"]([^\'"]+)[\'"]/', $paths_content, $matches)) {
        $system_dir = $matches[1];
        echo "System directory configured as: $system_dir<br>";
        
        // Check if this configured path exists
        $configured_exists = file_exists($system_dir);
        $status = $configured_exists ? "✅ EXISTS" : "❌ MISSING";
        echo "Configured system path exists: $status<br>";
        
        if ($configured_exists) {
            $boot_in_configured = file_exists($system_dir . '/Boot.php');
            $boot_status = $boot_in_configured ? "✅ Boot.php EXISTS" : "❌ Boot.php MISSING";
            echo "Boot.php in configured path: $boot_status<br>";
        }
    } else {
        echo "❌ Could not find systemDirectory configuration<br>";
    }
} else {
    echo "❌ app/Config/Paths.php missing<br>";
}
echo "</div>";

echo "<div class='error'>";
echo "<h2>Recommended Actions</h2>";
echo "<p><strong>Based on the analysis above:</strong></p>";
echo "<ol>";
echo "<li>If vendor/codeigniter4/framework/system/Boot.php exists, the issue might be in Paths.php configuration</li>";
echo "<li>If system directory exists but Boot.php is missing, the system files are incomplete</li>";
echo "<li>If no system directory is found, you need to upload the CodeIgniter framework files</li>";
echo "</ol>";
echo "</div>";

?>
