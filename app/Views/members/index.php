<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<style>
    .member-active {
        color: #198754 !important;
        font-weight: bold;
    }

    .member-inactive {
        color: #dc3545 !important;
        font-style: italic;
    }

    .member-active:hover, .member-inactive:hover {
        text-decoration: underline;
    }


</style>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Members <small class="text-muted">(<?= number_format($total_members) ?> total)</small></h1>
    <a href="<?= site_url('members/new') ?>" class="btn btn-success">
        <i class="fas fa-plus"></i> Add New Member
    </a>
</div>

<!-- Search and Filter Controls -->
<div class="card mb-3">
    <div class="card-body">
        <form method="GET" class="row g-3" id="searchForm">
            <div class="col-md-9">
                <label for="search" class="form-label">Search Members</label>
                <input type="text" class="form-control" id="search" name="search" value="<?= esc($search) ?>" placeholder="Search by name, phone, or WhatsApp...">
            </div>
            <div class="col-md-3 d-flex align-items-end">
                <button type="submit" class="btn btn-primary me-2">
                    <i class="fas fa-search"></i> Search
                </button>
                <a href="<?= site_url('members') ?>" class="btn btn-secondary">
                    <i class="fas fa-times"></i> Clear
                </a>
            </div>
        </form>
    </div>
</div>

<!-- Results Info -->
<div class="d-flex justify-content-between align-items-center mb-3">
    <div>
        Showing <?= number_format($pagination['showing_from']) ?> to <?= number_format($pagination['showing_to']) ?> of <?= number_format($pagination['total_items']) ?> members
    </div>
</div>

<div class="card">
    <div class="card-body">
        <div class="table-responsive">
            <table class="table table-striped table-hover datatable">
                <thead>
                    <tr>
                        <th>ID</th>
                        <th>Name</th>
                        <th>Post Office</th>
                        <th>Contact</th>
                        <th>Balance</th>
                        <th>Last Payment</th>
                        <th>Join Date</th>
                        <th>Actions</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (empty($members)): ?>
                        <tr>
                            <td colspan="8" class="text-center py-4">
                                <i class="fas fa-users fa-3x text-muted mb-3"></i>
                                <p class="text-muted">No members found.</p>
                                <?php if (!empty($search)): ?>
                                    <a href="<?= site_url('members') ?>" class="btn btn-primary">View All Members</a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php else: ?>
                        <?php foreach ($members as $member): ?>
                            <tr>
                                <td><?= $member['member_id'] ?></td>
                                <td>
                                    <a href="<?= site_url('members/show/' . $member['member_id']) ?>" class="<?= $member['status'] == 'active' ? 'member-active' : 'member-inactive' ?>">
                                        <?= esc($member['name']) ?>
                                    </a>
                                </td>
                                <td><?= esc($member['post_office'] ?? '-') ?></td>
                                <td>
                                    <?php if (!empty($member['phone'])): ?>
                                        <div class="d-flex align-items-center gap-4">
                                            <a href="tel:<?= $member['phone'] ?>" class="text-decoration-none" data-bs-toggle="tooltip" title="Call">
                                                <i class="fas fa-phone text-primary fa-lg"></i>
                                            </a>
                                            <?php if (!empty($member['whatsapp_number']) && $member['whatsapp_number'] !== $member['phone']): ?>
                                                <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $member['whatsapp_number']) ?>" target="_blank" class="text-decoration-none" data-bs-toggle="tooltip" title="WhatsApp">
                                                    <i class="fab fa-whatsapp text-success fa-lg"></i>
                                                </a>
                                            <?php elseif (!empty($member['whatsapp_number'])): ?>
                                                <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $member['whatsapp_number']) ?>" target="_blank" class="text-decoration-none" data-bs-toggle="tooltip" title="WhatsApp">
                                                    <i class="fab fa-whatsapp text-success fa-lg"></i>
                                                </a>
                                            <?php endif; ?>
                                        </div>
                                    <?php else: ?>
                                        -
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php
                                    $balance = $member['balance'] ?? 0;
                                    $balanceClass = $balance > 0 ? 'text-danger fw-bold' : 'text-muted';
                                    ?>
                                    <span class="<?= $balanceClass ?>">
                                        <?= $balance > 0 ? format_currency($balance) : '₹0' ?>
                                    </span>
                                </td>
                                <td>
                                    <?php if (!empty($member['last_payment_date'])): ?>
                                        <?= date('d M Y', strtotime($member['last_payment_date'])) ?>
                                    <?php else: ?>
                                        <span class="text-muted">No payments</span>
                                    <?php endif; ?>
                                </td>
                                <td><?= date('d M Y', strtotime($member['join_date'])) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#deleteModal<?= $member['member_id'] ?>" title="Delete">
                                            <i class="fas fa-trash"></i>
                                        </button>
                                    </div>

                                    <!-- Delete Modal -->
                                    <div class="modal fade" id="deleteModal<?= $member['member_id'] ?>" tabindex="-1" aria-labelledby="deleteModalLabel<?= $member['member_id'] ?>" aria-hidden="true">
                                        <div class="modal-dialog">
                                            <div class="modal-content">
                                                <div class="modal-header">
                                                    <h5 class="modal-title" id="deleteModalLabel<?= $member['member_id'] ?>">Confirm Delete</h5>
                                                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                                </div>
                                                <div class="modal-body">
                                                    Are you sure you want to delete member <strong><?= esc($member['name']) ?></strong>? This action cannot be undone.
                                                </div>
                                                <div class="modal-footer">
                                                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                                    <a href="<?= site_url('members/delete/' . $member['member_id']) ?>" class="btn btn-danger">Delete</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Destroy existing DataTable if it exists
    if ($.fn.DataTable.isDataTable('.datatable')) {
        $('.datatable').DataTable().destroy();
    }

    // Custom DataTables configuration for members
    $('.datatable').DataTable({
        responsive: true,
        order: [[0, 'desc']], // Sort by Member ID descending by default
        dom: '<"row"<"col-sm-6"f><"col-sm-6"l>><"row"<"col-sm-12"tr>><"row"<"col-sm-5"i><"col-sm-7"p>>',
        columnDefs: [
            {
                // Member ID column - numeric sorting
                targets: 0,
                type: 'num'
            },
            {
                // Name column - string sorting
                targets: 1,
                type: 'string'
            },
            {
                // Post Office column - string sorting
                targets: 2,
                type: 'string'
            },
            {
                // Contact column - disable sorting (contains icons)
                targets: 3,
                orderable: false
            },
            {
                // Balance column - currency sorting
                targets: 4,
                type: 'currency',
                render: function(data, type, row) {
                    if (type === 'sort' || type === 'type') {
                        // Extract numeric value for sorting
                        return parseFloat(data.replace(/[₹,\s]/g, '')) || 0;
                    }
                    return data;
                }
            },
            {
                // Last Payment column - date sorting
                targets: 5,
                type: 'date',
                render: function(data, type, row) {
                    if (type === 'sort' || type === 'type') {
                        // Handle "No payments" text
                        if (data.includes('No payments')) {
                            return 0; // Sort to bottom
                        }
                        // Extract date for sorting - format "02 Jan 2024"
                        var dateMatch = data.match(/(\d{2}\s\w{3}\s\d{4})/);
                        if (dateMatch) {
                            return new Date(dateMatch[1]).getTime();
                        }
                        return 0;
                    }
                    return data;
                }
            },
            {
                // Join Date column - date sorting
                targets: 6,
                type: 'date',
                render: function(data, type, row) {
                    if (type === 'sort' || type === 'type') {
                        // Extract date for sorting - format "02 Jan 2024"
                        var dateMatch = data.match(/(\d{2}\s\w{3}\s\d{4})/);
                        if (dateMatch) {
                            return new Date(dateMatch[1]).getTime();
                        }
                        return 0;
                    }
                    return data;
                }
            },
            {
                // Actions column - disable sorting
                targets: 7,
                orderable: false
            }
        ]
    });
});
</script>
<?= $this->endSection() ?>
