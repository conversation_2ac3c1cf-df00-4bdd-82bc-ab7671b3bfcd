<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Edit Commitment</h1>
    <a href="<?= site_url('commitments/show/' . $commitment['commitment_id']) ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Commitment
    </a>
</div>

<div class="card">
    <div class="card-body">
        <div class="alert alert-warning mb-4">
            <h6 class="alert-heading"><i class="fas fa-exclamation-triangle"></i> Important Notice</h6>
            <p class="mb-0">Editing this commitment will permanently delete any associated payments and recalculate all balances. This action cannot be undone.</p>
        </div>

        <form action="<?= site_url('commitments/update/' . $commitment['commitment_id']) ?>" method="post">
            <?= csrf_field() ?>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="member_id" class="form-label">Member <span class="text-danger">*</span></label>
                    <select class="form-select <?= session('errors.member_id') ? 'is-invalid' : '' ?>" id="member_id" name="member_id" required>
                        <option value="">Select Member</option>
                        <?php foreach ($members as $member): ?>
                            <option value="<?= $member['member_id'] ?>" <?= old('member_id', $commitment['member_id']) == $member['member_id'] ? 'selected' : '' ?>>
                                <?= $member['name'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if (session('errors.member_id')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.member_id') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">₹</span>
                        <input type="number" step="0.01" class="form-control <?= session('errors.amount') ? 'is-invalid' : '' ?>" id="amount" name="amount" value="<?= old('amount', $commitment['amount']) ?>" required>
                        <?php if (session('errors.amount')): ?>
                            <div class="invalid-feedback">
                                <?= session('errors.amount') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="frequency" class="form-label">Frequency <span class="text-danger">*</span></label>
                    <select class="form-select <?= session('errors.frequency') ? 'is-invalid' : '' ?>" id="frequency" name="frequency" required>
                        <option value="monthly" <?= old('frequency', $commitment['frequency']) == 'monthly' ? 'selected' : '' ?>>Monthly</option>
                        <option value="one-time" <?= old('frequency', $commitment['frequency']) == 'one-time' ? 'selected' : '' ?>>One-time</option>
                        <?php if (in_array(old('frequency', $commitment['frequency']), ['quarterly', 'yearly'])): ?>
                        <option value="<?= old('frequency', $commitment['frequency']) ?>" selected><?= ucfirst(old('frequency', $commitment['frequency'])) ?> (Legacy)</option>
                        <?php endif; ?>
                    </select>
                    <?php if (session('errors.frequency')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.frequency') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <label for="start_date" class="form-label">Start Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control <?= session('errors.start_date') ? 'is-invalid' : '' ?>" id="start_date" name="start_date" value="<?= old('start_date', $commitment['start_date']) ?>" required>
                    <?php if (session('errors.start_date')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.start_date') ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="end_date" class="form-label">End Date <span class="text-danger" id="end_date_required">*</span></label>
                    <input type="date" class="form-control <?= session('errors.end_date') ? 'is-invalid' : '' ?>" id="end_date" name="end_date" value="<?= old('end_date', $commitment['end_date']) ?>" required>
                    <?php if (session('errors.end_date')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.end_date') ?>
                        </div>
                    <?php endif; ?>
                    <div class="form-text" id="end_date_help"></div>
                </div>
            </div>

            <div class="mb-3">
                <label for="notes" class="form-label">Notes</label>
                <textarea class="form-control <?= session('errors.notes') ? 'is-invalid' : '' ?>" id="notes" name="notes" rows="3"><?= old('notes', $commitment['notes']) ?></textarea>
                <?php if (session('errors.notes')): ?>
                    <div class="invalid-feedback">
                        <?= session('errors.notes') ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="reset" class="btn btn-secondary">Reset</button>
                <button type="submit" class="btn btn-primary">Update Commitment</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    const frequencySelect = document.getElementById('frequency');
    const startDateInput = document.getElementById('start_date');
    const endDateInput = document.getElementById('end_date');
    const endDateHelp = document.getElementById('end_date_help');

    function handleFrequencyChange() {
        const frequency = frequencySelect.value;
        const endDateRequired = document.getElementById('end_date_required');

        if (frequency === 'one-time') {
            // For one-time commitments
            endDateInput.disabled = true;
            endDateInput.required = true;
            endDateInput.value = startDateInput.value;
            endDateHelp.textContent = '';
            endDateHelp.className = 'form-text';
            endDateRequired.style.display = 'inline'; // Show required asterisk
        } else {
            // For monthly commitments
            endDateInput.disabled = false;
            endDateInput.required = true; // End date is now required for monthly commitments
            // Clear the end date when switching from one-time to monthly
            // Only clear if it was auto-set (equals start date)
            if (endDateInput.value === startDateInput.value) {
                endDateInput.value = '';
            }
            endDateHelp.textContent = '';
            endDateHelp.className = 'form-text';
            endDateRequired.style.display = 'inline'; // Show required asterisk
        }
    }

    function handleStartDateChange() {
        const frequency = frequencySelect.value;

        if (frequency === 'one-time') {
            // Automatically set end date to start date for one-time commitments
            endDateInput.value = startDateInput.value;
        }

        // Set minimum date for end date to be greater than start date
        if (startDateInput.value) {
            const startDate = new Date(startDateInput.value);
            const nextDay = new Date(startDate);
            nextDay.setDate(startDate.getDate() + 1);
            endDateInput.min = nextDay.toISOString().split('T')[0];

            // If current end date is less than or equal to start date, clear it
            if (endDateInput.value && endDateInput.value <= startDateInput.value && frequency !== 'one-time') {
                endDateInput.value = '';
            }
        } else {
            // Remove min constraint if no start date
            endDateInput.removeAttribute('min');
        }
    }

    // Initialize the form state based on current frequency
    handleFrequencyChange();

    // Initialize date constraints if start date is already set
    if (startDateInput.value) {
        handleStartDateChange();
    }

    function handleEndDateChange() {
        const frequency = frequencySelect.value;

        // For monthly commitments, validate that end date is after start date
        if (frequency === 'monthly' && startDateInput.value && endDateInput.value) {
            if (endDateInput.value <= startDateInput.value) {
                endDateInput.setCustomValidity('End date must be greater than start date');
            } else {
                endDateInput.setCustomValidity('');
            }
        }
    }

    // Add event listeners
    frequencySelect.addEventListener('change', handleFrequencyChange);
    startDateInput.addEventListener('change', handleStartDateChange);
    startDateInput.addEventListener('input', handleStartDateChange);
    endDateInput.addEventListener('change', handleEndDateChange);
    endDateInput.addEventListener('input', handleEndDateChange);
});
</script>
<?= $this->endSection() ?>
