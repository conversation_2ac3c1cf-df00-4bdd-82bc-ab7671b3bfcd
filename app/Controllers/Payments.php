<?php

namespace App\Controllers;

use App\Models\MemberModel;
use App\Models\CommitmentModel;
use App\Models\PaymentModel;
use App\Models\CollectionSummaryModel;
use CodeIgniter\RESTful\ResourceController;

class Payments extends ResourceController
{
    protected $memberModel;
    protected $commitmentModel;
    protected $paymentModel;
    protected $summaryModel;

    public function __construct()
    {
        $this->memberModel = new MemberModel();
        $this->commitmentModel = new CommitmentModel();
        $this->paymentModel = new PaymentModel();
        $this->summaryModel = new CollectionSummaryModel();
    }

    /**
     * Display a list of all payments
     *
     * @return mixed
     */
    public function index()
    {
        $data = [
            'title' => 'Payments',
            'payments' => $this->paymentModel->getPaymentsWithMemberDetails()
        ];

        return view('payments/index', $data);
    }

    /**
     * Display the form to create a new payment
     *
     * @return mixed
     */
    public function new()
    {
        // Get member_id and commitment_id from URL parameters if available
        $memberId = $this->request->getGet('member_id');
        $commitmentId = $this->request->getGet('commitment_id');
        $returnToMember = $this->request->getGet('return_to_member');
        $selectedMember = null;
        $selectedCommitment = null;

        // If member_id is provided, check if the member exists
        if ($memberId) {
            $selectedMember = $this->memberModel->find($memberId);
        }

        // If commitment_id is provided, check if the commitment exists
        if ($commitmentId) {
            $selectedCommitment = $this->commitmentModel->find($commitmentId);

            // If commitment exists but member_id wasn't provided, get it from the commitment
            if ($selectedCommitment && !$selectedMember) {
                $memberId = $selectedCommitment['member_id'];
                $selectedMember = $this->memberModel->find($memberId);
            }
        }

        $data = [
            'title' => 'Add New Payment',
            'members' => $this->memberModel->getActiveMembers(),
            'receipt_number' => $this->paymentModel->generateReceiptNumber(),
            'selectedMemberId' => $selectedMember ? $selectedMember['member_id'] : null,
            'selectedCommitmentId' => $selectedCommitment ? $selectedCommitment['commitment_id'] : null,
            'returnToMember' => $returnToMember
        ];

        return view('payments/create', $data);
    }

    /**
     * Create a new payment
     *
     * @return mixed
     */
    public function create()
    {
        $rules = $this->paymentModel->getValidationRules();

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $memberId = $this->request->getPost('member_id');
        $commitmentId = $this->request->getPost('commitment_id') ?: null;

        // Check if member exists
        if (!$this->memberModel->find($memberId)) {
            return redirect()->back()->withInput()->with('error', 'Member not found');
        }

        // Check if commitment exists if provided
        if ($commitmentId && !$this->commitmentModel->find($commitmentId)) {
            return redirect()->back()->withInput()->with('error', 'Commitment not found');
        }

        // Get selected months if any
        $selectedMonthsForNotes = $this->request->getPost('selected_months') ? json_decode($this->request->getPost('selected_months'), true) : [];
        $isAdditionalPayment = $this->request->getPost('is_additional_payment') ? true : false;

        // Prepare notes with selected months if applicable
        $notes = $this->request->getPost('notes');

        if (!empty($selectedMonthsForNotes)) {
            $formattedMonths = [];
            foreach ($selectedMonthsForNotes as $month) {
                // Add day component to avoid month overflow issues
                $date = \DateTime::createFromFormat('Y-m-d', $month . '-01');
                if ($date) {
                    $formattedMonths[] = $date->format('F Y');
                }
            }

            if (!empty($formattedMonths)) {
                $monthsText = 'Payment for: ' . implode(', ', $formattedMonths);
                $notes = $notes ? $notes . "\n\n" . $monthsText : $monthsText;
            }
        } else if ($isAdditionalPayment && $commitmentId) {
            // For additional payments where all months are already paid
            $commitment = $this->commitmentModel->find($commitmentId);
            if ($commitment) {
                $additionalText = 'Additional payment for commitment #' . $commitmentId . ' (all months already paid)';
                $notes = $notes ? $notes . "\n\n" . $additionalText : $additionalText;
            }
        }

        // Handle payment periods
        $paymentPeriods = null;
        $selectedMonthsRaw = $this->request->getPost('selected_months');
        if ($selectedMonthsRaw) {
            $paymentPeriods = $selectedMonthsRaw;
        }

        $paymentId = $this->paymentModel->insert([
            'member_id' => $memberId,
            'commitment_id' => $commitmentId,
            'amount' => $this->request->getPost('amount'),
            'payment_date' => $this->request->getPost('payment_date'),
            'receipt_number' => $this->request->getPost('receipt_number'),
            'receipt_book_number' => $this->request->getPost('receipt_book_number'),
            'payment_method' => $this->request->getPost('payment_method'),
            'notes' => $notes,
            'payment_periods' => $paymentPeriods
        ]);

        if (!$paymentId) {
            return redirect()->back()->withInput()->with('error', 'Failed to create payment');
        }

        // Recalculate summary
        $this->summaryModel->recalculateSummary($memberId);

        // Check if we should redirect back to member page
        $returnToMember = $this->request->getPost('return_to_member');
        if ($returnToMember) {
            return redirect()->to('/members/show/' . $returnToMember)->with('message', 'Payment recorded successfully');
        }

        return redirect()->to('/payments')->with('message', 'Payment recorded successfully');
    }

    /**
     * Display a specific payment
     *
     * @param int $id
     * @return mixed
     */
    public function show($id = null)
    {
        $payment = $this->paymentModel->find($id);

        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }

        $member = $this->memberModel->find($payment['member_id']);
        $commitment = null;

        if ($payment['commitment_id']) {
            $commitment = $this->commitmentModel->find($payment['commitment_id']);
        }

        $data = [
            'title' => 'Payment Details',
            'payment' => $payment,
            'member' => $member,
            'commitment' => $commitment
        ];

        return view('payments/show', $data);
    }

    /**
     * Display the form to edit a payment
     *
     * @param int $id
     * @return mixed
     */
    public function edit($id = null)
    {
        $payment = $this->paymentModel->find($id);

        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }

        // Get return_to_member parameter
        $returnToMember = $this->request->getGet('return_to_member');

        // Get all commitments for the member, including the current one even if it's fully paid
        $allCommitments = $this->commitmentModel->where('member_id', $payment['member_id'])->findAll();
        $unpaidCommitments = $this->commitmentModel->getActiveUnpaidCommitments($payment['member_id']);

        // If the current commitment is not in the unpaid list, add it
        $currentCommitmentIncluded = false;
        if ($payment['commitment_id']) {
            foreach ($unpaidCommitments as $commitment) {
                if ($commitment['commitment_id'] == $payment['commitment_id']) {
                    $currentCommitmentIncluded = true;
                    break;
                }
            }

            if (!$currentCommitmentIncluded) {
                foreach ($allCommitments as $commitment) {
                    if ($commitment['commitment_id'] == $payment['commitment_id']) {
                        $unpaidCommitments[] = $commitment;
                        break;
                    }
                }
            }
        }

        $data = [
            'title' => 'Edit Payment',
            'payment' => $payment,
            'members' => $this->memberModel->getActiveMembers(),
            'commitments' => $unpaidCommitments,
            'returnToMember' => $returnToMember
        ];

        return view('payments/edit', $data);
    }

    /**
     * Update a payment
     *
     * @param int $id
     * @return mixed
     */
    public function update($id = null)
    {
        $payment = $this->paymentModel->find($id);

        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }

        $rules = $this->paymentModel->getValidationRules();

        if (!$this->validate($rules)) {
            return redirect()->back()->withInput()->with('errors', $this->validator->getErrors());
        }

        $memberId = $this->request->getPost('member_id');
        $commitmentId = $this->request->getPost('commitment_id') ?: null;

        // Check if member exists
        if (!$this->memberModel->find($memberId)) {
            return redirect()->back()->withInput()->with('error', 'Member not found');
        }

        // Check if commitment exists if provided
        if ($commitmentId && !$this->commitmentModel->find($commitmentId)) {
            return redirect()->back()->withInput()->with('error', 'Commitment not found');
        }

        // Handle payment periods
        $paymentPeriods = $payment['payment_periods']; // Keep existing value by default
        $selectedMonths = $this->request->getPost('selected_months');
        if ($selectedMonths) {
            $paymentPeriods = $selectedMonths;
        }

        $this->paymentModel->update($id, [
            'member_id' => $memberId,
            'commitment_id' => $commitmentId,
            'amount' => $this->request->getPost('amount'),
            'payment_date' => $this->request->getPost('payment_date'),
            'receipt_number' => $this->request->getPost('receipt_number'),
            'receipt_book_number' => $this->request->getPost('receipt_book_number'),
            'payment_method' => $this->request->getPost('payment_method'),
            'notes' => $this->request->getPost('notes'),
            'payment_periods' => $paymentPeriods
        ]);

        // Recalculate summary for both old and new member (if changed)
        $this->summaryModel->recalculateSummary($payment['member_id']);
        if ($payment['member_id'] != $memberId) {
            $this->summaryModel->recalculateSummary($memberId);
        }

        // Check if we should redirect back to member page
        $returnToMember = $this->request->getPost('return_to_member');
        if ($returnToMember) {
            return redirect()->to('/members/show/' . $returnToMember)->with('message', 'Payment updated successfully');
        }

        return redirect()->to('/payments')->with('message', 'Payment updated successfully');
    }

    /**
     * Delete a payment
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id = null)
    {
        $payment = $this->paymentModel->find($id);

        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }

        $memberId = $payment['member_id'];

        $this->paymentModel->delete($id);

        // Recalculate summary
        $this->summaryModel->recalculateSummary($memberId);

        // Check if we should redirect back to member page
        $returnToMember = $this->request->getGet('return_to_member');
        if ($returnToMember) {
            return redirect()->to('/members/show/' . $returnToMember)->with('message', 'Payment deleted successfully');
        }

        return redirect()->to('/payments')->with('message', 'Payment deleted successfully');
    }

    /**
     * Display payments for a specific member
     *
     * @param int $memberId
     * @return mixed
     */
    public function memberPayments($memberId)
    {
        $member = $this->memberModel->find($memberId);

        if (!$member) {
            return redirect()->to('/members')->with('error', 'Member not found');
        }

        $data = [
            'title' => 'Payments for ' . $member['name'],
            'member' => $member,
            'payments' => $this->paymentModel->getMemberPayments($memberId)
        ];

        return view('payments/member_payments', $data);
    }

    /**
     * Generate a receipt for a payment
     *
     * @param int $id
     * @return mixed
     */
    public function generateReceipt($id = null)
    {
        $payment = $this->paymentModel->find($id);

        if (!$payment) {
            return redirect()->to('/payments')->with('error', 'Payment not found');
        }

        $member = $this->memberModel->find($payment['member_id']);
        $commitment = null;

        if ($payment['commitment_id']) {
            $commitment = $this->commitmentModel->find($payment['commitment_id']);
        }

        $data = [
            'title' => 'Receipt',
            'payment' => $payment,
            'member' => $member,
            'commitment' => $commitment,
            'organization' => [
                'name' => 'Your Organization Name',
                'address' => 'Your Organization Address',
                'phone' => 'Your Organization Phone',
                'email' => 'Your Organization Email'
            ]
        ];

        return view('payments/receipt', $data);
    }
}
