name: Deploy to <PERSON><PERSON> via SSH

on:
  push:
    branches:
      - stage  # Change this if your main branch has a different name

jobs:
  deploy:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Deploy via SSH
        uses: appleboy/ssh-action@v1.0.0
        with:
          host: ${{ secrets.HOSTINGER_HOST }}
          username: ${{ secrets.HOSTINGER_USER }}
          key: ${{ secrets.HOSTINGER_SSH_KEY }}
          port: ${{ secrets.HOSTINGER_PORT }}
          script: |
            cd /home/<USER>/domains/halqa.mazharulirfan.com/public_html/dev
            git pull origin stage
            if [ ! -d .git ]; then
              git clone https://araheemkk:<EMAIL>/araheemkk/halqa.git 
              git checkout -b stage origin/stage              
            else
              git pull origin stage
            fi

