<?php
/**
 * Complete Site Functionality Test
 */

echo "<h1>Site Functionality Test</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .info{background:#e7f3ff;padding:15px;margin:10px 0;border-left:4px solid #2196F3;} .error{background:#ffebee;padding:15px;margin:10px 0;border-left:4px solid #f44336;} .success{background:#e8f5e8;padding:15px;margin:10px 0;border-left:4px solid #4caf50;}</style>";

// Test 1: Basic file structure
echo "<div class='info'>";
echo "<h2>1. File Structure Check</h2>";
$required_files = [
    'index.php',
    'app/Config/App.php',
    'app/Config/Database.php',
    'app/Config/Routes.php',
    '.env'
];

foreach ($required_files as $file) {
    $exists = file_exists($file);
    $status = $exists ? '✅' : '❌';
    echo "$status $file<br>";
}
echo "</div>";

// Test 2: Environment setup
echo "<div class='info'>";
echo "<h2>2. Environment Configuration</h2>";
if (file_exists('.env')) {
    echo "✅ .env file exists<br>";
    $env_content = file_get_contents('.env');
    
    // Check for key environment variables
    $env_vars = ['CI_ENVIRONMENT', 'app.baseURL', 'database.default.hostname'];
    foreach ($env_vars as $var) {
        if (strpos($env_content, $var) !== false) {
            echo "✅ $var found in .env<br>";
        } else {
            echo "❌ $var missing from .env<br>";
        }
    }
} else {
    echo "❌ .env file missing<br>";
}
echo "</div>";

// Test 3: Try to simulate index.php execution
echo "<div class='info'>";
echo "<h2>3. Index.php Execution Test</h2>";

try {
    // Capture any output or errors from index.php
    ob_start();
    error_reporting(E_ALL);
    ini_set('display_errors', 1);
    
    // Set up the same environment as index.php
    if (!defined('FCPATH')) {
        define('FCPATH', __DIR__ . DIRECTORY_SEPARATOR);
    }
    
    // Check if we can load the paths
    if (file_exists('app/Config/Paths.php')) {
        require 'app/Config/Paths.php';
        $paths = new Config\Paths();
        echo "✅ Paths loaded successfully<br>";
        
        // Check system directory
        if (file_exists($paths->systemDirectory . '/Boot.php')) {
            echo "✅ Boot.php found at: " . $paths->systemDirectory . "<br>";
            
            // Try to include Boot.php
            require $paths->systemDirectory . '/Boot.php';
            echo "✅ Boot.php included successfully<br>";
            
            // Try to boot the application
            if (class_exists('CodeIgniter\\Boot')) {
                echo "✅ CodeIgniter\\Boot class available<br>";
                
                // This is the critical test - can we actually boot?
                try {
                    echo "Attempting to boot CodeIgniter...<br>";
                    $app = CodeIgniter\Boot::bootWeb($paths);
                    echo "✅ CodeIgniter booted successfully!<br>";
                    
                    // Try to get a response
                    ob_start();
                    $response = $app->run();
                    $output = ob_get_clean();
                    
                    echo "✅ Application ran successfully<br>";
                    echo "Response type: " . get_class($response) . "<br>";
                    echo "Output length: " . strlen($output) . " characters<br>";
                    
                    if (strlen($output) > 0) {
                        echo "<strong>First 500 characters of output:</strong><br>";
                        echo "<pre style='background:#f5f5f5;padding:10px;max-height:200px;overflow:auto;'>";
                        echo htmlspecialchars(substr($output, 0, 500));
                        echo "</pre>";
                    }
                    
                } catch (Exception $e) {
                    echo "❌ Boot failed: " . htmlspecialchars($e->getMessage()) . "<br>";
                    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
                    echo "<pre>" . htmlspecialchars($e->getTraceAsString()) . "</pre>";
                } catch (Error $e) {
                    echo "❌ Fatal error during boot: " . htmlspecialchars($e->getMessage()) . "<br>";
                    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
                }
                
            } else {
                echo "❌ CodeIgniter\\Boot class not found<br>";
            }
            
        } else {
            echo "❌ Boot.php not found at: " . $paths->systemDirectory . "<br>";
        }
        
    } else {
        echo "❌ app/Config/Paths.php not found<br>";
    }
    
    $execution_output = ob_get_clean();
    if (!empty($execution_output)) {
        echo "<strong>Execution output:</strong><br>";
        echo "<pre style='background:#f5f5f5;padding:10px;'>" . htmlspecialchars($execution_output) . "</pre>";
    }
    
} catch (Exception $e) {
    echo "❌ Critical error: " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
} catch (Error $e) {
    echo "❌ Fatal error: " . htmlspecialchars($e->getMessage()) . "<br>";
    echo "File: " . $e->getFile() . " Line: " . $e->getLine() . "<br>";
}

echo "</div>";

// Test 4: Database connection
echo "<div class='info'>";
echo "<h2>4. Database Connection Test</h2>";
try {
    if (file_exists('.env')) {
        // Parse .env file
        $env_lines = file('.env', FILE_IGNORE_NEW_LINES | FILE_SKIP_EMPTY_LINES);
        $env_vars = [];
        foreach ($env_lines as $line) {
            if (strpos($line, '=') !== false && !str_starts_with(trim($line), '#')) {
                list($key, $value) = explode('=', $line, 2);
                $env_vars[trim($key)] = trim($value);
            }
        }
        
        // Try database connection
        if (isset($env_vars['database.default.hostname'])) {
            $host = $env_vars['database.default.hostname'];
            $database = $env_vars['database.default.database'] ?? '';
            $username = $env_vars['database.default.username'] ?? '';
            $password = $env_vars['database.default.password'] ?? '';
            
            echo "Attempting database connection...<br>";
            echo "Host: $host<br>";
            echo "Database: $database<br>";
            echo "Username: $username<br>";
            
            try {
                $pdo = new PDO("mysql:host=$host;dbname=$database", $username, $password);
                echo "✅ Database connection successful<br>";
            } catch (PDOException $e) {
                echo "❌ Database connection failed: " . htmlspecialchars($e->getMessage()) . "<br>";
            }
        } else {
            echo "❌ Database configuration not found in .env<br>";
        }
    }
} catch (Exception $e) {
    echo "❌ Database test error: " . htmlspecialchars($e->getMessage()) . "<br>";
}
echo "</div>";

echo "<div class='info'>";
echo "<h2>5. Quick Actions</h2>";
echo "<p><a href='../'>🏠 Try Main Site</a></p>";
echo "<p><a href='phpinfo.php'>🔍 PHP Info</a></p>";
echo "</div>";

?>
