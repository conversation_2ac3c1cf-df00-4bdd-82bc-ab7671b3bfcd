<?php
// Turn on error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>Debug Information</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .ok{color:green;} .error{color:red;}</style>";

echo "<h2>PHP Information</h2>";
echo "PHP Version: " . phpversion() . "<br>";
echo "Current Directory: " . getcwd() . "<br>";
echo "Document Root: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";

echo "<h2>File Checks</h2>";

$files = ['index.php', '.env', 'app/Config/App.php', 'vendor/autoload.php'];
foreach ($files as $file) {
    echo "File {$file}: ";
    if (file_exists($file)) {
        echo "<span class='ok'>✓ Exists</span>";
        if (is_readable($file)) {
            echo " <span class='ok'>✓ Readable</span>";
        } else {
            echo " <span class='error'>✗ Not Readable</span>";
        }
    } else {
        echo "<span class='error'>✗ Missing</span>";
    }
    echo "<br>";
}

echo "<h2>Directory Checks</h2>";
$dirs = ['app', 'vendor', 'writable', 'writable/cache', 'writable/logs'];
foreach ($dirs as $dir) {
    echo "Directory {$dir}: ";
    if (is_dir($dir)) {
        echo "<span class='ok'>✓ Exists</span>";
        if (is_readable($dir)) {
            echo " <span class='ok'>✓ Readable</span>";
        } else {
            echo " <span class='error'>✗ Not Readable</span>";
        }
        if (is_writable($dir)) {
            echo " <span class='ok'>✓ Writable</span>";
        } else {
            echo " <span class='error'>✗ Not Writable</span>";
        }
    } else {
        echo "<span class='error'>✗ Missing</span>";
    }
    echo "<br>";
}

echo "<h2>Environment Test</h2>";
if (file_exists('.env')) {
    echo "Environment file exists<br>";
    // Try to read first few lines safely
    $env_content = file_get_contents('.env');
    if ($env_content !== false) {
        echo "Environment file is readable<br>";
        // Check for common issues
        if (strpos($env_content, 'CI_ENVIRONMENT') !== false) {
            echo "<span class='ok'>✓ CI_ENVIRONMENT found</span><br>";
        } else {
            echo "<span class='error'>✗ CI_ENVIRONMENT missing</span><br>";
        }
    } else {
        echo "<span class='error'>✗ Cannot read environment file</span><br>";
    }
} else {
    echo "<span class='error'>✗ Environment file missing</span><br>";
}

echo "<h2>Simple PHP Test</h2>";
try {
    echo "Testing basic PHP functionality...<br>";
    
    // Test autoloader
    if (file_exists('vendor/autoload.php')) {
        echo "Attempting to load autoloader...<br>";
        require_once 'vendor/autoload.php';
        echo "<span class='ok'>✓ Autoloader loaded successfully</span><br>";
    } else {
        echo "<span class='error'>✗ Autoloader missing</span><br>";
    }
    
} catch (Exception $e) {
    echo "<span class='error'>✗ Error: " . $e->getMessage() . "</span><br>";
} catch (Error $e) {
    echo "<span class='error'>✗ Fatal Error: " . $e->getMessage() . "</span><br>";
}

echo "<h2>Next Steps</h2>";
echo "<p>1. Check the error logs in Hostinger control panel</p>";
echo "<p>2. Look for any red ✗ items above</p>";
echo "<p>3. Report back what you find</p>";

echo "<hr>";
echo "<p><em>Debug completed: " . date('Y-m-d H:i:s') . "</em></p>";
?>
