<?php
echo "<h1>Site Loading Fix Tool</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .ok{color:green;} .error{color:red;} .info{color:blue;}</style>";

echo "<h2>Testing Site Components</h2>";

// Test 1: Check if index.php exists and is readable
echo "1. Index.php file: ";
if (file_exists('index.php') && is_readable('index.php')) {
    echo "<span class='ok'>✓ Found and readable</span><br>";
} else {
    echo "<span class='error'>✗ Missing or not readable</span><br>";
}

// Test 2: Check if .htaccess exists
echo "2. .htaccess file: ";
if (file_exists('.htaccess')) {
    echo "<span class='ok'>✓ Found</span><br>";
} else {
    echo "<span class='error'>✗ Missing</span><br>";
}

// Test 3: Check if app directory exists
echo "3. App directory: ";
if (is_dir('app') && is_readable('app')) {
    echo "<span class='ok'>✓ Found and readable</span><br>";
} else {
    echo "<span class='error'>✗ Missing or not readable</span><br>";
}

// Test 4: Check vendor autoload
echo "4. Vendor autoload: ";
if (file_exists('vendor/autoload.php')) {
    echo "<span class='ok'>✓ Found</span><br>";
} else {
    echo "<span class='error'>✗ Missing</span><br>";
}

// Test 5: Check environment file
echo "5. Environment file: ";
if (file_exists('.env')) {
    echo "<span class='ok'>✓ Found</span><br>";
} else {
    echo "<span class='error'>✗ Missing</span><br>";
}

echo "<h2>Quick Tests</h2>";

// Test direct access to index.php
echo "<p><strong>Test Links:</strong></p>";
echo "<ul>";
echo "<li><a href='index.php' target='_blank'>Test index.php directly</a></li>";
echo "<li><a href='index.php/auth/login' target='_blank'>Test login page directly</a></li>";
echo "<li><a href='/' target='_blank'>Test root domain</a></li>";
echo "</ul>";

echo "<h2>Common Fixes</h2>";

echo "<h3>Fix 1: Simple .htaccess</h3>";
echo "<p>If the root domain doesn't work but index.php does, replace your .htaccess with:</p>";
echo "<textarea rows='10' cols='80' readonly>";
echo "Options -Indexes\n";
echo "DirectoryIndex index.php\n\n";
echo "<IfModule mod_rewrite.c>\n";
echo "    RewriteEngine On\n";
echo "    RewriteBase /\n";
echo "    RewriteCond %{REQUEST_FILENAME} !-f\n";
echo "    RewriteCond %{REQUEST_FILENAME} !-d\n";
echo "    RewriteRule ^(.*)$ index.php/\$1 [L,QSA]\n";
echo "</IfModule>";
echo "</textarea>";

echo "<h3>Fix 2: Check Error Logs</h3>";
echo "<p>Check your Hostinger error logs in the control panel for specific error messages.</p>";

echo "<h3>Fix 3: Test URLs</h3>";
echo "<p>Try these URLs to identify the issue:</p>";
echo "<ul>";
echo "<li><code>https://halqa.mazharulirfan.com/index.php</code> - Direct access</li>";
echo "<li><code>https://halqa.mazharulirfan.com/index.php/auth/login</code> - Login page</li>";
echo "<li><code>https://halqa.mazharulirfan.com/</code> - Root domain</li>";
echo "</ul>";

echo "<hr>";
echo "<p><em>Generated: " . date('Y-m-d H:i:s') . "</em></p>";
?>
