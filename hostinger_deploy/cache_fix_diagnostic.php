<?php
/**
 * Cache Directory Fix Diagnostic
 * Upload this to your Hostinger public_html and run it to fix the cache issue
 */

echo "<h1>Cache Directory Fix Tool</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .ok{color:green;} .error{color:red;} .warning{color:orange;}</style>";

echo "<h2>Automatic Fix Attempt</h2>";

// Try to create cache directory if it doesn't exist
if (!is_dir('writable/cache')) {
    echo "Creating cache directory...<br>";
    if (mkdir('writable/cache', 0777, true)) {
        echo "<span class='ok'>✓ Cache directory created successfully</span><br>";
    } else {
        echo "<span class='error'>✗ Failed to create cache directory</span><br>";
    }
} else {
    echo "Cache directory exists<br>";
}

// Try to set correct permissions
if (is_dir('writable/cache')) {
    echo "Setting cache directory permissions to 777...<br>";
    if (chmod('writable/cache', 0777)) {
        echo "<span class='ok'>✓ Permissions set successfully</span><br>";
    } else {
        echo "<span class='error'>✗ Failed to set permissions</span><br>";
    }
}

// Create index.html file in cache directory for security
$cache_index = 'writable/cache/index.html';
if (!file_exists($cache_index)) {
    $content = '<html><head><title>403 Forbidden</title></head><body><h1>Directory access is forbidden.</h1></body></html>';
    if (file_put_contents($cache_index, $content)) {
        echo "<span class='ok'>✓ Security index.html created in cache directory</span><br>";
    } else {
        echo "<span class='warning'>⚠ Could not create security index.html</span><br>";
    }
}

echo "<h2>Final Status Check</h2>";

// Final verification
$directories = [
    'writable/cache' => 'Cache directory',
    'writable/logs' => 'Logs directory',
    'writable/session' => 'Session directory'
];

foreach ($directories as $dir => $description) {
    echo "{$description} ({$dir}): ";
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<span class='ok'>✓ Writable</span><br>";
        } else {
            echo "<span class='error'>✗ Not writable</span><br>";
        }
    } else {
        echo "<span class='error'>✗ Directory missing</span><br>";
    }
}

echo "<h2>Next Steps</h2>";
echo "<p>After running this fix:</p>";
echo "<ol>";
echo "<li>Run the main diagnostic: <a href='diagnostic.php'>diagnostic.php</a></li>";
echo "<li>If all checks pass, delete both diagnostic files</li>";
echo "<li>Test your main application</li>";
echo "</ol>";

echo "<hr>";
echo "<p><em>Generated: " . date('Y-m-d H:i:s') . "</em></p>";
?>
