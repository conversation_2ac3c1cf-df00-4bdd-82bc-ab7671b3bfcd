HOSTINGER DEPLOYMENT INSTRUCTIONS
=================================

🚀 STEP-BY-STEP DEPLOYMENT:

1. UPLOAD FILES:
   - Upload ALL contents of this folder to your Hostinger public_html directory
   - Make sure index.php is in the ROOT of public_html (not in a subfolder)
   - The structure should be: public_html/index.php, public_html/app/, etc.

2. SET PERMISSIONS (CRITICAL):
   In Hostinger File Manager, set these permissions:
   - writable/ folder → 777 (recursive)
   - writable/cache/ → 777
   - writable/logs/ → 777  
   - writable/session/ → 777

3. CONFIGURE DATABASE:
   - Edit .env file with your actual database password
   - Replace "YOUR_SECURE_PASSWORD_HERE" with your real password
   - Verify database credentials are correct

4. TEST DEPLOYMENT:
   - Visit: yourdomain.com/diagnostic.php
   - Fix any RED errors shown
   - Delete diagnostic.php when done

5. VERIFY APPLICATION:
   - Visit your domain: https://halqa.mazharulirfan.com/
   - Test login functionality
   - Check that all pages load correctly

🔧 HOSTINGER CONTROL PANEL SETTINGS:

1. PHP Configuration:
   - Set PHP Version to 8.0 or higher
   - Enable extensions: mysqli, mbstring, openssl, json, curl

2. Database:
   - Ensure database u888771413_halqa exists
   - Import your database if not already done
   - Verify user u888771413_halqa_user has access

⚠️ COMMON ISSUES & FIXES:

1. 403 Forbidden Error:
   - Check file permissions (writable/ must be 777)
   - Ensure index.php is in public_html root
   - Verify .htaccess file exists

2. 500 Internal Server Error:
   - Check PHP version (must be 8.0+)
   - Verify .env file exists and is configured
   - Check writable/ permissions

3. Database Connection Error:
   - Update .env with correct password
   - Verify database exists in Hostinger
   - Check database user permissions

4. CSS/Assets Not Loading:
   - Verify assets/ folder is uploaded
   - Check .htaccess allows access to assets
   - Clear browser cache

📋 FINAL CHECKLIST:

□ All files uploaded to public_html/
□ writable/ permissions set to 777
□ .env file configured with real database password
□ PHP version set to 8.0+ in Hostinger
□ Database imported and accessible
□ diagnostic.php shows all green checkmarks
□ Main application loads successfully
□ Login functionality works
□ diagnostic.php deleted for security

🆘 SUPPORT:

If issues persist:
1. Check diagnostic.php results
2. Review Hostinger error logs in control panel
3. Check writable/logs/ for application errors
4. Contact Hostinger support for server issues
