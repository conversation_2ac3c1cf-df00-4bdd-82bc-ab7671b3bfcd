<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Record New Payment</h1>
    <?php if (isset($selectedMemberId)): ?>
    <a href="<?= site_url('members/show/' . $selectedMemberId) ?>" class="btn btn-success">
        <i class="fas fa-user"></i> Back to Member
    </a>
    <?php endif; ?>
</div>

<div class="card">
    <div class="card-body">
        <form action="<?= site_url('payments/create') ?>" method="post" id="paymentForm">
            <?= csrf_field() ?>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="member_id" class="form-label">Member <span class="text-danger">*</span></label>
                    <select class="form-select <?= session('errors.member_id') ? 'is-invalid' : '' ?>" id="member_id" name="member_id" required>
                        <option value="">Select Member</option>
                        <?php foreach ($members as $member): ?>
                            <option value="<?= $member['member_id'] ?>" <?= (old('member_id') == $member['member_id'] || (!old('member_id') && isset($selectedMemberId) && $selectedMemberId == $member['member_id'])) ? 'selected' : '' ?>>
                                <?= $member['name'] ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                    <?php if (session('errors.member_id')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.member_id') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <label for="commitment_id" class="form-label">Commitment</label>
                    <select class="form-select <?= session('errors.commitment_id') ? 'is-invalid' : '' ?>" id="commitment_id" name="commitment_id">
                        <option value="">Select Commitment</option>
                        <!-- Commitments will be loaded via AJAX when a member is selected -->
                    </select>
                    <?php if (session('errors.commitment_id')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.commitment_id') ?>
                        </div>
                    <?php endif; ?>
                    <div class="form-text">Select a specific commitment this payment is for</div>
                    <div id="commitment-message"></div>
                </div>
            </div>

            <!-- Payment Period Selection (will be shown only for monthly commitments) -->
            <div class="row mb-3" id="payment-period-container" style="display: none;">
                <div class="col-md-12">
                    <label for="payment_period" class="form-label">Payment Period</label>
                    <div id="payment-period-options">
                        <!-- Options will be loaded dynamically based on commitment -->
                    </div>
                </div>
            </div>

            <!-- Month Selection (will be shown only for monthly commitments) -->
            <div class="row mb-3" id="months-container" style="display: none;">
                <div class="col-md-12">
                    <label class="form-label">Select Months to Pay For</label>
                    <div id="months-checkboxes" class="row">
                        <!-- Months will be loaded dynamically based on commitment -->
                    </div>
                    <input type="hidden" name="selected_months" id="selected-months-input">
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="amount" class="form-label">Amount <span class="text-danger">*</span></label>
                    <div class="input-group">
                        <span class="input-group-text">₹</span>
                        <input type="number" step="0.01" class="form-control <?= session('errors.amount') ? 'is-invalid' : '' ?>" id="amount" name="amount" value="<?= old('amount') ?>" required>
                        <?php if (session('errors.amount')): ?>
                            <div class="invalid-feedback">
                                <?= session('errors.amount') ?>
                            </div>
                        <?php endif; ?>
                    </div>
                    <div class="form-text" id="amount-description"></div>
                </div>

                <div class="col-md-6">
                    <label for="payment_date" class="form-label">Payment Date <span class="text-danger">*</span></label>
                    <input type="date" class="form-control <?= session('errors.payment_date') ? 'is-invalid' : '' ?>" id="payment_date" name="payment_date" value="<?= old('payment_date') ?: date('Y-m-d') ?>" required>
                    <?php if (session('errors.payment_date')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.payment_date') ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="receipt_book_number" class="form-label">Receipt Book # <span class="text-danger">*</span></label>
                    <input type="text" class="form-control <?= session('errors.receipt_book_number') ? 'is-invalid' : '' ?>" id="receipt_book_number" name="receipt_book_number" value="<?= old('receipt_book_number') ?>" required>
                    <?php if (session('errors.receipt_book_number')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.receipt_book_number') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <label for="receipt_number" class="form-label">Receipt Number <span class="text-danger">*</span></label>
                    <input type="text" class="form-control <?= session('errors.receipt_number') ? 'is-invalid' : '' ?>" id="receipt_number" name="receipt_number" value="<?= old('receipt_number') ?: $receipt_number ?>" required>
                    <?php if (session('errors.receipt_number')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.receipt_number') ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>

            <div class="row mb-3">
                <div class="col-md-6">
                    <label for="payment_method" class="form-label">Payment Method <span class="text-danger">*</span></label>
                    <select class="form-select <?= session('errors.payment_method') ? 'is-invalid' : '' ?>" id="payment_method" name="payment_method" required>
                        <option value="">Select Payment Method</option>
                        <option value="cash" <?= old('payment_method') == 'cash' ? 'selected' : '' ?>>Cash</option>
                        <option value="check" <?= old('payment_method') == 'check' ? 'selected' : '' ?>>Check</option>
                        <option value="bank_transfer" <?= old('payment_method') == 'bank_transfer' ? 'selected' : '' ?>>Bank Transfer</option>
                        <option value="other" <?= old('payment_method') == 'other' ? 'selected' : '' ?>>Other</option>
                    </select>
                    <?php if (session('errors.payment_method')): ?>
                        <div class="invalid-feedback">
                            <?= session('errors.payment_method') ?>
                        </div>
                    <?php endif; ?>
                </div>

                <div class="col-md-6">
                    <!-- Empty column for layout balance -->
                </div>
            </div>

            <div class="mb-3">
                <label for="notes" class="form-label">Notes</label>
                <textarea class="form-control <?= session('errors.notes') ? 'is-invalid' : '' ?>" id="notes" name="notes" rows="3"><?= old('notes') ?></textarea>
                <?php if (session('errors.notes')): ?>
                    <div class="invalid-feedback">
                        <?= session('errors.notes') ?>
                    </div>
                <?php endif; ?>
            </div>

            <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                <button type="reset" class="btn btn-secondary">Reset</button>
                <button type="submit" class="btn btn-primary">Record Payment</button>
            </div>
        </form>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
    $(document).ready(function() {
        var commitmentData = {}; // Store commitment data
        var selectedMonths = []; // Store selected months

        // When member is selected, load their commitments
        $('#member_id').change(function() {
            var memberId = $(this).val();
            if (memberId) {
                // Clear current options
                $('#commitment_id').html('<option value="">Select Commitment</option>');

                // Reset UI elements
                $('#payment-period-container').hide();
                $('#months-container').hide();
                $('#amount-description').text('');
                $('#commitment-message').html('');
                $('#commitment_id').prop('disabled', false);

                // Load commitments via AJAX
                $.ajax({
                    url: '<?= site_url('api/member-commitments') ?>/' + memberId,
                    type: 'GET',
                    dataType: 'json',
                    success: function(data) {
                        commitmentData = {}; // Reset commitment data

                        if (data.length > 0) {
                            $.each(data, function(key, value) {
                                // Store commitment data for later use
                                commitmentData[value.commitment_id] = value;

                                var option = '<option value="' + value.commitment_id + '">';
                                option += value.amount + ' (' + value.frequency + ')';
                                if (value.start_date) {
                                    option += ' from ' + new Date(value.start_date).toLocaleDateString();
                                }
                                if (value.end_date) {
                                    option += ' to ' + new Date(value.end_date).toLocaleDateString();
                                }
                                option += '</option>';
                                $('#commitment_id').append(option);
                            });
                        } else {
                            $('#commitment_id').append('<option value="">No unpaid commitments found</option>');
                            // Disable the commitment dropdown since there are no unpaid commitments
                            $('#commitment_id').prop('disabled', true);
                            // Show a message to the user
                            $('#commitment-message').html('<div class="alert alert-info mt-2">All commitments for this member have been fully paid.</div>');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error("AJAX Error:", status, error);
                        $('#commitment_id').append('<option value="">Error loading commitments</option>');
                    }
                });
            }
        });

        // When commitment is selected, show appropriate payment options
        $('#commitment_id').change(function() {
            var commitmentId = $(this).val();

            // Reset UI elements
            $('#payment-period-container').hide();
            $('#months-container').hide();
            $('#amount-description').text('');
            $('#commitment-message').html('');
            $('#amount').val('');
            selectedMonths = [];

            if (commitmentId && commitmentData[commitmentId]) {
                var commitment = commitmentData[commitmentId];

                // Set default amount
                $('#amount').val(commitment.amount);

                // For monthly commitments, show month selection
                if (commitment.frequency === 'monthly') {
                    if (commitment.months && commitment.months.length > 0) {
                        // Show payment period options (1 month, 3 months, 6 months, all)
                        var periodOptions = '<div class="btn-group" role="group" aria-label="Payment Period">';
                        periodOptions += '<button type="button" class="btn btn-outline-primary period-btn" data-months="1">1 Month</button>';

                        if (commitment.months.length >= 3) {
                            periodOptions += '<button type="button" class="btn btn-outline-primary period-btn" data-months="3">3 Months</button>';
                        }

                        if (commitment.months.length >= 6) {
                            periodOptions += '<button type="button" class="btn btn-outline-primary period-btn" data-months="6">6 Months</button>';
                        }

                        periodOptions += '<button type="button" class="btn btn-outline-primary period-btn" data-months="all">All (' + commitment.months.length + ' Months)</button>';
                        periodOptions += '</div>';

                        $('#payment-period-options').html(periodOptions);
                        $('#payment-period-container').show();

                        // Generate month checkboxes
                        var monthsHtml = '';
                        $.each(commitment.months, function(index, month) {
                            monthsHtml += '<div class="col-md-4 mb-2">';
                            monthsHtml += '<div class="form-check">';
                            monthsHtml += '<input class="form-check-input month-checkbox" type="checkbox" value="' + month.value + '" id="month-' + month.value + '">';
                            monthsHtml += '<label class="form-check-label" for="month-' + month.value + '">' + month.label + '</label>';
                            monthsHtml += '</div>';
                            monthsHtml += '</div>';
                        });

                        $('#months-checkboxes').html(monthsHtml);
                        $('#months-container').show();
                    } else {
                        // No unpaid months available
                        $('#amount').val(commitment.amount);
                        $('#amount-description').text('All months for this commitment have been paid');
                        $('#commitment-message').html('<div class="alert alert-info mt-2">All months for this commitment have been paid. You can still record an additional payment if needed.</div>');

                        // Create a hidden field to indicate this is an additional payment
                        var additionalPaymentField = '<input type="hidden" name="is_additional_payment" value="1">';
                        $('#commitment-message').append(additionalPaymentField);
                    }

                    // Handle period button clicks
                    $('.period-btn').click(function() {
                        $('.period-btn').removeClass('active');
                        $(this).addClass('active');

                        var months = $(this).data('months');

                        // Uncheck all checkboxes
                        $('.month-checkbox').prop('checked', false);
                        selectedMonths = [];

                        if (months === 'all') {
                            // Check all checkboxes
                            $('.month-checkbox').prop('checked', true);

                            // Add all months to selectedMonths
                            $('.month-checkbox').each(function() {
                                selectedMonths.push($(this).val());
                            });

                            // Update amount
                            $('#amount').val((commitment.amount * commitment.months.length).toFixed(2));
                            $('#amount-description').text('Payment for all ' + commitment.months.length + ' months');
                        } else {
                            // Check the first n checkboxes
                            $('.month-checkbox').slice(0, months).prop('checked', true);

                            // Add selected months to selectedMonths
                            $('.month-checkbox:checked').each(function() {
                                selectedMonths.push($(this).val());
                            });

                            // Update amount
                            $('#amount').val((commitment.amount * months).toFixed(2));
                            $('#amount-description').text('Payment for ' + months + ' month' + (months > 1 ? 's' : ''));
                        }

                        // Update hidden input
                        $('#selected-months-input').val(JSON.stringify(selectedMonths));
                    });

                    // Handle individual month checkbox changes
                    $(document).on('change', '.month-checkbox', function() {
                        // Reset period buttons
                        $('.period-btn').removeClass('active');

                        // Update selectedMonths
                        selectedMonths = [];
                        $('.month-checkbox:checked').each(function() {
                            selectedMonths.push($(this).val());
                        });

                        // Update amount
                        var numMonths = selectedMonths.length;
                        $('#amount').val((commitment.amount * numMonths).toFixed(2));
                        $('#amount-description').text('Payment for ' + numMonths + ' month' + (numMonths > 1 ? 's' : ''));

                        // Update hidden input
                        $('#selected-months-input').val(JSON.stringify(selectedMonths));
                    });

                    // Trigger click on 1 month button by default
                    $('.period-btn[data-months="1"]').click();
                } else {
                    // For non-monthly commitments, just set the amount
                    $('#amount').val(commitment.amount);

                    if (commitment.frequency === 'one-time') {
                        $('#amount-description').text('One-time payment');
                    } else {
                        $('#amount-description').text('Payment for 1 ' + commitment.period_type);
                    }
                }
            }
        });

        // Function to load commitments with a specific commitment included
        function loadCommitmentsWithSpecific(memberId, commitmentId) {
            console.log('loadCommitmentsWithSpecific called with:', memberId, commitmentId);

            // Clear current options
            $('#commitment_id').html('<option value="">Select Commitment</option>');

            // Reset UI elements
            $('#payment-period-container').hide();
            $('#months-container').hide();
            $('#amount-description').text('');
            $('#commitment-message').html('');
            $('#commitment_id').prop('disabled', false);

            var ajaxUrl = '<?= site_url('api/member-commitments') ?>/' + memberId + '?include_commitment=' + commitmentId;
            console.log('Making AJAX request to:', ajaxUrl);

            // Load commitments via AJAX with specific commitment ID
            $.ajax({
                url: ajaxUrl,
                type: 'GET',
                dataType: 'json',
                success: function(data) {
                    console.log('AJAX success, received data:', data);
                    commitmentData = {}; // Reset commitment data

                    if (data.length > 0) {
                        $.each(data, function(key, value) {
                            console.log('Processing commitment:', value);
                            // Store commitment data for later use
                            commitmentData[value.commitment_id] = value;

                            var option = '<option value="' + value.commitment_id + '">';
                            option += value.amount + ' (' + value.frequency + ')';
                            if (value.start_date) {
                                option += ' from ' + new Date(value.start_date).toLocaleDateString();
                            }
                            if (value.end_date) {
                                option += ' to ' + new Date(value.end_date).toLocaleDateString();
                            }
                            option += '</option>';
                            $('#commitment_id').append(option);
                        });

                        console.log('Auto-selecting commitment:', commitmentId);
                        // Auto-select the specific commitment
                        $('#commitment_id').val(commitmentId).trigger('change');
                        console.log('Commitment dropdown value after selection:', $('#commitment_id').val());
                    } else {
                        console.log('No commitments found');
                        $('#commitment_id').append('<option value="">No commitments found</option>');
                        $('#commitment_id').prop('disabled', true);
                        $('#commitment-message').html('<div class="alert alert-info mt-2">No commitments found for this member.</div>');
                    }
                },
                error: function(xhr, status, error) {
                    console.error("AJAX Error:", status, error);
                    console.error("Response text:", xhr.responseText);
                    $('#commitment_id').append('<option value="">Error loading commitments</option>');
                }
            });
        }

        // Function to initialize auto-selection
        function initializeAutoSelection() {
            console.log('Initializing auto-selection...');
            console.log('Member dropdown exists:', $('#member_id').length > 0);
            console.log('Member dropdown options count:', $('#member_id option').length);
            console.log('Member dropdown value:', $('#member_id').val());

            <?php if (isset($selectedCommitmentId)): ?>
            console.log('Selected commitment ID from URL:', <?= $selectedCommitmentId ?>);
            <?php endif; ?>

            // Check if member dropdown is populated and has a selected value
            var memberId = $('#member_id').val();
            var memberOptions = $('#member_id option').length;

            if (memberOptions <= 1) {
                console.log('Member dropdown not yet populated, will retry...');
                return false; // Not ready yet
            }

            if (memberId) {
                console.log('Member is pre-selected:', memberId);

                <?php if (isset($selectedCommitmentId)): ?>
                // If a commitment ID was passed in the URL, we need to load commitments and then select it
                var selectedCommitmentId = <?= $selectedCommitmentId ?>;
                console.log('Loading commitments with specific commitment ID:', selectedCommitmentId);

                // Load commitments with the specific commitment ID included
                loadCommitmentsWithSpecific(memberId, selectedCommitmentId);
                return true; // Successfully initialized
                <?php else: ?>
                // No specific commitment, just load normally
                console.log('No specific commitment, loading all commitments');
                $('#member_id').trigger('change');
                return true; // Successfully initialized
                <?php endif; ?>
            } else {
                console.log('No member pre-selected');
                return true; // No auto-selection needed
            }
        }

        // Try to initialize with retries
        var initRetries = 0;
        var maxRetries = 10;

        function tryInitialize() {
            initRetries++;
            console.log('Attempt', initRetries, 'to initialize auto-selection...');

            if (initializeAutoSelection()) {
                console.log('Auto-selection initialized successfully');
                return;
            }

            if (initRetries < maxRetries) {
                setTimeout(tryInitialize, 100);
            } else {
                console.log('Failed to initialize auto-selection after', maxRetries, 'attempts');
            }
        }

        // Start the initialization process
        tryInitialize();

        // Form submission validation
        $('#paymentForm').submit(function(e) {
            var commitmentId = $('#commitment_id').val();

            if (commitmentId && commitmentData[commitmentId] && commitmentData[commitmentId].frequency === 'monthly') {
                if (selectedMonths.length === 0) {
                    alert('Please select at least one month to pay for.');
                    e.preventDefault();
                    return false;
                }
            }

            return true;
        });
    });
</script>
<?= $this->endSection() ?>
