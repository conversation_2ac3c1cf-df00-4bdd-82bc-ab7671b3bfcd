<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Payment History</h1>
    <a href="<?= site_url('reports') ?>" class="btn btn-secondary">
        <i class="fas fa-arrow-left"></i> Back to Dashboard
    </a>
</div>

<div class="card mb-4">
    <div class="card-header bg-primary text-white">
        <h5 class="card-title mb-0">Filter Options</h5>
    </div>
    <div class="card-body">
        <form action="<?= site_url('reports/payment-history') ?>" method="get" class="row g-3">
            <div class="col-md-3">
                <label for="start_date" class="form-label">Start Date</label>
                <input type="date" class="form-control" id="start_date" name="start_date" value="<?= $startDate ?>">
            </div>
            <div class="col-md-3">
                <label for="end_date" class="form-label">End Date</label>
                <input type="date" class="form-control" id="end_date" name="end_date" value="<?= $endDate ?>">
            </div>
            <div class="col-md-4">
                <label for="member_id" class="form-label">Member</label>
                <select class="form-select" id="member_id" name="member_id">
                    <option value="">All Members</option>
                    <?php foreach ($members as $member): ?>
                        <option value="<?= $member['member_id'] ?>" <?= $memberId == $member['member_id'] ? 'selected' : '' ?>>
                            <?= $member['name'] ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-2 d-flex align-items-end">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-filter"></i> Apply Filters
                </button>
            </div>
        </form>
    </div>
</div>

<div class="card">
    <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
        <h5 class="card-title mb-0">
            Payment History
            (<?= date('d M Y', strtotime($startDate)) ?> to <?= date('d M Y', strtotime($endDate)) ?>)
            <?php if ($memberId): ?>
                <?php foreach ($members as $member): ?>
                    <?php if ($member['member_id'] == $memberId): ?>
                        - <?= $member['name'] ?>
                    <?php endif; ?>
                <?php endforeach; ?>
            <?php endif; ?>
        </h5>
        <div>
            <button onclick="window.print()" class="btn btn-sm btn-light">
                <i class="fas fa-print"></i> Print Report
            </button>
            <a href="<?= site_url('reports/export-payment-history?' . http_build_query($_GET)) ?>" class="btn btn-sm btn-light">
                <i class="fas fa-file-excel"></i> Export to Excel
            </a>
        </div>
    </div>
    <div class="card-body">
        <?php if (empty($payments)): ?>
            <div class="alert alert-info">
                No payments found for the selected period.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>ID</th>
                            <th>Receipt #</th>
                            <th>Member</th>
                            <th>Amount</th>
                            <th>Date</th>
                            <th>Method</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($payments as $payment): ?>
                            <tr>
                                <td><?= $payment['payment_id'] ?></td>
                                <td><?= $payment['receipt_number'] ?></td>
                                <td>
                                    <a href="<?= site_url('members/show/' . $payment['member_id']) ?>">
                                        <?= $payment['member_name'] ?>
                                    </a>
                                </td>
                                <td><?= format_currency_with_decimals($payment['amount']) ?></td>
                                <td><?= date('d M Y', strtotime($payment['payment_date'])) ?></td>
                                <td><?= ucfirst(str_replace('_', ' ', $payment['payment_method'])) ?></td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="<?= site_url('payments/show/' . $payment['payment_id']) ?>" class="btn btn-sm btn-info" data-bs-toggle="tooltip" title="View">
                                            <i class="fas fa-eye"></i>
                                        </a>
                                        <a href="<?= site_url('payments/generate-receipt/' . $payment['payment_id']) ?>" class="btn btn-sm btn-success" data-bs-toggle="tooltip" title="Receipt">
                                            <i class="fas fa-file-invoice"></i>
                                        </a>
                                    </div>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-dark">
                            <th colspan="3">TOTAL</th>
                            <th><?= format_currency_with_decimals($totalAmount) ?></th>
                            <th colspan="3"></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<style type="text/css" media="print">
    @media print {
        .sidebar, .navbar, .card-header button, .card-header a, .btn, .dataTables_filter, .dataTables_length, .dataTables_paginate, .dataTables_info {
            display: none !important;
        }
        .content {
            margin-left: 0 !important;
            padding: 0 !important;
        }
        .card {
            border: none !important;
        }
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }
    }
</style>

<?= $this->endSection() ?>
