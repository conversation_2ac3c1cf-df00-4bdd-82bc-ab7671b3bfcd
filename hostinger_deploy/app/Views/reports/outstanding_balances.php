<?= $this->extend('layout/main') ?>

<?= $this->section('content') ?>

<div class="d-flex justify-content-between align-items-center mb-4">
    <h1>Outstanding Balances</h1>
    <div>
        <button onclick="window.print()" class="btn btn-primary">
            <i class="fas fa-print"></i> Print Report
        </button>
        <a href="<?= site_url('reports') ?>" class="btn btn-secondary">
            <i class="fas fa-arrow-left"></i> Back to Dashboard
        </a>
    </div>
</div>

<div class="card">
    <div class="card-header bg-danger text-white">
        <h5 class="card-title mb-0">Members with Outstanding Balances</h5>
    </div>
    <div class="card-body">
        <?php if (empty($members)): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> Great! No members have outstanding balances.
            </div>
        <?php else: ?>
            <div class="table-responsive">
                <table class="table table-striped table-hover datatable">
                    <thead>
                        <tr>
                            <th>Member</th>
                            <th class="text-center">Contact</th>
                            <th>Total Committed</th>
                            <th>Total Paid</th>
                            <th>Balance</th>
                            <th>Last Payment</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php
                        $totalCommitted = 0;
                        $totalPaid = 0;
                        $totalBalance = 0;
                        ?>

                        <?php foreach ($members as $member): ?>
                            <?php
                            $totalCommitted += $member['total_committed'];
                            $totalPaid += $member['total_paid'];
                            $totalBalance += $member['balance'];
                            ?>
                            <tr>
                                <td>
                                    <a href="<?= site_url('members/show/' . $member['member_id']) ?>">
                                        <?= $member['member_name'] ?>
                                    </a>
                                </td>
                                <td class="text-center">
                                    <div class="d-flex justify-content-center align-items-center gap-4">
                                        <?php if (!empty($member['whatsapp_number'])): ?>
                                            <a href="https://wa.me/<?= preg_replace('/[^0-9]/', '', $member['whatsapp_number']) ?>?text=Hello%20<?= urlencode($member['member_name']) ?>,%20this%20is%20a%20reminder%20about%20your%20outstanding%20balance%20of%20<?= urlencode(format_currency($member['balance'])) ?>." target="_blank" class="text-decoration-none" data-bs-toggle="tooltip" title="WhatsApp">
                                                <i class="fab fa-whatsapp text-success fa-lg"></i>
                                            </a>
                                        <?php endif; ?>

                                        <?php if (!empty($member['phone'])): ?>
                                            <a href="tel:<?= $member['phone'] ?>" class="text-decoration-none" data-bs-toggle="tooltip" title="Call">
                                                <i class="fas fa-phone-alt text-success fa-lg"></i>
                                            </a>
                                        <?php endif; ?>

                                        <?php if (empty($member['phone']) && empty($member['whatsapp_number'])): ?>
                                            <span class="text-muted">—</span>
                                        <?php endif; ?>
                                    </div>
                                </td>
                                <td><?= format_currency($member['total_committed']) ?></td>
                                <td><?= format_currency($member['total_paid']) ?></td>
                                <td class="text-danger fw-bold">
                                    <?= format_currency($member['balance']) ?>
                                </td>
                                <td>
                                    <?php if ($member['last_payment_date']): ?>
                                        <?= date('d M Y', strtotime($member['last_payment_date'])) ?>
                                        <br>
                                        <small class="text-muted">
                                            Amount: <?= format_currency($member['last_payment_amount']) ?>
                                        </small>
                                    <?php else: ?>
                                        <span class="text-muted">No payments yet</span>
                                    <?php endif; ?>
                                </td>

                            </tr>
                        <?php endforeach; ?>
                    </tbody>
                    <tfoot>
                        <tr class="table-dark">
                            <th colspan="4" class="text-end">TOTAL BALANCE:</th>
                            <th><?= format_currency($totalBalance) ?></th>
                            <th></th>
                        </tr>
                    </tfoot>
                </table>
            </div>
        <?php endif; ?>
    </div>
</div>

<style type="text/css" media="print">
    @media print {
        .sidebar, .navbar, .card-header button, .card-header a, .btn, .dataTables_filter, .dataTables_length, .dataTables_paginate, .dataTables_info {
            display: none !important;
        }
        .content {
            margin-left: 0 !important;
            padding: 0 !important;
        }
        .card {
            border: none !important;
        }
        .card-header {
            background-color: #f8f9fa !important;
            color: #000 !important;
        }
    }
</style>

<?= $this->endSection() ?>
