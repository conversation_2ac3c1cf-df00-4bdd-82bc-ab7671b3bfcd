<?php

namespace App\Models;

use CodeIgniter\Model;

class CommitmentModel extends Model
{
    protected $table = 'commitments';
    protected $primaryKey = 'commitment_id';

    protected $useAutoIncrement = true;
    protected $returnType = 'array';

    protected $allowedFields = [
        'member_id',
        'amount',
        'frequency',
        'start_date',
        'end_date',
        'notes'
    ];

    protected $useTimestamps = true;
    protected $createdField = 'created_at';
    protected $updatedField = 'updated_at';

    protected $validationRules = [
        'member_id' => 'required|integer',
        'amount' => 'required|numeric',
        'frequency' => 'required|in_list[monthly,one-time,quarterly,yearly]',
        'start_date' => 'required|valid_date',
        'end_date' => 'permit_empty|valid_date'
    ];

    protected $validationMessages = [
        'member_id' => [
            'required' => 'Member ID is required',
            'integer' => 'Member ID must be an integer'
        ],
        'amount' => [
            'required' => 'Amount is required',
            'numeric' => 'Amount must be a number'
        ],
        'frequency' => [
            'required' => 'Frequency is required',
            'in_list' => 'Frequency must be one of: monthly, one-time'
        ],
        'start_date' => [
            'required' => 'Start date is required',
            'valid_date' => 'Start date must be a valid date'
        ],
        'end_date' => [
            'valid_date' => 'End date must be a valid date'
        ]
    ];

    protected $skipValidation = false;

    /**
     * Get active commitments for a member
     *
     * @param int $memberId
     * @return array
     */
    public function getActiveCommitments($memberId)
    {
        $today = date('Y-m-d');

        $db = \Config\Database::connect();
        $builder = $db->table('commitments');
        $builder->where('member_id', $memberId);
        $builder->where('start_date <=', $today);
        $builder->groupStart();
        $builder->where('end_date IS NULL');
        $builder->orWhere('end_date >=', $today);
        $builder->groupEnd();

        $query = $builder->get();
        $result = $query->getResultArray();

        log_message('debug', "Active commitments query found " . count($result) . " commitments");

        return $result;
    }

    /**
     * Calculate total commitment amount for a member
     * This takes into account the frequency and duration of each commitment
     *
     * @param int $memberId
     * @return float
     */
    public function calculateTotalCommitment($memberId)
    {
        $commitments = $this->where('member_id', $memberId)->findAll();
        $totalAmount = 0;

        foreach ($commitments as $commitment) {
            $amount = $commitment['amount'];
            $frequency = $commitment['frequency'];
            $startDate = new \DateTime($commitment['start_date']);
            $endDate = !empty($commitment['end_date']) ? new \DateTime($commitment['end_date']) : null;

            // For one-time commitments, just add the amount
            if ($frequency == 'one-time') {
                $totalAmount += $amount;
                continue;
            }

            // For ongoing commitments (no end date), calculate for 1 year from start date or today
            if ($endDate === null) {
                $endDate = new \DateTime($startDate->format('Y-m-d'));
                $endDate->modify('+1 year');
            }

            // Calculate the number of periods based on frequency
            $interval = $startDate->diff($endDate);
            $totalMonths = ($interval->y * 12) + $interval->m + ($interval->d > 0 ? 1 : 0);

            switch ($frequency) {
                case 'monthly':
                    $periods = $totalMonths;
                    break;
                // Keep support for legacy quarterly and yearly commitments
                case 'quarterly':
                    $periods = ceil($totalMonths / 3);
                    break;
                case 'yearly':
                    $periods = ceil($totalMonths / 12);
                    break;
                default:
                    $periods = 1;
            }

            $totalAmount += $amount * $periods;
        }

        return $totalAmount;
    }

    /**
     * Get commitments with member details
     *
     * @return array
     */
    public function getCommitmentsWithMemberDetails()
    {
        $db = \Config\Database::connect();

        $query = $db->table('commitments c')
            ->select('c.*, m.name as member_name')
            ->join('members m', 'm.member_id = c.member_id')
            ->orderBy('c.created_at', 'DESC')
            ->get();

        return $query->getResultArray();
    }

    /**
     * Check if there are any overlapping commitments for a member
     *
     * @param int $memberId
     * @param string $startDate
     * @param string $endDate
     * @param int $excludeCommitmentId Optional commitment ID to exclude from the check (for updates)
     * @return bool True if there are overlapping commitments, false otherwise
     */
    public function hasOverlappingCommitments($memberId, $startDate, $endDate = null, $excludeCommitmentId = null)
    {
        $db = \Config\Database::connect();
        $builder = $db->table('commitments')
            ->where('member_id', $memberId);

        if ($excludeCommitmentId) {
            $builder->where('commitment_id !=', $excludeCommitmentId);
        }

        // If no end date is provided, we consider it as an ongoing commitment
        if ($endDate === null || $endDate === '') {
            // Check for any commitment that starts before or on our start date and has no end date
            $query1 = clone $builder;
            $overlapping1 = $query1->where('start_date <=', $startDate)
                ->where('end_date IS NULL')
                ->countAllResults() > 0;

            // Check for any commitment that starts after our start date and has no end date
            $query2 = clone $builder;
            $overlapping2 = $query2->where('start_date >', $startDate)
                ->where('end_date IS NULL')
                ->countAllResults() > 0;

            // Check for any commitment that starts before our start date and ends after our start date
            $query3 = clone $builder;
            $overlapping3 = $query3->where('start_date <=', $startDate)
                ->where('end_date >=', $startDate)
                ->countAllResults() > 0;

            return $overlapping1 || $overlapping2 || $overlapping3;
        } else {
            // Check for any commitment that overlaps with our date range
            // Case 1: Commitment starts before our start date and ends after our start date
            $query1 = clone $builder;
            $overlapping1 = $query1->where('start_date <=', $startDate)
                ->groupStart()
                    ->where('end_date IS NULL')
                    ->orWhere('end_date >=', $startDate)
                ->groupEnd()
                ->countAllResults() > 0;

            // Case 2: Commitment starts between our start and end dates
            $query2 = clone $builder;
            $overlapping2 = $query2->where('start_date >=', $startDate)
                ->where('start_date <=', $endDate)
                ->countAllResults() > 0;

            // Case 3: Commitment ends between our start and end dates
            $query3 = clone $builder;
            $overlapping3 = $query3->where('end_date >=', $startDate)
                ->where('end_date <=', $endDate)
                ->countAllResults() > 0;

            // Case 4: Commitment starts before our start date and ends after our end date
            $query4 = clone $builder;
            $overlapping4 = $query4->where('start_date <=', $startDate)
                ->where('end_date >=', $endDate)
                ->countAllResults() > 0;

            return $overlapping1 || $overlapping2 || $overlapping3 || $overlapping4;
        }
    }

    /**
     * Calculate the total amount due and total amount paid for a commitment
     *
     * @param int $commitmentId
     * @return array An array with 'totalDue', 'totalPaid', and 'balance' keys
     */
    public function calculateCommitmentBalance($commitmentId)
    {
        $commitment = $this->find($commitmentId);
        $result = [
            'totalDue' => 0,
            'totalPaid' => 0,
            'balance' => 0
        ];

        if (!$commitment) {
            return $result;
        }

        // Get the total amount due for this commitment
        $amount = $commitment['amount'];
        $frequency = $commitment['frequency'];
        $startDate = new \DateTime($commitment['start_date']);
        $endDate = !empty($commitment['end_date']) ? new \DateTime($commitment['end_date']) : null;

        // For one-time commitments, just use the amount
        if ($frequency == 'one-time') {
            $result['totalDue'] = $amount;
        } else {
            // For ongoing commitments (no end date), calculate up to today
            if ($endDate === null) {
                $endDate = new \DateTime(); // Use today's date
            }

            // Calculate the number of periods based on frequency
            $interval = $startDate->diff($endDate);
            $totalMonths = ($interval->y * 12) + $interval->m + ($interval->d > 0 ? 1 : 0);

            switch ($frequency) {
                case 'monthly':
                    $periods = $totalMonths;
                    break;
                // Keep support for legacy quarterly and yearly commitments
                case 'quarterly':
                    $periods = ceil($totalMonths / 3);
                    break;
                case 'yearly':
                    $periods = ceil($totalMonths / 12);
                    break;
                default:
                    $periods = 1;
            }

            $result['totalDue'] = $amount * $periods;
        }

        // Get the total amount paid for this commitment
        $db = \Config\Database::connect();
        $builder = $db->table('payments');
        $builder->selectSum('amount');
        $builder->where('commitment_id', $commitmentId);
        $query = $builder->get();

        if ($query && $row = $query->getRow()) {
            $result['totalPaid'] = (float)($row->amount ?? 0);
        }

        // Calculate the balance (amount still due)
        $result['balance'] = max(0, $result['totalDue'] - $result['totalPaid']);

        return $result;
    }

    /**
     * Check if a commitment has been fully paid
     *
     * @param int $commitmentId
     * @return bool True if the commitment has been fully paid, false otherwise
     */
    public function isFullyPaid($commitmentId)
    {
        $balance = $this->calculateCommitmentBalance($commitmentId);

        // Use a small epsilon value to account for floating point precision issues
        $epsilon = 0.01; // 1 cent tolerance
        $isFullyPaid = $balance['balance'] <= $epsilon;

        log_message('debug', "isFullyPaid: Commitment ID: {$commitmentId}, Total Due: {$balance['totalDue']}, Total Paid: {$balance['totalPaid']}, Balance: {$balance['balance']}, Is Fully Paid: " . ($isFullyPaid ? 'Yes' : 'No'));

        return $isFullyPaid;
    }

    /**
     * Get active unpaid commitments for a member
     *
     * @param int $memberId
     * @return array
     */
    public function getActiveUnpaidCommitments($memberId)
    {
        $commitments = $this->getActiveCommitments($memberId);
        $unpaidCommitments = [];

        log_message('debug', "Member ID: {$memberId}, Total Active Commitments: " . count($commitments));

        foreach ($commitments as $commitment) {
            $isFullyPaid = $this->isFullyPaid($commitment['commitment_id']);
            log_message('debug', "Commitment ID: {$commitment['commitment_id']}, Amount: {$commitment['amount']}, Frequency: {$commitment['frequency']}, Is Fully Paid: " . ($isFullyPaid ? 'Yes' : 'No'));

            if (!$isFullyPaid) {
                $unpaidCommitments[] = $commitment;
            }
        }

        log_message('debug', "Total Unpaid Commitments: " . count($unpaidCommitments));

        return $unpaidCommitments;
    }

    /**
     * Get all unpaid commitments for a member, regardless of active status
     *
     * @param int $memberId
     * @return array
     */
    public function getAllUnpaidCommitments($memberId)
    {
        $commitments = $this->where('member_id', $memberId)->findAll();
        $unpaidCommitments = [];

        log_message('debug', "getAllUnpaidCommitments: Member ID: {$memberId}, Total Commitments: " . count($commitments));

        foreach ($commitments as $commitment) {
            // Check if the commitment is fully paid
            $isFullyPaid = $this->isFullyPaid($commitment['commitment_id']);

            log_message('debug', "getAllUnpaidCommitments: Commitment ID: {$commitment['commitment_id']}, Amount: {$commitment['amount']}, Frequency: {$commitment['frequency']}, Is Fully Paid: " . ($isFullyPaid ? 'Yes' : 'No'));

            if (!$isFullyPaid) {
                $unpaidCommitments[] = $commitment;
                log_message('debug', "getAllUnpaidCommitments: Added unpaid commitment: {$commitment['commitment_id']}");
            } else {
                log_message('debug', "getAllUnpaidCommitments: Skipped fully paid commitment: {$commitment['commitment_id']}");
            }
        }

        log_message('debug', "getAllUnpaidCommitments: Total Unpaid Commitments (all): " . count($unpaidCommitments));

        return $unpaidCommitments;
    }

    /**
     * Get overlapping commitments for a member
     *
     * @param int $memberId
     * @param string $startDate
     * @param string $endDate
     * @param int $excludeCommitmentId Optional commitment ID to exclude from the check (for updates)
     * @return array List of overlapping commitments
     */
    public function getOverlappingCommitments($memberId, $startDate, $endDate = null, $excludeCommitmentId = null)
    {
        $db = \Config\Database::connect();
        $builder = $db->table('commitments')
            ->where('member_id', $memberId);

        if ($excludeCommitmentId) {
            $builder->where('commitment_id !=', $excludeCommitmentId);
        }

        // If no end date is provided, we consider it as an ongoing commitment
        if ($endDate === null || $endDate === '') {
            $query = $db->table('commitments')
                ->where('member_id', $memberId)
                ->groupStart()
                    // Commitment starts before or on our start date and has no end date
                    ->groupStart()
                        ->where('start_date <=', $startDate)
                        ->where('end_date IS NULL')
                    ->groupEnd()
                    // OR commitment starts after our start date and has no end date
                    ->orGroupStart()
                        ->where('start_date >', $startDate)
                        ->where('end_date IS NULL')
                    ->groupEnd()
                    // OR commitment starts before our start date and ends after our start date
                    ->orGroupStart()
                        ->where('start_date <=', $startDate)
                        ->where('end_date >=', $startDate)
                    ->groupEnd()
                ->groupEnd();

            if ($excludeCommitmentId) {
                $query->where('commitment_id !=', $excludeCommitmentId);
            }

            return $query->get()->getResultArray();
        } else {
            $query = $db->table('commitments')
                ->where('member_id', $memberId)
                ->groupStart()
                    // Case 1: Commitment starts before our start date and ends after our start date
                    ->groupStart()
                        ->where('start_date <=', $startDate)
                        ->groupStart()
                            ->where('end_date IS NULL')
                            ->orWhere('end_date >=', $startDate)
                        ->groupEnd()
                    ->groupEnd()
                    // Case 2: Commitment starts between our start and end dates
                    ->orGroupStart()
                        ->where('start_date >=', $startDate)
                        ->where('start_date <=', $endDate)
                    ->groupEnd()
                    // Case 3: Commitment ends between our start and end dates
                    ->orGroupStart()
                        ->where('end_date >=', $startDate)
                        ->where('end_date <=', $endDate)
                    ->groupEnd()
                    // Case 4: Commitment starts before our start date and ends after our end date
                    ->orGroupStart()
                        ->where('start_date <=', $startDate)
                        ->where('end_date >=', $endDate)
                    ->groupEnd()
                ->groupEnd();

            if ($excludeCommitmentId) {
                $query->where('commitment_id !=', $excludeCommitmentId);
            }

            return $query->get()->getResultArray();
        }
    }
}
