<?php

/**
 * Currency Helper Functions
 * 
 * Provides consistent currency formatting throughout the application
 */

if (!function_exists('format_currency')) {
    /**
     * Format amount with Rupees symbol
     *
     * @param float|int $amount
     * @param int $decimals
     * @param bool $showSymbol
     * @return string
     */
    function format_currency($amount, $decimals = 0, $showSymbol = true)
    {
        $formatted = number_format($amount, $decimals);
        return $showSymbol ? '₹' . $formatted : $formatted;
    }
}

if (!function_exists('format_currency_with_decimals')) {
    /**
     * Format amount with Rupees symbol and 2 decimal places
     *
     * @param float|int $amount
     * @param bool $showSymbol
     * @return string
     */
    function format_currency_with_decimals($amount, $showSymbol = true)
    {
        return format_currency($amount, 2, $showSymbol);
    }
}

if (!function_exists('currency_symbol')) {
    /**
     * Get the currency symbol
     *
     * @return string
     */
    function currency_symbol()
    {
        return '₹';
    }
}

if (!function_exists('currency_name')) {
    /**
     * Get the currency name
     *
     * @param bool $plural
     * @return string
     */
    function currency_name($plural = false)
    {
        return $plural ? 'rupees' : 'rupee';
    }
}

if (!function_exists('amount_in_words')) {
    /**
     * Convert amount to words with currency name
     *
     * @param float|int $amount
     * @return string
     */
    function amount_in_words($amount)
    {
        $formatter = numfmt_create('en_US', \NumberFormatter::SPELLOUT);
        $words = $formatter->format($amount);
        $currency = $amount == 1 ? currency_name() : currency_name(true);
        return $words . ' ' . $currency;
    }
}
