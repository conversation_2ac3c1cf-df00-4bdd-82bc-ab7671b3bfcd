<?php
/**
 * Hostinger Deployment Diagnostic Script
 * Upload this file to public_html/ and visit: yourdomain.com/diagnostic.php
 */

echo "<h1>Halqa Accounting System - Deployment Diagnostic</h1>";
echo "<style>body{font-family:Arial;margin:20px;} .ok{color:green;} .error{color:red;} .warning{color:orange;}</style>";

// Check PHP Version
echo "<h2>PHP Environment</h2>";
echo "PHP Version: " . phpversion();
if (version_compare(phpversion(), '8.0.0', '>=')) {
    echo " <span class='ok'>✓ OK</span><br>";
} else {
    echo " <span class='error'>✗ ERROR: PHP 8.0+ required</span><br>";
}

// Check required extensions
$required_extensions = ['mysqli', 'mbstring', 'openssl', 'json', 'curl'];
echo "<h3>Required PHP Extensions</h3>";
foreach ($required_extensions as $ext) {
    echo "Extension {$ext}: ";
    if (extension_loaded($ext)) {
        echo "<span class='ok'>✓ Loaded</span><br>";
    } else {
        echo "<span class='error'>✗ Missing</span><br>";
    }
}

// Check file structure
echo "<h2>File Structure</h2>";
$required_files = [
    'index.php' => 'CodeIgniter entry point',
    '.htaccess' => 'URL rewriting rules',
    'app/Config/App.php' => 'Application configuration',
    'vendor/autoload.php' => 'Composer autoloader',
    '.env' => 'Environment configuration'
];

foreach ($required_files as $file => $description) {
    echo "{$description} ({$file}): ";
    if (file_exists($file)) {
        echo "<span class='ok'>✓ Found</span><br>";
    } else {
        echo "<span class='error'>✗ Missing</span><br>";
    }
}

// Check directory permissions
echo "<h2>Directory Permissions</h2>";
$directories = [
    'writable' => 'Writable directory',
    'writable/cache' => 'Cache directory',
    'writable/logs' => 'Logs directory',
    'writable/session' => 'Session directory'
];

foreach ($directories as $dir => $description) {
    echo "{$description} ({$dir}): ";
    if (is_dir($dir)) {
        if (is_writable($dir)) {
            echo "<span class='ok'>✓ Writable</span><br>";
        } else {
            echo "<span class='error'>✗ Not writable (set to 777)</span><br>";
        }
    } else {
        echo "<span class='error'>✗ Directory missing</span><br>";
    }
}

// Check environment file
echo "<h2>Environment Configuration</h2>";
if (file_exists('.env')) {
    echo "Environment file: <span class='ok'>✓ Found</span><br>";
    
    $env_content = file_get_contents('.env');
    if (strpos($env_content, 'CI_ENVIRONMENT = production') !== false) {
        echo "Environment mode: <span class='ok'>✓ Production</span><br>";
    } else {
        echo "Environment mode: <span class='warning'>⚠ Not set to production</span><br>";
    }
    
    if (strpos($env_content, 'YOUR_SECURE_PASSWORD_HERE') !== false) {
        echo "Database password: <span class='error'>✗ Still using placeholder</span><br>";
    } else {
        echo "Database password: <span class='ok'>✓ Configured</span><br>";
    }
} else {
    echo "Environment file: <span class='error'>✗ Missing (.env file)</span><br>";
}

echo "<h2>Next Steps</h2>";
echo "<p>If you see any errors above, fix them before proceeding:</p>";
echo "<ul>";
echo "<li><strong>Red errors</strong>: Must be fixed for the application to work</li>";
echo "<li><strong>Orange warnings</strong>: Should be addressed for optimal performance</li>";
echo "<li><strong>Green checks</strong>: Everything is working correctly</li>";
echo "</ul>";

echo "<p><strong>After fixing issues:</strong></p>";
echo "<ol>";
echo "<li>Delete this diagnostic.php file for security</li>";
echo "<li>Visit your main domain to test the application</li>";
echo "<li>Check the login page works</li>";
echo "</ol>";

echo "<hr>";
echo "<p><em>Generated: " . date('Y-m-d H:i:s') . "</em></p>";
?>
